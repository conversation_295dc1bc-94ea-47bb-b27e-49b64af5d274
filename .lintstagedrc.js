const path = require("path")

const buildEslintCommand = (filenames) =>
  `next lint --fix --file ${filenames
    .map((f) => path.relative(process.cwd(), f))
    .join(" --file ")}`

module.exports = {
  "*.{js,jsx,ts,tsx}": [
    (files) => {
      const filteredFiles = files.filter(
        (file) => !file.includes("node_modules/"),
      )
      return buildEslintCommand(filteredFiles)
    },
    "prettier --ignore-path .gitignore --write",
  ],
}
