/** @type {import('next').NextConfig} */
const nextConfig = {
  images: {
    unoptimized: true,
    remotePatterns: [
      {
        protocol: "https",
        hostname: "randomuser.me",
      },
      {
        protocol: "https",
        hostname: "utfs.io",
      },
      {
        protocol: "https",
        hostname: "images.unsplash.com",
      },
      {
        protocol: "https",
        hostname: "agency-assets.theportfolyo.com",
      },
      {
        protocol: "https",
        hostname: "assets.theportfolyo.com",
      },
      {
        protocol: "https",
        hostname: "gallery.theportfolio.in",
      },
    ],
  },
}

export default nextConfig
