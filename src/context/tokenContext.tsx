// TokenContext.tsx
import { useAuth } from "@clerk/nextjs" // Import your Clerk hook
import React, { createContext, useContext, useState, useEffect } from "react"

interface TokenContextType {
  token: string | null
}

interface Props {
  children: React.ReactNode
}
const TokenContext = createContext<TokenContextType | undefined>(undefined)

export const TokenProvider = ({ children }: Props) => {
  const { getToken } = useAuth()
  const [token, setToken] = useState<string | null>(null)

  useEffect(() => {
    const fetchToken = async () => {
      const fetchedToken = await getToken()
      setToken(fetchedToken)
    }

    fetchToken()
  }, [getToken])

  return (
    <TokenContext.Provider value={{ token }}>{children}</TokenContext.Provider>
  )
}

export const useToken = () => {
  const context = useContext(TokenContext)
  if (!context) {
    throw new Error("useToken must be used within a TokenProvider")
  }
  return context.token
}
