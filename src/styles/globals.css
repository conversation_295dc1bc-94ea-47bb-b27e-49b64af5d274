@tailwind base;
@tailwind components;
@tailwind utilities;

@layer base {
  :root {
    --background: 0 0% 99.2157%;
    --foreground: 0 0% 0%;
    --card: 0 0% 99.2157%;
    --card-foreground: 0 0% 0%;
    --popover: 0 0% 98.8235%;
    --popover-foreground: 0 0% 0%;
    --primary: 172.8671 69.7561% 59.8039%;
    --primary-foreground: 0 0% 0%;
    --secondary: 214.2857 24.1379% 94.3137%;
    --secondary-foreground: 0 0% 3.1373%;
    --muted: 0 0% 96.0784%;
    --muted-foreground: 0 0% 32.1569%;
    --accent: 171.8182 73.3333% 94.1176%;
    --accent-foreground: 172.7586 70.7317% 32.1569%;
    --destructive: 358.4416 74.7573% 59.6078%;
    --destructive-foreground: 0 0% 100%;
    --border: 240 17.0732% 91.9608%;
    --input: 0 0% 92.1569%;
    --ring: 172.8671 69.7561% 59.8039%;
    --chart-1: 172.8671 69.7561% 59.8039%;
    --chart-2: 148.0328 82.4324% 70.9804%;
    --chart-3: 24.8571 98.1308% 58.0392%;
    --chart-4: 217.0787 76.7241% 54.5098%;
    --chart-5: 40.8696 100% 72.9412%;
    --sidebar: 210 42.8571% 97.2549%;
    --sidebar-foreground: 0 0% 0%;
    --sidebar-primary: 0 0% 0%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 173.8462 76.4706% 90%;
    --sidebar-accent-foreground: 0 0% 0%;
    --sidebar-border: 0 0% 92.1569%;
    --sidebar-ring: 0 0% 0%;
    --font-sans: Plus Jakarta Sans, sans-serif;
    --font-serif: Lora, serif;
    --font-mono: IBM Plex Mono, monospace;
    --radius: 1.4rem;
    --shadow-2xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);
    --shadow-xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);
    --shadow-sm:
      0 1px 3px 0px hsl(0 0% 0% / 0.1), 0 1px 2px -1px hsl(0 0% 0% / 0.1);
    --shadow:
      0 1px 3px 0px hsl(0 0% 0% / 0.1), 0 1px 2px -1px hsl(0 0% 0% / 0.1);
    --shadow-md:
      0 1px 3px 0px hsl(0 0% 0% / 0.1), 0 2px 4px -1px hsl(0 0% 0% / 0.1);
    --shadow-lg:
      0 1px 3px 0px hsl(0 0% 0% / 0.1), 0 4px 6px -1px hsl(0 0% 0% / 0.1);
    --shadow-xl:
      0 1px 3px 0px hsl(0 0% 0% / 0.1), 0 8px 10px -1px hsl(0 0% 0% / 0.1);
    --shadow-2xl: 0 1px 3px 0px hsl(0 0% 0% / 0.25);
    --tracking-normal: 0em;
    --spacing: 0.27rem;
  }

  .dark {
    --background: 0 0% 0%;
    --foreground: 0 0% 94.1176%;
    --card: 0 0% 3.9216%;
    --card-foreground: 0 0% 94.1176%;
    --popover: 240 1.9608% 10%;
    --popover-foreground: 0 0% 94.1176%;
    --primary: 172.8671 69.7561% 59.8039%;
    --primary-foreground: 0 0% 0%;
    --secondary: 172.7586 43.9394% 25.8824%;
    --secondary-foreground: 0 0% 94.1176%;
    --muted: 240 2.7778% 14.1176%;
    --muted-foreground: 0 0% 58.8235%;
    --accent: 173.5135 60.6557% 11.9608%;
    --accent-foreground: 0 0% 99.6078%;
    --destructive: 0 94.7712% 70%;
    --destructive-foreground: 0 0% 100%;
    --border: 240 2.439% 8.0392%;
    --input: 0 0% 23.1373%;
    --ring: 172.8671 69.7561% 59.8039%;
    --chart-1: 172.8671 69.7561% 59.8039%;
    --chart-2: 142.0513 100% 77.0588%;
    --chart-3: 0 93.5484% 81.7647%;
    --chart-4: 217.5484 87.5706% 65.2941%;
    --chart-5: 40.8696 100% 72.9412%;
    --sidebar: 0 0% 3.9216%;
    --sidebar-foreground: 0 0% 94.1176%;
    --sidebar-primary: 172.8671 69.7561% 59.8039%;
    --sidebar-primary-foreground: 0 0% 100%;
    --sidebar-accent: 172 83.3333% 7.0588%;
    --sidebar-accent-foreground: 172.8671 69.7561% 59.8039%;
    --sidebar-border: 222.8571 6.422% 21.3725%;
    --sidebar-ring: 172.8671 69.7561% 59.8039%;
    --font-sans: Plus Jakarta Sans, sans-serif;
    --font-serif: Lora, serif;
    --font-mono: IBM Plex Mono, monospace;
    --radius: 1.4rem;
    --shadow-2xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);
    --shadow-xs: 0 1px 3px 0px hsl(0 0% 0% / 0.05);
    --shadow-sm:
      0 1px 3px 0px hsl(0 0% 0% / 0.1), 0 1px 2px -1px hsl(0 0% 0% / 0.1);
    --shadow:
      0 1px 3px 0px hsl(0 0% 0% / 0.1), 0 1px 2px -1px hsl(0 0% 0% / 0.1);
    --shadow-md:
      0 1px 3px 0px hsl(0 0% 0% / 0.1), 0 2px 4px -1px hsl(0 0% 0% / 0.1);
    --shadow-lg:
      0 1px 3px 0px hsl(0 0% 0% / 0.1), 0 4px 6px -1px hsl(0 0% 0% / 0.1);
    --shadow-xl:
      0 1px 3px 0px hsl(0 0% 0% / 0.1), 0 8px 10px -1px hsl(0 0% 0% / 0.1);
    --shadow-2xl: 0 1px 3px 0px hsl(0 0% 0% / 0.25);
  }

  body {
    letter-spacing: var(--tracking-normal);
  }
}

@layer base {
  * {
    @apply border-border;
  }
  body {
    @apply bg-background text-foreground;
  }
}

::selection {
  background-color: #51e0cf;
  color: #000;
}

/* In your global CSS file (e.g., styles.css) */
.hide-scrollbar {
  -ms-overflow-style: none;
  /* IE and Edge */
  scrollbar-width: none;
  /* Firefox */
}

.hide-scrollbar::-webkit-scrollbar {
  display: none;
}

@layer utilities {
  .custom-scrollbar::-webkit-scrollbar {
    width: 10px;
    height: 3px;
  }

  .custom-scrollbar::-webkit-scrollbar-thumb {
    background-color: theme("colors.neutral.300");
    border-radius: 10px;
  }

  .custom-scrollbar::-webkit-scrollbar-track {
    background: theme("colors.gray.200");
    border-radius: 10px;
  }

  .custom-scrollbar-black::-webkit-scrollbar {
    width: 10px;
    height: 3px;
    border-radius: 100%;
    overflow: hidden;
  }

  .custom-scrollbar-black::-webkit-scrollbar-thumb {
    background-color: theme("colors.neutral.900");
    border-radius: 10px;
  }

  .custom-scrollbar-black::-webkit-scrollbar-track {
    background: theme("colors.gray.200");
    border-radius: 10px;
  }
}

/* Global scrollbar styles */
*::-webkit-scrollbar {
  width: 6px;
  height: 6px;
}

*::-webkit-scrollbar-thumb {
  background-color: theme("colors.neutral.700");
  border-radius: var(--radius);
}

*::-webkit-scrollbar-track {
  background: hsl(var(--muted) / 0.8);
  border-radius: var(--radius);
}

img {
  user-select: none;
  pointer-events: none;
}

.header {
  --clip: inset(0 0 calc(100% - 48px + 8px) 0 round 16px);
  -webkit-user-select: none;
  -moz-user-select: none;
  user-select: none;
  isolation: isolate;
  overflow: hidden;
  margin-inline: auto;
  transform: translateX(calc(-1 * 5px / 2));
  transition: 0.24s var(--ease-out-quad);
  transition-property:
    background,
    -webkit-clip-path;
  transition-property: clip-path, background;
  transition-property:
    clip-path,
    background,
    -webkit-clip-path;
}

.headyer::before {
  content: "";
  position: absolute;
  pointer-events: none;
  inset: 0;
  border: 1px solid hsl(var(--border));
  border-radius: inherit;
  height: calc(64px - 16px);
  will-change: height;
  transition: inherit;
  transition-property: height;
}

.btn-primary {
  z-index: 20;
  /* background-image: linear-gradient(to right, hsl(var(--primary)), #9333ea); */
  color: #fff;
  text-align: center;
  background-image: radial-gradient(
    circle farthest-side at 30% 0,
    rgba(255, 255, 255, 0.12),
    transparent
  );
  box-shadow:
    inset 1px 1px 2px rgba(255, 255, 255, 0.24),
    0 1px 3px hsl(var(--primary) / 0.24),
    0 2px 6px hsl(var(--primary) / 0.24),
    0 4px 8px rgba(96, 10, 255, 0.12),
    0 16px 32px -8px hsl(var(--primary) / 0.48);
}

.btn-primary:hover {
  background-color: #7c3aed;
  color: #fff;
  transform: scale(1.05) translateY(-4px);
  box-shadow:
    inset 0 0 rgba(255, 255, 255, 0.24),
    0 1px 3px rgba(124, 58, 237, 0.24),
    0 2px 6px rgba(124, 58, 237, 0.24),
    0 4px 8px rgba(124, 58, 237, 0.12),
    0 20px 40px -8px rgba(124, 58, 237, 0.64);
}

.btn-primary:active {
  background-color: #7c3aed;
  transform: scale(1) translate(0);
  box-shadow:
    inset 0 0 rgba(255, 255, 255, 0.24),
    0 1px 3px rgba(124, 58, 237, 0.48),
    0 2px 6px rgba(124, 58, 237, 0.48),
    0 4px 8px rgba(124, 58, 237, 0.48),
    0 4px 12px -8px rgba(124, 58, 237, 1);
}

.btn-secondary {
  z-index: 20;
  background-color: hsl(var(--background) / 0.04);
  color: #fff;
  text-align: center;
  background-image: radial-gradient(
    circle farthest-side at 35% -50%,
    rgba(255, 255, 255, 0.08),
    rgba(255, 255, 255, 0)
  );
  box-shadow:
    0 8px 40px -20px rgba(255, 255, 255, 0.2),
    inset 1px 1px rgba(255, 255, 255, 0.08),
    inset 0 0 0 1px rgba(255, 255, 255, 0.06);
}

.btn-secondary:hover {
  background-color: hsl(var(--background) / 0.08);
  color: #fff;
  transform: scale(1.05) translateY(-4px);
  box-shadow:
    0 8px 40px -20px rgba(255, 255, 255, 0.32),
    inset 1px 1px rgba(255, 255, 255, 0.08),
    inset 0 0 0 1px rgba(255, 255, 255, 0.1);
}

.btn-secondary:active {
  background-color: hsl(var(--background) / 0.08);
  transform: scale(1) translateY(0);
  box-shadow:
    0 8px 40px -20px rgba(255, 255, 255, 0.32),
    inset 1px 1px rgba(255, 255, 255, 0.08),
    inset 0 0 0 1px rgba(255, 255, 255, 0.1);
}

.badge {
  box-shadow: 0 0 0 1px hsl(var(--primary));
}

.heading {
  @apply bg-gradient-to-b from-foreground to-foreground/60 bg-clip-text text-transparent;
}

.bento-card {
  width: 100%;
  height: 100%;
  display: flex;
  flex-direction: column;
  align-items: flex-start;
  padding: 1rem;
  border-radius: 0.75rem;
  position: relative;
  z-index: 50;
}

@media screen and (min-width: 768px) {
  .bento-card {
    padding: 1.5rem;
  }
}

.spotlight::after {
  content: "";
  height: 100%;
  width: 100%;
  position: absolute;
  inset: 0;
  z-index: 10;
  background: radial-gradient(
    200px circle at var(--mouse-x) var(--mouse-y),
    hsl(var(--foreground)),
    transparent
  );
  transition: background 0.3s ease;
}

.group:hover .spotlight::after {
  border-color: #fff;
}

:root {
  --content-background: #100f1b;
  --spot-light-size: 800px;
  --spot-light-color: rgba(139, 92, 246, 0.15);
  --card-border-color: rgba(255, 255, 255, 0.4);
}

.card {
  background-color: rgba(255, 255, 255, 0.1);
  height: 100%;
  width: 100%;
  position: relative;
}

.content {
  background: var(--content-background);
  height: calc(100% - 2px);
  width: calc(100% - 2px);
  transform: translate(1px, 1px);
  border-radius: inherit;
}

.card:before,
.card:after {
  content: "";
  position: absolute;
  inset: 0;
  transition: opacity 500ms cubic-bezier(0.075, 0.82, 0.165, 1);
  border-radius: inherit;
  opacity: 0;
}

/* spotlight */
.card:after {
  background: radial-gradient(
    var(--spot-light-size) circle at var(--pos-x) var(--pos-y),
    var(--spot-light-color),
    transparent 40%
  );
}

/* card's border */
.card:before {
  background: radial-gradient(
    calc(var(--spot-light-size) / 2) circle at var(--pos-x) var(--pos-y),
    var(--card-border-color),
    transparent 40%
  );
}

.card:hover:after,
.card:hover:before {
  opacity: 1;
}

.pricing {
  background: radial-gradient(
    ellipse 80% 50% at 50% -20%,
    rgba(0, 24, 51, 0.6),
    rgba(25, 8, 43, 0.3)
  );
}

.footer::before {
  background: radial-gradient(
    50% 56400% at 50% 100%,
    rgba(40, 34, 57, 0.2) 0%,
    rgba(169, 163, 194, 0) 100%
  );
  bottom: 0;
  content: "";
  height: 1px;
  left: 0;
  position: absolute;
  width: 100%;
  color: rgb(23, 23, 23);
}

.th {
  transition: height 0.3s ease;
}

.gradient {
  background: conic-gradient(
    from 230.29deg at 51.63% 52.16%,
    rgb(36, 0, 255) 0deg,
    rgb(0, 135, 255) 67.5deg,
    rgb(108, 39, 157) 198.75deg,
    rgb(24, 38, 163) 251.25deg,
    rgb(54, 103, 196) 301.88deg,
    rgb(105, 30, 255) 360deg
  );
}

@keyframes techPulse {
  0% {
    transform: scale(2);
    opacity: 0;
    box-shadow: 0px 0px 30px rgba(59, 130, 246, 0.3);
  }
  50% {
    transform: translate(0px, -5px) scale(1);
    opacity: 1;
    box-shadow: 0px 8px 20px rgba(59, 130, 246, 0.4);
  }
  100% {
    transform: translate(0px, 5px) scale(0.1);
    opacity: 0;
    box-shadow: 0px 10px 20px rgba(59, 130, 246, 0);
  }
}
