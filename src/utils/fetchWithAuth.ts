import { getToken } from "./gettoken"

const fetchWithAuth = async (
  url: string,
  options: RequestInit = {},
): Promise<any> => {
  try {
    const token = getToken() // Ensure you have a function to fetch your token
    if (!token) {
      throw new Error("Authorization token is missing")
    }

    const defaultHeaders = {
      Authorization: `Bear<PERSON> ${token}`,
      "Content-Type": "application/json", // Adjust if necessary for your API
    }

    const response = await fetch(url, {
      ...options,
      headers: {
        ...defaultHeaders,
        ...options.headers, // Merge additional headers if provided
      },
    })

    if (!response.ok) {
      const errorData = await response.json()
      throw new Error(
        `Error ${response.status}: ${errorData.message || "Unknown error"}`,
      )
    }

    return await response.json()
  } catch (error) {
    console.error("Error in fetchWithAuth", error)
    throw error // Re-throw to handle errors in calling code
  }
}
