import { Product } from "@/types/product"
import { getToken } from "@/utils/gettoken"

import { API_BASE_URL } from "."

export const fetchAllProducts = async () => {
  try {
    const res = await fetch(`${API_BASE_URL}/products`)
    const data = await res.json()
    return data
  } catch (error) {
    console.error("Error fetching products", error)
    return []
  }
}

//fetch prodcut by type
export const fetchProductsByType = async (type: string) => {
  console.log("type", type)
  if (type.endsWith(".js")) {
    return []
  }
  try {
    const res = await fetch(`${API_BASE_URL}/products?type=${type}`, {
      next: { revalidate: 60 },
    })
    const data = await res.json()
    return data
  } catch (error) {
    console.error("Error fetching products by type", error)
    return []
  }
}

export const fetchProductBySlug = async (slug: string) => {
  try {
    const res = await fetch(`${API_BASE_URL}/products/slug/${slug}`, {
      cache: "no-store",
    })
    const data = await res.json()
    return data
  } catch (error) {
    console.error("Error fetching product by ID", error)
    return null
  }
}

// fetch product by ID for admin
export const fetchProductByIdAdmin = async (id: string) => {
  try {
    const token = getToken()
    const res = await fetch(`${API_BASE_URL}/products/${id}`, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    })
    const data = await res.json()
    return data
  } catch (error) {
    console.error("Error fetching product by ID", error)
    return null
  }
}

// admin actions only

// fetch products with pagination
export const fetchProductsWithPagination = async (
  pageNo: number,
  pageSize: number,
) => {
  try {
    const token = getToken()
    const res = await fetch(
      `${API_BASE_URL}/products/admin/pagination?limit=${pageSize}&page=${pageNo}`,
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      },
    )
    const data = await res.json()
    return data
  } catch (error) {
    console.error("Error fetching products with pagination", error)
    return []
  }
}

// create product
export const createProduct = async (product: Product) => {
  try {
    const token = getToken()
    const res = await fetch(`${API_BASE_URL}/products`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify(product),
    })
    const data = await res.json()
    return data
  } catch (error) {
    console.error("Error creating product", error)
    return null
  }
}

// update product
export const updateProduct = async (product: Product) => {
  try {
    const token = getToken()
    const res = await fetch(`${API_BASE_URL}/products/${product._id}`, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify(product),
    })
    const data = await res.json()
    return data
  } catch (error) {
    console.error("Error updating product", error)
    return null
  }
}

// delete product
export const deleteProduct = async (id: string) => {
  try {
    const token = getToken()
    const res = await fetch(`${API_BASE_URL}/products/${id}`, {
      method: "DELETE",
      headers: {
        Authorization: `Bearer ${token}`,
      },
    })
    const data = await res.json()
    return data
  } catch (error) {
    console.error("Error deleting product", error)
    return null
  }
}
