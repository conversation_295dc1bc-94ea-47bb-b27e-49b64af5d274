import { Subscription } from "@/types/subscription"
import { getToken } from "@/utils/gettoken"

import { API_BASE_URL } from "."

//fetch all subscriptions
export const fetchAllSubscriptions = async () => {
  try {
    const token = getToken()
    const res = await fetch(`${API_BASE_URL}/subscriptions`, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    })
    const data = await res.json()
    return data
  } catch (error) {
    console.error("Error fetching subscriptions", error)
    return []
  }
}

//fetch subscriptions with pagination
export const fetchSubscriptionsWithPagination = async (
  page = 1,
  limit = 10,
) => {
  try {
    const token = getToken()
    const res = await fetch(
      `${API_BASE_URL}/subscriptions?page=${page}&limit=${limit}`,
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      },
    )
    const data = await res.json()
    return data
  } catch (error) {
    console.error("Error fetching subscriptions with pagination", error)
    return []
  }
}

//fetch subscription by id
export const fetchSubscriptionById = async (id: string) => {
  try {
    const token = getToken()
    const res = await fetch(`${API_BASE_URL}/subscriptions/${id}`, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    })
    const data = await res.json()
    return data
  } catch (error) {
    console.error("Error fetching subscription by ID", error)
    return null
  }
}

//create subscription
export const createSubscription = async (subscription: Subscription) => {
  try {
    const token = getToken()
    const res = await fetch(`${API_BASE_URL}/subscriptions`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify(subscription),
    })
    const data = await res.json()
    return data
  } catch (error) {
    console.error("Error creating subscription", error)
    return null
  }
}

//update subscription
export const updateSubscription = async (subscription: Subscription) => {
  try {
    const token = getToken()
    const res = await fetch(
      `${API_BASE_URL}/subscriptions/${subscription._id}`,
      {
        method: "PUT",
        headers: {
          "Content-Type": "application/json",
          Authorization: `Bearer ${token}`,
        },
        body: JSON.stringify(subscription),
      },
    )
    const data = await res.json()
    return data
  } catch (error) {
    console.error("Error updating subscription", error)
    return null
  }
}

//delete subscription
export const deleteSubscription = async (id: string) => {
  try {
    const token = getToken()
    const res = await fetch(`${API_BASE_URL}/subscriptions/${id}`, {
      method: "DELETE",
      headers: {
        Authorization: `Bearer ${token}`,
      },
    })
    const data = await res.json()
    return data
  } catch (error) {
    console.error("Error deleting subscription", error)
    return null
  }
}
