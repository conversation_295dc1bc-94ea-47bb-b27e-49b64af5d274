import { Plan } from "@/types/plan"
import { getToken } from "@/utils/gettoken"

import { API_BASE_URL } from "."
// import { GetToken } from "@clerk/types";

export const fetchPlans = async () => {
  const token = getToken()
  const headers = {
    Authorization: `Bearer ${token}`,
  }
  try {
    const res = await fetch(`${API_BASE_URL}/plans`, { headers })
    const data = await res.json()
    return data
  } catch (error) {
    console.error("Error fetching plans", error)
    return []
  }
}

//fetch plan with pagination
export const fetchPlansWithPagination = async (page: number, limit: number) => {
  const token = getToken()
  const headers = {
    Authorization: `Bearer ${token}`,
  }
  try {
    const res = await fetch(
      `${API_BASE_URL}/plans/pagination?page=${page}&limit=${limit}`,
      {
        headers,
      },
    )
    const data = await res.json()
    return data
  } catch (error) {
    console.error("Error fetching plans with pagination", error)
    return []
  }
}

//fetch plan by id
export const fetchPlanById = async (id: string) => {
  const token = getToken()
  const headers = {
    Authorization: `Bearer ${token}`,
  }
  try {
    const res = await fetch(`${API_BASE_URL}/plans/${id}`, { headers })
    const data = await res.json()
    return data
  } catch (error) {
    console.error("Error fetching plan by ID", error)
    return null
  }
}

//create plan
export const createPlan = async (plan: Plan) => {
  const token = getToken()
  const headers = {
    "Content-Type": "application/json",
    Authorization: `Bearer ${token}`,
  }
  try {
    const res = await fetch(`${API_BASE_URL}/plans`, {
      method: "POST",
      headers,
      body: JSON.stringify(plan),
    })
    const data = await res.json()
    return data
  } catch (error) {
    console.error("Error creating plan", error)
    return null
  }
}

//update plan
export const updatePlan = async (plan: Plan) => {
  const token = getToken()
  const headers = {
    "Content-Type": "application/json",
    Authorization: `Bearer ${token}`,
  }
  try {
    const res = await fetch(`${API_BASE_URL}/plans/${plan._id}`, {
      method: "PUT",
      headers,
      body: JSON.stringify(plan),
    })
    const data = await res.json()
    return data
  } catch (error) {
    console.error("Error updating plan", error)
    return null
  }
}

//delete plan
export const deletePlan = async (id: string) => {
  const token = getToken()
  const headers = {
    Authorization: `Bearer ${token}`,
  }
  try {
    const res = await fetch(`${API_BASE_URL}/plans/${id}`, {
      method: "DELETE",
      headers,
    })
    const data = await res.json()
    return data
  } catch (error) {
    console.error("Error deleting plan", error)
    return null
  }
}
