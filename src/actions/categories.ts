import { Category } from "@/types/category"
import { getToken } from "@/utils/gettoken"

import { API_BASE_URL } from "."

export const fetchCategories = async () => {
  const token = getToken()
  try {
    const res = await fetch(`${API_BASE_URL}/categories`, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    })
    const data = await res.json()
    return data
  } catch (error) {
    console.error("Error fetching categories", error)
    return []
  }
}

export const fetchCategoriesWithPagination = async (
  page: number,
  limit: number,
) => {
  const token = getToken()

  try {
    const res = await fetch(
      `${API_BASE_URL}/categories/pagination?page=${page}&limit=${limit}`,
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      },
    )
    const data = await res.json()
    return data
  } catch (error) {
    console.error("Error fetching categories with pagination", error)
    return []
  }
}

export const fetchCategoryById = async (id: string) => {
  const token = getToken()

  try {
    const res = await fetch(`${API_BASE_URL}/categories/${id}`, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    })
    const data = await res.json()
    return data
  } catch (error) {
    console.error("Error fetching category by ID", error)
    return null
  }
}

export const updateCategory = async (category: Category) => {
  const token = getToken()

  try {
    const res = await fetch(`${API_BASE_URL}/categories/${category._id}`, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify(category),
    })
    const data = await res.json()
    return data
  } catch (error) {
    console.error("Error updating category", error)
    return null
  }
}

export const createCategory = async (category: Category) => {
  const token = getToken()

  try {
    const res = await fetch(`${API_BASE_URL}/categories`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify(category),
    })
    const data = await res.json()
    return data
  } catch (error) {
    console.error("Error creating category", error)
    return null
  }
}

export const deleteCategory = async (id: string) => {
  const token = getToken()

  try {
    const res = await fetch(`${API_BASE_URL}/categories/${id}`, {
      method: "DELETE",
      headers: {
        Authorization: `Bearer ${token}`,
      },
    })
    const data = await res.json()
    return data
  } catch (error) {
    console.error("Error deleting category", error)
    return null
  }
}
