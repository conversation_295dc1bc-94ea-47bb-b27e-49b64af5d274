import { Coupon } from "@/types/coupon"
import { getToken } from "@/utils/gettoken"

import { API_BASE_URL } from "."

export const fetchAllCoupons = async () => {
  try {
    const token = getToken()
    const res = await fetch(`${API_BASE_URL}/coupons`, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    })
    const data = await res.json()
    return data
  } catch (error) {
    console.error("Error fetching coupons", error)
    return []
  }
}

export const fetchCouponsWithPagination = async (
  page: number,
  limit: number,
) => {
  try {
    const token = getToken()
    const res = await fetch(
      `${API_BASE_URL}/coupons/pagination?page=${page}&limit=${limit}`,
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      },
    )
    const data = await res.json()
    return data
  } catch (error) {
    console.error("Error fetching coupons with pagination", error)
    return []
  }
}

export const fetchCouponById = async (id: string) => {
  try {
    const token = getToken()
    const res = await fetch(`${API_BASE_URL}/coupons/${id}`, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    })
    const data = await res.json()
    return data
  } catch (error) {
    console.error("Error fetching coupon by ID", error)
    return null
  }
}

export const createCoupon = async (coupon: Coupon) => {
  try {
    const token = getToken()
    const res = await fetch(`${API_BASE_URL}/coupons`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify(coupon),
    })
    const data = await res.json()
    return data
  } catch (error) {
    console.error("Error creating coupon", error)
    return error
  }
}

export const updateCoupon = async (coupon: Coupon) => {
  try {
    const token = getToken()
    const res = await fetch(`${API_BASE_URL}/coupons/${coupon._id}`, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify(coupon),
    })
    const data = await res.json()
    return data
  } catch (error) {
    console.error("Error updating coupon", error)
    return null
  }
}

export const deleteCoupon = async (id: string) => {
  try {
    const token = getToken()
    const res = await fetch(`${API_BASE_URL}/coupons/${id}`, {
      method: "DELETE",
      headers: {
        Authorization: `Bearer ${token}`,
      },
    })
    const data = await res.json()
    return data
  } catch (error) {
    console.error("Error deleting coupon", error)
    return null
  }
}
