import { Order } from "@/types/order"
import { getToken } from "@/utils/gettoken"

import { API_BASE_URL } from "."

export const fetchAllOrders = async () => {
  try {
    const token = getToken()
    const res = await fetch(`${API_BASE_URL}/orders`, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    })
    const data = await res.json()
    return data
  } catch (error) {
    console.error("Error fetching orders", error)
    return []
  }
}

export const fetchOrdersWithPagination = async (
  page: number,
  limit: number,
) => {
  try {
    const token = getToken()
    const res = await fetch(
      `${API_BASE_URL}/orders/pagination?page=${page}&limit=${limit}`,
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      },
    )
    const data = await res.json()
    return data
  } catch (error) {
    console.error("Error fetching orders with pagination", error)
    return []
  }
}

export const fetchOrderById = async (id: string) => {
  try {
    const token = getToken()
    const res = await fetch(`${API_BASE_URL}/orders/${id}`, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    })
    const data = await res.json()
    return data
  } catch (error) {
    console.error("Error fetching order by ID", error)
    return null
  }
}

export const createOrder = async (order: Order) => {
  try {
    const token = getToken()
    const res = await fetch(`${API_BASE_URL}/orders`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify(order),
    })
    const data = await res.json()
    return data
  } catch (error) {
    console.error("Error creating order", error)
    return null
  }
}

export const updateOrder = async (order: Order) => {
  try {
    const token = getToken()
    const res = await fetch(`${API_BASE_URL}/orders/${order._id}`, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify(order),
    })
    const data = await res.json()
    return data
  } catch (error) {
    console.error("Error updating order", error)
    return null
  }
}

export const deleteOrder = async (id: string) => {
  try {
    const token = getToken()
    const res = await fetch(`${API_BASE_URL}/orders/${id}`, {
      method: "DELETE",
      headers: {
        Authorization: `Bearer ${token}`,
      },
    })
    const data = await res.json()
    return data
  } catch (error) {
    console.error("Error deleting order", error)
    return null
  }
}
