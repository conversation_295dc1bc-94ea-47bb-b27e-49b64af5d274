import { Payment } from "@/types/payment"
import { getToken } from "@/utils/gettoken"

import { API_BASE_URL } from "."

export const fetchAllPayments = async () => {
  const token = getToken()
  try {
    const res = await fetch(`${API_BASE_URL}/payments`, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    })
    const data = await res.json()
    return data
  } catch (error) {
    console.error("Error fetching payments", error)
    return []
  }
}

// fetch payment with pagination
export const fetchPaymentsWithPagination = async (
  page: number,
  limit: number,
) => {
  const token = getToken()
  try {
    const res = await fetch(
      `${API_BASE_URL}/payments/pagination?page=${page}&limit=${limit}`,
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      },
    )
    const data = await res.json()
    return data
  } catch (error) {
    console.error("Error fetching payments with pagination", error)
    return []
  }
}

// fetch payment by id
export const fetchPaymentById = async (id: string) => {
  const token = getToken()
  try {
    const res = await fetch(`${API_BASE_URL}/payments/${id}`, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    })
    const data = await res.json()
    return data
  } catch (error) {
    console.error("Error fetching payment by ID", error)
    return null
  }
}

// create payment
export const createPayment = async (payment: Payment) => {
  const token = getToken()
  try {
    const res = await fetch(`${API_BASE_URL}/payments`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify(payment),
    })
    const data = await res.json()
    return data
  } catch (error) {
    console.error("Error creating payment", error)
    return null
  }
}

// update payment
export const updatePayment = async (payment: Payment) => {
  const token = getToken()
  try {
    const res = await fetch(`${API_BASE_URL}/payments/${payment._id}`, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify(payment),
    })
    const data = await res.json()
    return data
  } catch (error) {
    console.error("Error updating payment", error)
    return null
  }
}

// delete payment
export const deletePayment = async (id: string) => {
  const token = getToken()
  try {
    const res = await fetch(`${API_BASE_URL}/payments/${id}`, {
      method: "DELETE",
      headers: {
        Authorization: `Bearer ${token}`,
      },
    })
    const data = await res.json()
    return data
  } catch (error) {
    console.error("Error deleting payment", error)
    return null
  }
}

// fetch payments by user id
export const fetchPaymentsByUserId = async (userId: string) => {
  const token = getToken()
  try {
    const res = await fetch(`${API_BASE_URL}/payments/user/${userId}`, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    })
    const data = await res.json()
    return data
  } catch (error) {
    console.error("Error fetching payments by user ID", error)
    return []
  }
}
