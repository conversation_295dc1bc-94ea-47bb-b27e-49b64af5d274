import { Gallery } from "@/types/gallery"
import { getToken } from "@/utils/gettoken"

import { API_BASE_URL } from "."

// https://api.unsplash.com/search/photos?query=dog&page=1&per_page=10&client_id=YOUR_ACCESS_KEY
const UNSPLASH_API_URL = process.env.NEXT_PUBLIC_UNSPLASH_API_URL // Replace with your Unsplash API URL
const UNSPLASH_ACCESS_KEY = process.env.NEXT_PUBLIC_UNSPLASH_ACCESS_KEY // Replace with your Unsplash Access Key

export const searchUnsplashWithApi = async (
  query: string,
  page = 1,
  perPage = 10,
) => {
  try {
    const url = `${UNSPLASH_API_URL}?query=${query}&page=${page}&per_page=${perPage}&client_id=${UNSPLASH_ACCESS_KEY}`
    const res = await fetch(url)

    if (!res.ok) {
      throw new Error(`Unsplash API error: ${res.statusText}`)
    }

    const data = await res.json()
    return data // Contains `results` and other metadata like `total_pages`
  } catch (error) {
    console.error("Error searching Unsplash:", error)
    return { results: [], total_pages: 0 }
  }
}

export const searchUnsplash = async (query: string, page = 1, perPage = 10) => {
  try {
    const url = `${UNSPLASH_API_URL}/search/photos?client_id=${UNSPLASH_ACCESS_KEY}&query=${query}&page=${page}&per_page=${perPage}`
    const res = await fetch(url)

    if (!res.ok) {
      throw new Error(`Unsplash API error: ${res.statusText}`)
    }

    const data = await res.json()
    return data // Contains `results` and other metadata like `total_pages`
  } catch (error) {
    console.error("Error searching Unsplash:", error)
    return { results: [], total_pages: 0 }
  }
}

//fetch all gallery items
export const fetchAllGalleryItems = async () => {
  try {
    const token = getToken()
    const res = await fetch(`${API_BASE_URL}/gallery`, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    })
    const data = await res.json()
    return data
  } catch (error) {
    console.error("Error fetching gallery items", error)
    return []
  }
}

//fetch gallery items with pagination
export const fetchGalleryItemsWithPagination = async (
  page: number,
  limit: number,
  search?: string,
) => {
  try {
    const token = getToken()
    const res = await fetch(
      `${API_BASE_URL}/gallery/pagination?page=${page}&limit=${limit}&search=${
        search ? search : ""
      }`,
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      },
    )
    const data = await res.json()
    return data
  } catch (error) {
    console.error("Error fetching gallery items with pagination", error)
    return {}
  }
}

//fetch gallery item by id
export const fetchGalleryItemById = async (id: string) => {
  try {
    const token = getToken()
    const res = await fetch(`${API_BASE_URL}/gallery/${id}`, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    })
    const data = await res.json()
    return data
  } catch (error) {
    console.error("Error fetching gallery item by ID", error)
    return null
  }
}

//create gallery item
export const createGalleryItem = async (item: Gallery) => {
  try {
    const token = getToken()
    const res = await fetch(`${API_BASE_URL}/gallery/create`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify(item),
    })
    const data = await res.json()
    return data
  } catch (error) {
    console.error("Error creating gallery item", error)
    return null
  }
}

//update gallery item
export const updateGalleryItem = async (item: Gallery) => {
  try {
    const token = getToken()
    const res = await fetch(`${API_BASE_URL}/gallery/${item._id}`, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify(item),
    })
    const data = await res.json()
    return data
  } catch (error) {
    console.error("Error updating gallery item", error)
    return null
  }
}

//delete gallery item
// export const deleteGalleryItem = async (id: string) => {
//   try {
//     const token = getToken();
//     const res = await fetch(`${API_BASE_URL}/gallery/${id}`, {
//       method: "DELETE",
//       headers: {
//         Authorization: `Bearer ${token}`,
//       },
//     });
//     const data = await res.json();
//     return data;
//   } catch (error) {
//     console.error("Error deleting gallery item", error);
//     return null;
//   }
// };

// delete single or multiple gallery items
export const deleteGalleryItems = async (ids: string[]) => {
  try {
    const token = getToken()
    const res = await fetch(`${API_BASE_URL}/gallery/delete`, {
      method: "DELETE",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify({ ids }),
    })
    const data = await res.json()
    return data
  } catch (error) {
    console.error("Error deleting gallery item", error)
    return null
  }
}
