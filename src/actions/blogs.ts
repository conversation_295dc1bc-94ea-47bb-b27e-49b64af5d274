import { Blog } from "@/types/blog"
import { getToken } from "@/utils/gettoken"

import { API_BASE_URL } from "."
// fetch all blogs
export const fetchAllBlogs = async () => {
  try {
    const token = getToken()
    const res = await fetch(`${API_BASE_URL}/blogs`, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    })
    const data = await res.json()
    return data
  } catch (error) {
    console.error("Error fetching blogs", error)
    return []
  }
}

// fetch blogs with pagination
export const fetchBlogsWithPagination = async (page: number, limit: number) => {
  try {
    const token = getToken()
    const res = await fetch(
      `${API_BASE_URL}/blogs/pagination?page=${page}&limit=${limit}`,
      {
        headers: {
          Authorization: `Bearer ${token}`,
        },
      },
    )
    const data = await res.json()
    return data
  } catch (error) {
    console.error("Error fetching blogs with pagination", error)
    return []
  }
}

// fetch blog by ID
export const fetchBlogById = async (id: string) => {
  try {
    const token = getToken()
    const res = await fetch(`${API_BASE_URL}/blogs/${id}`, {
      headers: {
        Authorization: `Bearer ${token}`,
      },
    })
    const data = await res.json()
    return data
  } catch (error) {
    console.error("Error fetching blog by ID", error)
    return null
  }
}

// create blog
export const createBlog = async (blog: Blog) => {
  try {
    const token = getToken()
    const res = await fetch(`${API_BASE_URL}/blogs`, {
      method: "POST",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify(blog),
    })
    const data = await res.json()
    return data
  } catch (error) {
    console.error("Error creating blog", error)
    return null
  }
}

// update blog
export const updateBlog = async (blog: Blog) => {
  try {
    const token = getToken()
    const res = await fetch(`${API_BASE_URL}/blogs/${blog._id}`, {
      method: "PUT",
      headers: {
        "Content-Type": "application/json",
        Authorization: `Bearer ${token}`,
      },
      body: JSON.stringify(blog),
    })
    const data = await res.json()
    return data
  } catch (error) {
    console.error("Error updating blog", error)
    return null
  }
}

// delete blog
export const deleteBlog = async (id: string) => {
  try {
    const token = getToken()
    const res = await fetch(`${API_BASE_URL}/blogs/${id}`, {
      method: "DELETE",
      headers: {
        Authorization: `Bearer ${token}`,
      },
    })
    const data = await res.json()
    return data
  } catch (error) {
    console.error("Error deleting blog", error)
    return null
  }
}
