import { NextRequest, NextResponse } from "next/server"

const isProtectedRoute = (pathname: string) => {
  return pathname.startsWith("/dashboard") || pathname.startsWith("/admin")
}

const isPublicRoute = (pathname: string) => {
  return (
    pathname === "/" ||
    pathname.startsWith("/marketing") ||
    pathname.startsWith("/templates") ||
    pathname.startsWith("/pricing")
  )
}

const isAuthRoute = (pathname: string) => {
  return pathname.startsWith("/auth")
}

export default async function middleware(req: NextRequest) {
  const { pathname } = req.nextUrl

  // Allow public routes
  if (isPublicRoute(pathname)) {
    return NextResponse.next()
  }

  // Get token from cookies or Authorization header
  const token =
    req.cookies.get("token")?.value ||
    req.headers.get("authorization")?.replace("Bearer ", "")

  // Check if user is authenticated for protected routes
  if (isProtectedRoute(pathname)) {
    if (!token) {
      return NextResponse.redirect(new URL("/auth/signin", req.url))
    }

    // Verify token with backend
    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/auth/verify-token`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        },
      )

      if (!response.ok) {
        return NextResponse.redirect(new URL("/auth/signin", req.url))
      }

      const data = await response.json()

      // Check dashboard access for admin routes
      if (
        pathname.startsWith("/admin") &&
        !["admin", "super_admin"].includes(data.user?.role)
      ) {
        return NextResponse.redirect(new URL("/", req.url))
      }

      // Check general dashboard access
      if (pathname.startsWith("/dashboard") && !data.user?.access) {
        return NextResponse.redirect(new URL("/", req.url))
      }
    } catch (error) {
      console.error("Token verification failed:", error)
      return NextResponse.redirect(new URL("/auth/signin", req.url))
    }
  }

  // Redirect authenticated users away from auth routes
  if (isAuthRoute(pathname) && token) {
    try {
      const response = await fetch(
        `${process.env.NEXT_PUBLIC_API_BASE_URL}/auth/verify-token`,
        {
          headers: {
            Authorization: `Bearer ${token}`,
          },
        },
      )

      if (response.ok) {
        return NextResponse.redirect(new URL("/", req.url))
      }
    } catch (error) {
      // Token is invalid, allow access to auth routes
    }
  }

  return NextResponse.next()
}

export const config = {
  // matcher: ["/((?!.*\\..*|_next).*)", "/(api|trpc)(.*)"],
  matcher: [
    // Skip Next.js internals and all static files, unless found in search params
    "/((?!_next|[^?]*\\.(?:html?|css|js(?!on)|jpe?g|webp|png|gif|svg|ttf|woff2?|ico|csv|docx?|xlsx?|zip|webmanifest)).*)",
    // Always run for API routes
    "/(api|trpc)(.*)",
  ],
}
