import "@/styles/globals.css"
import NextTopLoader from "nextjs-toploader"

import { Providers } from "@/components/global"
import { aeonik, inter } from "@/constants"
import { cn, generateMetadata } from "@/functions"

export const metadata = generateMetadata()

export default function RootLayout({
  children,
}: Readonly<{
  children: React.ReactNode
}>) {
  return (
    <html lang='en' suppressHydrationWarning>
      <body
        className={cn(
          "dark min-h-screen overflow-x-hidden bg-background font-default text-foreground antialiased !scrollbar-hide",
          inter.variable,
          aeonik.variable,
        )}
      >
        {/* <Toaster
                    richColors
                    theme="dark"
                    position="top-right"
                /> */}
        <NextTopLoader showSpinner={false} height={3} color='#8c49ff' />

        <Providers>{children}</Providers>
      </body>
    </html>
  )
}
