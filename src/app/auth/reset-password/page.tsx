"use client"

import { motion } from "framer-motion"
import { EyeIcon, EyeOffIcon } from "lucide-react"
import Link from "next/link"
import { useRouter, useSearchParams } from "next/navigation"
import { useEffect, useState } from "react"
import { toast } from "sonner"

import { FADE_IN_VARIANTS } from "@/constants"
import { useAuth } from "@/stores/auth-store"

import Icons from "@/components/global/icons"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import LoadingIcon from "@/components/ui/loading-icon"

const ResetPasswordPage = () => {
  const router = useRouter()
  const searchParams = useSearchParams()
  const { resetPassword } = useAuth()

  const [password, setPassword] = useState("")
  const [confirmPassword, setConfirmPassword] = useState("")
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [isLoading, setIsLoading] = useState(false)
  const [token, setToken] = useState("")

  useEffect(() => {
    const resetToken = searchParams.get("token")

    if (!resetToken) {
      toast.error("Invalid reset link")
      router.push("/auth/forgot-password")
      return
    }

    setToken(resetToken)
  }, [searchParams, router])

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()

    if (!password || !confirmPassword) {
      toast.error("Please fill in all fields")
      return
    }

    if (password !== confirmPassword) {
      toast.error("Passwords do not match")
      return
    }

    if (password.length < 6) {
      toast.error("Password must be at least 6 characters long")
      return
    }

    if (!token) {
      toast.error("Invalid reset token")
      return
    }

    setIsLoading(true)

    try {
      const result = await resetPassword(token, password)

      if (result.success) {
        toast.success(result.message || "Password reset successfully!")
        router.push("/auth/signin")
      } else {
        toast.error(
          result.message || "Password reset failed. Please try again.",
        )
      }
    } catch (error) {
      console.error("Reset password error:", error)
      toast.error("An error occurred. Please try again.")
    } finally {
      setIsLoading(false)
    }
  }

  if (!token) {
    return (
      <div className='flex size-full flex-col items-center justify-center'>
        <div className='mx-auto mt-[270px] flex size-full max-w-xs flex-col items-center text-center'>
          <motion.div
            variants={FADE_IN_VARIANTS}
            animate='visible'
            initial='hidden'
          >
            <div className='flex justify-center'>
              <Link href='/'>
                <Icons.icon className='h-8 w-8' />
              </Link>
            </div>
            <h1 className='mt-4 text-center text-2xl'>Invalid Reset Link</h1>
            <p className='mt-2 text-sm text-muted-foreground'>
              The password reset link is invalid or has expired.
            </p>
            <Button
              onClick={() => router.push("/auth/forgot-password")}
              className='mt-6'
            >
              Request New Reset Link
            </Button>
          </motion.div>
        </div>
      </div>
    )
  }

  return (
    <div className='flex size-full flex-col items-center justify-center'>
      <div className='mx-auto mt-[270px] flex size-full max-w-xs flex-col items-center'>
        <motion.div
          variants={FADE_IN_VARIANTS}
          animate='visible'
          initial='hidden'
          className='w-full text-center'
        >
          <div className='flex justify-center'>
            <Link href='/'>
              <Icons.icon className='h-8 w-8' />
            </Link>
          </div>

          <h1 className='mt-4 text-center text-2xl'>Reset your password</h1>
          <p className='mt-2 text-sm text-muted-foreground'>
            Enter your new password below.
          </p>

          <form onSubmit={handleSubmit} className='mt-8 space-y-4'>
            <div className='relative'>
              <Input
                type={showPassword ? "text" : "password"}
                placeholder='New password'
                value={password}
                onChange={(e) => setPassword(e.target.value)}
                className='pr-10'
              />
              <button
                type='button'
                onClick={() => setShowPassword(!showPassword)}
                className='absolute right-3 top-1/2 -translate-y-1/2 text-muted-foreground hover:text-foreground'
              >
                {showPassword ? (
                  <EyeOffIcon className='h-4 w-4' />
                ) : (
                  <EyeIcon className='h-4 w-4' />
                )}
              </button>
            </div>

            <div className='relative'>
              <Input
                type={showConfirmPassword ? "text" : "password"}
                placeholder='Confirm new password'
                value={confirmPassword}
                onChange={(e) => setConfirmPassword(e.target.value)}
                className='pr-10'
              />
              <button
                type='button'
                onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                className='absolute right-3 top-1/2 -translate-y-1/2 text-muted-foreground hover:text-foreground'
              >
                {showConfirmPassword ? (
                  <EyeOffIcon className='h-4 w-4' />
                ) : (
                  <EyeIcon className='h-4 w-4' />
                )}
              </button>
            </div>

            <Button type='submit' className='w-full' disabled={isLoading}>
              {isLoading ? <LoadingIcon className='mr-2 h-4 w-4' /> : null}
              Reset Password
            </Button>
          </form>

          <div className='mt-6'>
            <Link
              href='/auth/signin'
              className='text-sm text-muted-foreground hover:text-foreground'
            >
              Back to Sign In
            </Link>
          </div>
        </motion.div>
      </div>
    </div>
  )
}

export default ResetPasswordPage
