import Image from "next/image"
import React from "react"

import { Particles } from "@/components/ui/particles"

interface Props {
  children: React.ReactNode
}

const AuthLayout = ({ children }: Props) => {
  return (
    <main className='relative h-screen w-full select-none'>
      {/* <div className="absolute left-1/2 -translate-x-1/2 top-0 w-1/3 h-1/8 blur-[12rem] rounded-b-full bg-foreground/70 -z-20"></div> */}
      <div className='absolute inset-x-0 top-0 -z-10 hidden size-full opacity-50 lg:hidden'>
        <Image
          src='/images/bas.svg'
          alt='Auth background'
          width={1920}
          height={1080}
          className='pointer-events-none h-full select-none'
        />
      </div>
      <Particles
        className='absolute inset-x-0 bottom-4 -z-10 hidden h-1/3'
        quantity={50}
        ease={80}
        color='#d4d4d4'
        refresh
      />
      {children}
    </main>
  )
}

export default AuthLayout
