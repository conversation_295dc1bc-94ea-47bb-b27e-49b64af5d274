"use client"

import { motion } from "framer-motion"
import { ArrowLeftIcon, MailIcon } from "lucide-react"
import Link from "next/link"
import { useRouter } from "next/navigation"
import { useState } from "react"
import { toast } from "sonner"

import { FADE_IN_VARIANTS } from "@/constants"
import { useAuth } from "@/contexts/auth-context"

import Icons from "@/components/global/icons"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import LoadingIcon from "@/components/ui/loading-icon"

const ForgotPasswordPage = () => {
  const router = useRouter()
  const { forgotPassword } = useAuth()
  
  const [email, setEmail] = useState("")
  const [isLoading, setIsLoading] = useState(false)
  const [isEmailSent, setIsEmailSent] = useState(false)

  const handleSubmit = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()

    if (!email) {
      toast.error("Please enter your email address")
      return
    }

    // Basic email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(email)) {
      toast.error("Please enter a valid email address")
      return
    }

    setIsLoading(true)

    try {
      const result = await forgotPassword(email)
      
      if (result.success) {
        setIsEmailSent(true)
        toast.success(result.message || "Password reset email sent!")
      } else {
        toast.error(result.message || "Failed to send reset email. Please try again.")
      }
    } catch (error) {
      console.error("Forgot password error:", error)
      toast.error("An error occurred. Please try again.")
    } finally {
      setIsLoading(false)
    }
  }

  const handleBackToSignIn = () => {
    router.push("/auth/signin")
  }

  const handleTryAgain = () => {
    setIsEmailSent(false)
    setEmail("")
  }

  return (
    <div className='flex size-full flex-col items-center justify-center'>
      <div className='mx-auto mt-[270px] flex size-full max-w-xs flex-col items-center'>
        <motion.div
          variants={FADE_IN_VARIANTS}
          animate='visible'
          initial='hidden'
          className='w-full text-center'
        >
          <div className='flex justify-center'>
            <Link href='/'>
              <Icons.icon className='h-8 w-8' />
            </Link>
          </div>

          <h1 className='mt-4 text-center text-2xl'>
            {isEmailSent ? "Check your email" : "Forgot your password?"}
          </h1>
          <p className='mt-2 text-sm text-muted-foreground'>
            {isEmailSent
              ? "We've sent a password reset link to your email address."
              : "Enter your email address and we'll send you a link to reset your password."}
          </p>

          {isEmailSent ? (
            <div className='mt-8 space-y-4'>
              <div className='flex justify-center'>
                <MailIcon className='h-12 w-12 text-muted-foreground' />
              </div>
              <p className='text-sm text-muted-foreground'>
                If you don't see the email, check your spam folder or try again.
              </p>
              <div className='space-y-2'>
                <Button onClick={handleTryAgain} variant='outline' className='w-full'>
                  Try again
                </Button>
                <Button onClick={handleBackToSignIn} className='w-full'>
                  Back to Sign In
                </Button>
              </div>
            </div>
          ) : (
            <div className='mt-8 space-y-4'>
              <Button
                variant='outline'
                onClick={handleBackToSignIn}
                className='flex w-full items-center gap-2'
              >
                <ArrowLeftIcon className='h-4 w-4' />
                Back to Sign In
              </Button>

              <form onSubmit={handleSubmit} className='space-y-4'>
                <Input
                  type='email'
                  placeholder='Enter your email'
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                />
                <Button
                  type='submit'
                  className='w-full'
                  disabled={isLoading}
                >
                  {isLoading ? (
                    <LoadingIcon className='mr-2 h-4 w-4' />
                  ) : (
                    <MailIcon className='mr-2 h-4 w-4' />
                  )}
                  Send Reset Link
                </Button>
              </form>
            </div>
          )}
        </motion.div>
      </div>
    </div>
  )
}

export default ForgotPasswordPage
