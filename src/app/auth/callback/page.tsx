"use client"

import { useRouter, useSearch<PERSON>ara<PERSON> } from "next/navigation"
import { useEffect, useState } from "react"
import { toast } from "sonner"

import { handleGoogleCallback } from "@/actions/auth"
import { useAuth } from "@/contexts/auth-context"

import LoadingIcon from "@/components/ui/loading-icon"

const AuthCallbackPage = () => {
  const router = useRouter()
  const searchParams = useSearchParams()
  const { refreshUser } = useAuth()
  const [isLoading, setIsLoading] = useState(true)

  useEffect(() => {
    const handleCallback = async () => {
      const code = searchParams.get("code")
      const error = searchParams.get("error")

      if (error) {
        toast.error("Authentication failed")
        router.push("/auth/signin")
        return
      }

      if (code) {
        try {
          const result = await handleGoogleCallback(code)

          if (result.success) {
            // Store token if provided
            if (result.token) {
              localStorage.setItem("token", result.token)
            }

            // Refresh user data
            await refreshUser()

            toast.success("Login successful!")
            router.push("/")
          } else {
            toast.error(result.message || "Authentication failed")
            router.push("/auth/signin")
          }
        } catch (error) {
          console.error("Callback error:", error)
          toast.error("Authentication failed")
          router.push("/auth/signin")
        }
      } else {
        // No code parameter, redirect to signin
        router.push("/auth/signin")
      }

      setIsLoading(false)
    }

    handleCallback()
  }, [searchParams, router, refreshUser])

  if (isLoading) {
    return (
      <div className='flex h-screen items-center justify-center'>
        <div className='text-center'>
          <LoadingIcon className='mx-auto h-8 w-8' />
          <p className='mt-2 text-muted-foreground'>
            Completing authentication...
          </p>
        </div>
      </div>
    )
  }

  return null
}

export default AuthCallbackPage
