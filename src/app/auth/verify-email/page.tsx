"use client"

import { motion } from "framer-motion"
import { CheckCircleIcon, XCircleIcon } from "lucide-react"
import Link from "next/link"
import { useRouter, useSearchParams } from "next/navigation"
import { useEffect, useState } from "react"
import { toast } from "sonner"

import { FADE_IN_VARIANTS } from "@/constants"
import { useAuth } from "@/contexts/auth-context"

import Icons from "@/components/global/icons"
import { Button } from "@/components/ui/button"
import LoadingIcon from "@/components/ui/loading-icon"

const VerifyEmailPage = () => {
  const router = useRouter()
  const searchParams = useSearchParams()
  const { verifyEmail } = useAuth()
  
  const [isLoading, setIsLoading] = useState(true)
  const [isVerified, setIsVerified] = useState(false)
  const [error, setError] = useState("")

  useEffect(() => {
    const token = searchParams.get("token")
    
    if (!token) {
      setError("Invalid verification link")
      setIsLoading(false)
      return
    }

    handleVerification(token)
  }, [searchParams])

  const handleVerification = async (token: string) => {
    try {
      const result = await verifyEmail(token)
      
      if (result.success) {
        setIsVerified(true)
        toast.success("Email verified successfully!")
      } else {
        setError(result.message || "Email verification failed")
        toast.error(result.message || "Email verification failed")
      }
    } catch (error) {
      console.error("Verification error:", error)
      setError("An error occurred during verification")
      toast.error("An error occurred during verification")
    } finally {
      setIsLoading(false)
    }
  }

  const handleContinue = () => {
    router.push("/auth/signin")
  }

  return (
    <div className='flex size-full flex-col items-center justify-center'>
      <div className='mx-auto mt-[270px] flex size-full max-w-md flex-col items-center text-center'>
        <motion.div
          variants={FADE_IN_VARIANTS}
          animate='visible'
          initial='hidden'
          className='w-full'
        >
          <div className='flex justify-center'>
            <Link href='/'>
              <Icons.icon className='h-8 w-8' />
            </Link>
          </div>

          <div className='mt-8'>
            {isLoading ? (
              <div className='flex flex-col items-center space-y-4'>
                <LoadingIcon className='h-12 w-12' />
                <h1 className='text-2xl font-semibold'>Verifying your email...</h1>
                <p className='text-muted-foreground'>
                  Please wait while we verify your email address.
                </p>
              </div>
            ) : isVerified ? (
              <div className='flex flex-col items-center space-y-4'>
                <CheckCircleIcon className='h-12 w-12 text-green-500' />
                <h1 className='text-2xl font-semibold'>Email verified!</h1>
                <p className='text-muted-foreground'>
                  Your email has been successfully verified. You can now sign in to your account.
                </p>
                <Button onClick={handleContinue} className='mt-6'>
                  Continue to Sign In
                </Button>
              </div>
            ) : (
              <div className='flex flex-col items-center space-y-4'>
                <XCircleIcon className='h-12 w-12 text-red-500' />
                <h1 className='text-2xl font-semibold'>Verification failed</h1>
                <p className='text-muted-foreground'>
                  {error || "We couldn't verify your email address. The link may be invalid or expired."}
                </p>
                <div className='mt-6 space-y-2'>
                  <Button onClick={() => router.push("/auth/signup")} variant='outline'>
                    Try signing up again
                  </Button>
                  <Button onClick={() => router.push("/auth/signin")}>
                    Back to Sign In
                  </Button>
                </div>
              </div>
            )}
          </div>
        </motion.div>
      </div>
    </div>
  )
}

export default VerifyEmailPage
