import { fetchProductsByType } from "@/actions/products"
import MasonaryLayout from "@/components/custom/masonary-layout"

export default async function Page({
  params: { type },
}: {
  params: { type: string }
}) {
  const productResult = await fetchProductsByType(type)
  console.log("data", type, productResult)

  return (
    <>
      <MasonaryLayout data={productResult?.data} />

      <div className='fixed inset-0 left-1/2 top-0 -z-10 h-1/4 w-3/4 -translate-x-1/2 -translate-y-1/2 bg-primary/30 blur-[20rem]' />
    </>
  )
}
