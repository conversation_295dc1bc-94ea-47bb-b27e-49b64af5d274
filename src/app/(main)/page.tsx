import { GlowingBentoGrid } from "@/components/custom"
import { Container, Wrapper } from "@/components/layout"
import FeaturesSection from "@/components/main/features-section"
import Hero from "@/components/main/hero"
import Pricing from "@/components/main/pricing"
import Reviews from "@/components/main/reviews"
import TechShowcase from "@/components/main/tech-showcase"
import { Companies } from "@/components/sections/companies"
import { CTA } from "@/components/sections/cta"
import { FeaturesGrid } from "@/components/sections/features-grid"
import Perks from "@/components/sections/perks"

const HomePage = () => {
  return (
    <>
      {/* <Background background={0}> */}
      {/* <div className="absolute inset-0 h-screen">
        <Image
          src={
            "https://utfs.io/f/qPlpyBmwd8UNDbQGePgfcU0BPHiJwnq2OTMo8C6r5aRkyYWb"
          }
          alt="background"
          layout="fill"
          objectFit="cover"
          objectPosition="center"
          quality={100}
          className="z-[-1]"
        />
      </div> */}

      <Hero />

      <Wrapper className='relative pb-14'>
        <Container className='py-8 !pt-0 lg:py-10'>
          {/* <Connect /> */}
          <GlowingBentoGrid />
          <FeaturesSection />
          <FeaturesGrid />
          <TechShowcase />
          <Companies />

          <Perks />
          <Pricing />
          <Reviews />
          <CTA />
        </Container>
      </Wrapper>

      {/* </Background> */}
    </>
  )
}

export default HomePage
