"use client"

import { useMemo, useState } from "react"

import { BlogPostCard } from "@/components/cards"
import { BlogFilters } from "@/components/common/blog-filters"
import Heading from "@/components/global/heading"
import { Container, Wrapper } from "@/components/layout"
import { <PERSON><PERSON><PERSON><PERSON> } from "@/components/sections/blog-hero"
import { BlogStats } from "@/components/sections/blog-stats"
import { PopularPostsSidebar } from "@/components/sections/popular-posts-sidebar"
import { Spotlight } from "@/components/ui/spotlight"
import { BLOG_POSTS, FEATURED_POSTS } from "@/constants"

const BlogsPage = () => {
  const [searchQuery, setSearchQuery] = useState("")
  const [selectedCategory, setSelectedCategory] = useState("")
  const [selectedTags, setSelectedTags] = useState<string[]>([])

  // Get all available tags from blog posts
  const availableTags = useMemo(() => {
    const tags = new Set<string>()
    BLOG_POSTS.forEach((post) => {
      post.tags.forEach((tag) => tags.add(tag))
    })
    return Array.from(tags).sort()
  }, [])

  // Filter blog posts based on search and filters
  const filteredPosts = useMemo(() => {
    return BLOG_POSTS.filter((post) => {
      const matchesSearch =
        !searchQuery ||
        post.title.toLowerCase().includes(searchQuery.toLowerCase()) ||
        post.description.toLowerCase().includes(searchQuery.toLowerCase()) ||
        post.author.name.toLowerCase().includes(searchQuery.toLowerCase())

      const matchesCategory =
        !selectedCategory || post.category === selectedCategory

      const matchesTags =
        selectedTags.length === 0 ||
        selectedTags.some((tag) => post.tags.includes(tag))

      return matchesSearch && matchesCategory && matchesTags
    })
  }, [searchQuery, selectedCategory, selectedTags])

  const featuredPost = FEATURED_POSTS[0]

  return (
    <Wrapper className='relative py-20'>
      <Container className='py-8 lg:py-20'>
        <Spotlight
          className='-top-40 left-0 md:-top-20 md:left-60'
          fill='rgba(255, 255, 255, 0.5)'
        />

        {/* Hero Section */}
        {featuredPost && (
          <div className='mb-16'>
            <BlogHero featuredPost={featuredPost} />
          </div>
        )}

        {/* Header */}
        <div className='mb-12 text-center'>
          <Heading>Developer Insights & Tutorials</Heading>
          <p className='mx-auto mt-6 max-w-2xl text-center text-muted-foreground'>
            Explore our comprehensive collection of articles on modern
            development practices, cutting-edge tools, and innovative techniques
            to elevate your projects and stay ahead of industry trends.
          </p>
        </div>

        {/* Blog Stats */}
        <div className='mb-12'>
          <BlogStats />
        </div>

        {/* Main Content */}
        <div className='grid gap-8 xl:grid-cols-12'>
          {/* Sidebar Filters */}
          <div className='xl:col-span-3'>
            <div className='sticky top-8 space-y-6'>
              <BlogFilters
                searchQuery={searchQuery}
                onSearchChange={setSearchQuery}
                selectedCategory={selectedCategory}
                onCategoryChange={setSelectedCategory}
                selectedTags={selectedTags}
                onTagsChange={setSelectedTags}
                availableTags={availableTags}
              />

              {/* Popular Posts - Only show on larger screens */}
              <div className='hidden xl:block'>
                <PopularPostsSidebar />
              </div>
            </div>
          </div>

          {/* Blog Posts Grid */}
          <div className='xl:col-span-9'>
            {/* Results Info */}
            <div className='mb-6 flex items-center justify-between'>
              <p className='text-sm text-muted-foreground'>
                {filteredPosts.length === BLOG_POSTS.length
                  ? `Showing all ${BLOG_POSTS.length} articles`
                  : `Found ${filteredPosts.length} of ${BLOG_POSTS.length} articles`}
              </p>
            </div>

            {/* Posts Grid */}
            {filteredPosts.length > 0 ? (
              <div className='grid gap-6 md:grid-cols-2 2xl:grid-cols-3'>
                {filteredPosts.map((post) => (
                  <BlogPostCard
                    key={post.id}
                    post={post}
                    variant={post.featured ? "featured" : "default"}
                  />
                ))}
              </div>
            ) : (
              <div className='rounded-lg border border-dashed p-12 text-center'>
                <h3 className='mb-2 text-lg font-semibold'>
                  No articles found
                </h3>
                <p className='text-muted-foreground'>
                  Try adjusting your search or filter criteria to find more
                  articles.
                </p>
              </div>
            )}

            {/* Popular Posts - Show on smaller screens */}
            <div className='mt-12 xl:hidden'>
              <PopularPostsSidebar />
            </div>
          </div>
        </div>
      </Container>
    </Wrapper>
  )
}

export default BlogsPage
