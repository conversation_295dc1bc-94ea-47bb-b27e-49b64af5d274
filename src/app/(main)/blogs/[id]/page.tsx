"use client"

import { <PERSON>, <PERSON>, <PERSON>, Share2, User } from "lucide-react"
import Image from "next/image"
import Link from "next/link"
import { notFound } from "next/navigation"
import { useMemo } from "react"

import { BlogPostCard } from "@/components/cards"
import Heading from "@/components/global/heading"
import { Container, Wrapper } from "@/components/layout"
import { PopularPostsSidebar } from "@/components/sections/popular-posts-sidebar"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Separator } from "@/components/ui/separator"
import { Spotlight } from "@/components/ui/spotlight"
import { BLOG_POSTS } from "@/constants"

interface BlogDetailsPageProps {
  params: {
    id: string
  }
}

const getCategoryColor = (category: string) => {
  const colors = {
    typescript: "bg-blue-500/10 text-blue-400 border-blue-500/20",
    "ui-design": "bg-purple-500/10 text-purple-400 border-purple-500/20",
    "developer-tools": "bg-green-500/10 text-green-400 border-green-500/20",
    nextjs: "bg-gray-500/10 text-gray-400 border-gray-500/20",
    "web-development": "bg-orange-500/10 text-orange-400 border-orange-500/20",
    react: "bg-cyan-500/10 text-cyan-400 border-cyan-500/20",
  }
  return (
    colors[category as keyof typeof colors] ||
    "bg-gray-500/10 text-gray-400 border-gray-500/20"
  )
}

const BlogDetailsPage = ({ params }: BlogDetailsPageProps) => {
  // Find the blog post by slug (id parameter)
  const post = useMemo(() => {
    return BLOG_POSTS.find((p) => p.slug === params.id)
  }, [params.id])

  // Get related posts (same category, excluding current post)
  const relatedPosts = useMemo(() => {
    if (!post) return []
    return BLOG_POSTS.filter(
      (p) => p.category === post.category && p.id !== post.id,
    ).slice(0, 3)
  }, [post])

  if (!post) {
    notFound()
  }

  return (
    <Wrapper className='relative py-20'>
      <Container className='py-8 lg:py-20'>
        <Spotlight
          className='-top-40 left-0 md:-top-20 md:left-60'
          fill='rgba(255, 255, 255, 0.5)'
        />

        {/* Breadcrumb */}
        <div className='mb-8'>
          <nav className='text-sm text-muted-foreground'>
            <Link href='/blogs' className='hover:text-foreground'>
              Blogs
            </Link>
            <span className='mx-2'>/</span>
            <span className='text-foreground'>{post.title}</span>
          </nav>
        </div>

        <div className='grid gap-8 xl:grid-cols-12'>
          {/* Main Content */}
          <article className='xl:col-span-8'>
            {/* Header */}
            <header className='mb-8'>
              {/* Category and Meta */}
              <div className='mb-4 flex flex-wrap items-center gap-4 text-sm text-muted-foreground'>
                <Badge
                  variant='outline'
                  className={getCategoryColor(post.category)}
                >
                  {post.category.replace("-", " ")}
                </Badge>
                <div className='flex items-center gap-1'>
                  <Calendar className='h-4 w-4' />
                  <span>{post.date}</span>
                </div>
                <div className='flex items-center gap-1'>
                  <Clock className='h-4 w-4' />
                  <span>{post.readTime}</span>
                </div>
                {post.views && (
                  <div className='flex items-center gap-1'>
                    <Eye className='h-4 w-4' />
                    <span>{post.views.toLocaleString()} views</span>
                  </div>
                )}
              </div>

              {/* Title */}
              <Heading className='mb-6 text-left text-2xl md:text-4xl'>
                {post.title}
              </Heading>

              {/* Description */}
              <p className='text-lg text-muted-foreground md:text-xl'>
                {post.description}
              </p>

              {/* Author & Actions */}
              <div className='mt-6 flex items-center justify-between'>
                <div className='flex items-center gap-3'>
                  <div className='flex h-12 w-12 items-center justify-center rounded-full bg-primary/10'>
                    <User className='h-6 w-6 text-primary' />
                  </div>
                  <div>
                    <p className='font-medium'>{post.author.name}</p>
                    {post.author.bio && (
                      <p className='text-sm text-muted-foreground'>
                        {post.author.bio}
                      </p>
                    )}
                  </div>
                </div>

                <Button variant='outline' size='sm' className='gap-2'>
                  <Share2 className='h-4 w-4' />
                  Share
                </Button>
              </div>
            </header>

            {/* Featured Image */}
            <div className='mb-8 overflow-hidden rounded-lg'>
              <div className='relative aspect-video'>
                <Image
                  src={post.image}
                  alt={post.title}
                  fill
                  className='object-cover'
                  priority
                />
              </div>
            </div>

            {/* Tags */}
            <div className='mb-8'>
              <div className='flex flex-wrap gap-2'>
                {post.tags.map((tag) => (
                  <Badge key={tag} variant='secondary' className='text-xs'>
                    {tag}
                  </Badge>
                ))}
              </div>
            </div>

            {/* Article Content */}
            <div className='max-w-none space-y-6 text-base leading-relaxed'>
              {/* This is where the actual blog content would go */}
              {/* For now, we'll add some placeholder content */}
              <p className='text-muted-foreground'>
                This is where the main content of the blog post would be
                displayed. In a real application, you would fetch this content
                from your CMS or markdown files and render it here.
              </p>

              <h2
                id='introduction'
                className='mb-4 mt-8 text-2xl font-bold text-foreground'
              >
                Introduction
              </h2>
              <p className='text-muted-foreground'>
                Lorem ipsum dolor sit amet, consectetur adipiscing elit. Sed do
                eiusmod tempor incididunt ut labore et dolore magna aliqua. Ut
                enim ad minim veniam, quis nostrud exercitation ullamco laboris
                nisi ut aliquip ex ea commodo consequat.
              </p>

              <h2
                id='key-concepts'
                className='mb-4 mt-8 text-2xl font-bold text-foreground'
              >
                Key Concepts
              </h2>
              <p className='text-muted-foreground'>
                Duis aute irure dolor in reprehenderit in voluptate velit esse
                cillum dolore eu fugiat nulla pariatur. Excepteur sint occaecat
                cupidatat non proident, sunt in culpa qui officia deserunt
                mollit anim id est laborum.
              </p>

              <div className='my-6 rounded-lg border bg-muted/30 p-4'>
                <p className='text-sm text-muted-foreground'>
                  <strong>💡 Pro Tip:</strong> This is an example of how you
                  could format callout boxes or important notes within your blog
                  content.
                </p>
              </div>

              <h2
                id='implementation'
                className='mb-4 mt-8 text-2xl font-bold text-foreground'
              >
                Implementation
              </h2>
              <p className='text-muted-foreground'>
                Sed ut perspiciatis unde omnis iste natus error sit voluptatem
                accusantium doloremque laudantium, totam rem aperiam, eaque ipsa
                quae ab illo inventore veritatis et quasi architecto beatae
                vitae dicta sunt explicabo.
              </p>

              <div className='my-6 rounded-lg bg-muted/50 p-4 font-mono text-sm'>
                <p className='text-muted-foreground'>
                  {`// Example code block styling`}
                  <br />
                  {`const example = () => {`}
                  <br />
                  &nbsp;&nbsp;{`return "This is how code would look"`}
                  <br />
                  {`}`}
                </p>
              </div>

              <h2
                id='conclusion'
                className='mb-4 mt-8 text-2xl font-bold text-foreground'
              >
                Conclusion
              </h2>
              <p className='text-muted-foreground'>
                At vero eos et accusamus et iusto odio dignissimos ducimus qui
                blanditiis praesentium voluptatum deleniti atque corrupti quos
                dolores et quas molestias excepturi sint occaecati cupiditate
                non provident.
              </p>
            </div>

            {/* Related Posts */}
            {relatedPosts.length > 0 && (
              <div className='mt-16'>
                <Separator className='mb-8' />
                <h2 className='mb-6 text-2xl font-semibold'>
                  Related Articles
                </h2>
                <div className='grid gap-6 md:grid-cols-2 lg:grid-cols-3'>
                  {relatedPosts.map((relatedPost) => (
                    <BlogPostCard
                      key={relatedPost.id}
                      post={relatedPost}
                      variant='compact'
                    />
                  ))}
                </div>
              </div>
            )}
          </article>

          {/* Sidebar */}
          <aside className='xl:col-span-4'>
            <div className='sticky top-8 space-y-8'>
              {/* Table of Contents */}
              <div className='rounded-lg border p-6'>
                <h3 className='mb-4 font-semibold'>Table of Contents</h3>
                <nav className='space-y-2 text-sm'>
                  <a
                    href='#introduction'
                    className='block text-muted-foreground hover:text-foreground'
                  >
                    Introduction
                  </a>
                  <a
                    href='#key-concepts'
                    className='block text-muted-foreground hover:text-foreground'
                  >
                    Key Concepts
                  </a>
                  <a
                    href='#implementation'
                    className='block text-muted-foreground hover:text-foreground'
                  >
                    Implementation
                  </a>
                  <a
                    href='#conclusion'
                    className='block text-muted-foreground hover:text-foreground'
                  >
                    Conclusion
                  </a>
                </nav>
              </div>

              {/* Popular Posts */}
              <PopularPostsSidebar />

              {/* Newsletter Signup */}
              <div className='rounded-lg border bg-muted/30 p-6'>
                <h3 className='mb-2 font-semibold'>Stay Updated</h3>
                <p className='mb-4 text-sm text-muted-foreground'>
                  Get the latest articles and tutorials delivered to your inbox.
                </p>
                <div className='space-y-3'>
                  <input
                    type='email'
                    placeholder='Enter your email'
                    className='w-full rounded-md border bg-background px-3 py-2 text-sm'
                  />
                  <Button size='sm' className='w-full'>
                    Subscribe
                  </Button>
                </div>
              </div>
            </div>
          </aside>
        </div>
      </Container>
    </Wrapper>
  )
}

export default BlogDetailsPage
