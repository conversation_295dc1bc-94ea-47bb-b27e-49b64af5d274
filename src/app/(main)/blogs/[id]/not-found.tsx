import Link from "next/link"

import { Con<PERSON><PERSON>, Wrapper } from "@/components/layout"
import { But<PERSON> } from "@/components/ui/button"

export default function NotFound() {
  return (
    <Wrapper className='relative py-20'>
      <Container className='py-8 lg:py-20'>
        <div className='flex min-h-[400px] flex-col items-center justify-center text-center'>
          <h1 className='mb-4 text-6xl font-bold text-muted-foreground'>404</h1>
          <h2 className='mb-4 text-2xl font-semibold'>Blog Post Not Found</h2>
          <p className='mb-8 max-w-md text-muted-foreground'>
            The blog post you&apos;re looking for doesn&apos;t exist or may have
            been moved.
          </p>
          <div className='flex gap-4'>
            <Button asChild>
              <Link href='/blogs'>Back to Blogs</Link>
            </Button>
            <Button variant='outline' asChild>
              <Link href='/'>Go Home</Link>
            </Button>
          </div>
        </div>
      </Container>
    </Wrapper>
  )
}
