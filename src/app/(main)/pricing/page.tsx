import { Container, Wrapper } from "@/components/layout"
import Pricing from "@/components/main/pricing"
import Reviews from "@/components/main/reviews"
import { Spotlight } from "@/components/ui/spotlight"

const PircingPage = () => {
  return (
    <>
      {/* <Background> */}
      <Wrapper className='relative py-20'>
        <div className='absolute left-[calc(55%-379px/2)] top-[-266px] -z-10 hidden h-[620px] w-[379px] rounded-full bg-primary/60 opacity-50 blur-[10rem] lg:flex' />
        <div className='absolute left-[calc(50%-433px/2)] top-[-60px] -z-10 hidden h-[525px] w-[433px] rounded-full bg-primaryLight/40 opacity-50 blur-[15rem] lg:flex' />
        <Container className='relative'>
          <Spotlight
            className='-top-40 left-0 md:-top-20 md:left-60'
            fill='rgba(255, 255, 255, 0.5)'
          />
          <Pricing />
        </Container>

        <Reviews />
      </Wrapper>

      {/* </Background> */}
    </>
  )
}

export default PircingPage
