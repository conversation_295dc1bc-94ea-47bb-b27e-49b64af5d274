"use client"

import { useMutation, useQuery } from "@tanstack/react-query"
import { usePara<PERSON>, useRouter } from "next/navigation"
import { toast } from "sonner"

import { createBlog, fetchBlogById, updateBlog } from "@/actions/blogs" // Assuming you have these API functions
import { BlogForm } from "@/components/forms/blog-form"
import { Container } from "@/components/layout"
import { BlogFormData } from "@/types/blog"

export default function CreateEditBlogPage() {
  const router = useRouter()
  const { id } = useParams<{ id: string }>()

  // Fetch blog data if editing (not creating)
  const {
    data: blogData,
    isLoading,
    isError,
  } = useQuery({
    queryKey: ["blog", id],
    queryFn: () => fetchBlogById(id!),
    enabled: !!id && id !== "new", // Only run if we are editing an existing blog
  })

  // Mutation for creating or updating a blog
  const { mutate } = useMutation({
    mutationFn: id === "new" ? createBlog : updateBlog,
    onSuccess: (data) => {
      if (data.success) {
        toast.success(
          `Blog ${id === "new" ? "created" : "updated"} successfully`,
        )
        // Redirect to blogs list after creating or updating
        router.push("/dashboard/blogs")
      } else {
        toast.error("Error creating/updating blog")
      }
    },
  })

  const handleSubmit = async (data: BlogFormData) => {
    if (id === "new") {
      // Create new blog
      mutate(data)
    } else {
      // Update existing blog
      mutate({ ...data, _id: id! })
    }
  }

  return (
    <Container className='p-5 md:p-10'>
      <div className='mb-8 flex items-center justify-between'>
        <div>
          <h1 className='text-2xl font-bold'>
            {id === "new" ? "Create New Blog Post" : "Edit Blog Post"}
          </h1>
          <p className='mt-1 text-sm text-muted-foreground'>
            {id === "new"
              ? "Share your thoughts with the world"
              : "Update your blog post content"}
          </p>
        </div>
        <button
          onClick={() => router.back()}
          className='rounded-md border px-4 py-2 text-sm font-medium transition-colors hover:bg-secondary'
        >
          Back
        </button>
      </div>

      {isLoading ? (
        <div className='flex h-[400px] w-full items-center justify-center'>
          <div className='flex flex-col items-center gap-2'>
            <div className='h-8 w-8 animate-spin rounded-full border-2 border-primary border-t-transparent' />
            <p className='text-sm text-muted-foreground'>
              Loading blog data...
            </p>
          </div>
        </div>
      ) : isError || (blogData && !blogData.success) ? (
        <div className='flex h-[400px] w-full flex-col items-center justify-center rounded-lg border border-dashed'>
          <p className='text-xl font-medium text-destructive'>
            Error fetching blog data
          </p>
          <p className='text-muted-foreground'>
            Please try again or contact support
          </p>
          <button
            onClick={() => router.push("/dashboard/blogs")}
            className='mt-4 rounded-md bg-primary px-4 py-2 text-sm font-medium text-primary-foreground'
          >
            Return to Blogs
          </button>
        </div>
      ) : (
        <div className='rounded-lg border bg-card p-6 shadow-sm'>
          <BlogForm
            initialData={blogData?.data} // Populate form with existing data for edit or empty for new
            onSubmit={handleSubmit}
          />
        </div>
      )}
    </Container>
  )
}
