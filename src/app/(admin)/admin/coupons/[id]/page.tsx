"use client"

import { useMutation, useQuery } from "@tanstack/react-query"
import { usePara<PERSON>, useRouter } from "next/navigation"
import { toast } from "sonner"

import { createCoupon, fetchCouponById, updateCoupon } from "@/actions/coupons" // Assuming you have these API functions
import { CouponForm } from "@/components/forms/coupon-form"
import { Container } from "@/components/layout"
import { CouponFormData } from "@/types/coupon"

export default function CreateEditCouponPage() {
  const router = useRouter()
  const { id } = useParams<{ id: string }>()

  // Fetch coupon data if editing (not creating)
  const {
    data: couponData,
    isLoading,
    isError,
  } = useQuery({
    queryKey: ["coupon", id],
    queryFn: () => fetchCouponById(id!),
    enabled: !!id && id !== "new", // Only run if we are editing an existing coupon
  })

  // Mutation for creating or updating a coupon
  const { mutate, data, error, isPending, isSuccess } = useMutation({
    mutationFn: id === "new" ? createCoupon : updateCoupon,
    onSuccess: (data) => {
      console.log(data)

      if (data.success) {
        toast.success(
          `Coupon ${id === "new" ? "created" : "updated"} successfully`,
        )
        router.push("/dashboard/coupons") // Redirect to coupons list after creating or updating
      } else {
        toast.error(`Error ${id === "new" ? "creating" : "updating"} coupon`)
      }
    },
  })

  const handleSubmit = async (data: CouponFormData) => {
    if (id === "new") {
      // Create new coupon
      mutate(data)
    } else {
      // Update existing coupon
      mutate({ ...data, _id: id! })
    }
  }

  if (isLoading) {
    return <div>Loading...</div> // Show loading state while fetching
  }

  if (isError) {
    return <div>Error fetching coupon data</div> // Handle error state
  }
  if (couponData && !couponData.success) {
    return <div>Error fetching coupon data</div> // Handle error state
  }

  return (
    <Container className='p-5 md:p-10'>
      <h1 className='mb-5 text-2xl font-bold'>
        {id === "new" ? "Create New Coupon" : "Edit Coupon"}
      </h1>
      <CouponForm
        initialData={couponData?.data} // Populate form with existing data for edit or empty for new
        onSubmit={handleSubmit}
      />
    </Container>
  )
}
