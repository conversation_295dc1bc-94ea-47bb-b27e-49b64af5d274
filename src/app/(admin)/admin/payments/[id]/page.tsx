"use client"

import { useMutation, useQuery } from "@tanstack/react-query"
import { usePara<PERSON>, useRouter } from "next/navigation"
import { toast } from "sonner"

import {
  createPayment,
  fetchPaymentById,
  updatePayment,
} from "@/actions/payments" // Assuming you have these API functions
import { PaymentForm } from "@/components/forms/payment-form"
import { Container } from "@/components/layout"
import { PaymentFormData } from "@/types/payment"

export default function CreateEditPaymentPage() {
  const router = useRouter()
  const { id } = useParams<{ id: string }>()

  // Fetch payment data if editing (not creating)
  const {
    data: paymentData,
    isLoading,
    isError,
  } = useQuery({
    queryKey: ["payment", id],
    queryFn: () => fetchPaymentById(id!),
    enabled: !!id && id !== "new", // Only run if we are editing an existing payment
  })

  // Mutation for creating or updating a payment
  const { mutate, data, error, isPending, isSuccess } = useMutation({
    mutationFn: id === "new" ? createPayment : updatePayment,
    onSuccess: (data) => {
      if (data.success) {
        toast.success(
          `Payment ${id === "new" ? "created" : "updated"} successfully`,
        )
        router.push("/dashboard/payments") // Redirect to orders list after creating or updating
      } else {
        toast.error(`Error ${id === "new" ? "creating" : "updating"} Payment`)
      }
    },
  })

  const handleSubmit = async (data: PaymentFormData) => {
    if (id === "new") {
      // Create new payment
      mutate(data)
    } else {
      // Update existing payment
      mutate({ ...data, _id: id! })
    }
  }

  if (isLoading) {
    return <div>Loading...</div> // Show loading state while fetching
  }

  if (isError) {
    return <div>Error fetching payment data</div> // Handle error state
  }
  if (paymentData && !paymentData.success) {
    return <div>Error fetching payment data</div> // Handle error state
  }

  return (
    <Container className='p-5 md:p-10'>
      <h1 className='mb-5 text-2xl font-bold'>
        {id === "new" ? "Create New Payment" : "Edit Payment"}
      </h1>
      <PaymentForm
        initialData={paymentData?.data} // Populate form with existing data for edit or empty for new
        onSubmit={handleSubmit}
      />
    </Container>
  )
}
