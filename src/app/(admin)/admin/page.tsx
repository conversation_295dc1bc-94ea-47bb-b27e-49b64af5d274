"use client"

import {
  DollarSignIcon,
  Share2Icon,
  ShoppingCartIcon,
  UsersIcon,
} from "lucide-react"

import { ActivityCard } from "@/components/cards/activity-card"
import { ChartCard } from "@/components/cards/chart-card"
import { SalesCard } from "@/components/cards/sales-card"
import { StatsCard } from "@/components/cards/stats-card"
import { ACTIVITIES, ANALYTICS_DATA, RECENT_SALES } from "@/constants/dashboard"

const chartConfig = {
  reach: {
    label: "Total Reach",
    color: "hsl(var(--primary))",
  },
  engagement: {
    label: "Engagement",
    color: "hsl(var(--blue-500))",
  },
  conversion: {
    label: "Conversion",
    color: "hsl(var(--green-500))",
  },
}

const Page = () => {
  return (
    <div className='w-full p-4 md:p-6'>
      <div className='flex w-full flex-col'>
        <div className='mb-8'>
          <h1 className='text-3xl font-bold tracking-tight'>Dashboard</h1>
          <p className='mt-1 text-muted-foreground'>
            Welcome back! Here&apos;s your business overview for today.
          </p>
        </div>

        {/* Stats Cards */}
        <div className='mt-4 grid gap-4 md:grid-cols-2 lg:grid-cols-4'>
          <StatsCard
            title='Total Reach'
            value='2.4M'
            change={{ value: "+20.1%", trend: "up" }}
            icon={UsersIcon}
            iconColor='primary'
          />
          <StatsCard
            title='Engagement Rate'
            value='4.3%'
            change={{ value: "+1.2%", trend: "up" }}
            icon={Share2Icon}
            iconColor='blue-500'
            delay={0.1}
          />
          <StatsCard
            title='Total Revenue'
            value='$24,389'
            change={{ value: "-2.5%", trend: "down" }}
            icon={DollarSignIcon}
            iconColor='green-500'
            delay={0.2}
          />
          <StatsCard
            title='Total Orders'
            value='842'
            change={{ value: "+48", trend: "up" }}
            icon={ShoppingCartIcon}
            iconColor='orange-500'
            delay={0.3}
          />
        </div>

        <div className='mt-8 grid grid-cols-1 gap-6 lg:grid-cols-7'>
          {/* Chart */}
          <ChartCard
            title='Performance Overview'
            subtitle='Track your key metrics over time'
            data={ANALYTICS_DATA}
            config={chartConfig}
            delay={0.2}
          />

          {/* Recent Activities and Sales */}
          <div className='col-span-full lg:col-span-3'>
            <div className='grid gap-6'>
              <ActivityCard activities={ACTIVITIES} delay={0.3} />
              <SalesCard
                sales={RECENT_SALES}
                totalSales='$5,489'
                percentageIncrease='+12.3%'
                delay={0.3}
              />
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}

export default Page
