"use client"

import { useMutation, useQuery } from "@tanstack/react-query"
import { usePara<PERSON>, useRouter } from "next/navigation"
import { toast } from "sonner"

import { createOrder, fetchOrderById, updateOrder } from "@/actions/orders" // Assuming you have these API functions
import { OrderForm } from "@/components/forms/order-form"
import { Container } from "@/components/layout"
import { OrderFormData } from "@/types/order"

export default function CreateEditOrderPage() {
  const router = useRouter()
  const { id } = useParams<{ id: string }>()

  // Fetch order data if editing (not creating)
  const {
    data: orderData,
    isLoading,
    isError,
  } = useQuery({
    queryKey: ["order", id],
    queryFn: () => fetchOrderById(id!),
    enabled: !!id && id !== "new", // Only run if we are editing an existing order
  })

  // Mutation for creating or updating an order
  const { mutate, data, error, isPending, isSuccess } = useMutation({
    mutationFn: id === "new" ? createOrder : updateOrder,
    onSuccess: (data) => {
      if (data.success) {
        toast.success(
          `Order ${id === "new" ? "created" : "updated"} successfully`,
        )
        router.push("/dashboard/orders") // Redirect to orders list after creating or updating
      } else {
        toast.error(`Error ${id === "new" ? "creating" : "updating"} Order`)
      }
    },
  })

  const handleSubmit = async (data: OrderFormData) => {
    if (id === "new") {
      // Create new order
      mutate(data)
    } else {
      // Update existing order
      mutate({ ...data, _id: id! })
    }
  }

  if (isLoading) {
    return <div>Loading...</div> // Show loading state while fetching
  }

  if (isError) {
    return <div>Error fetching order data</div> // Handle error state
  }
  if (orderData && !orderData.success) {
    return <div>Error fetching order data</div> // Handle error state
  }

  return (
    <Container className='p-5 md:p-10'>
      <h1 className='mb-5 text-2xl font-bold'>
        {id === "new" ? "Create New Order" : "Edit Order"}
      </h1>
      <OrderForm
        initialData={orderData?.data} // Populate form with existing data for edit or empty for new
        onSubmit={handleSubmit}
      />
    </Container>
  )
}
