"use client"

import { useMutation, useQuery } from "@tanstack/react-query"
import { usePara<PERSON>, useRouter } from "next/navigation"
import { toast } from "sonner"

import { createPlan, fetchPlanById, updatePlan } from "@/actions/plans" // Assuming you have these API functions
import { PlanForm } from "@/components/forms/plan-form"
import { Container } from "@/components/layout"
import { PlanFormData } from "@/types/plan"

export default function CreateEditPlanPage() {
  const router = useRouter()
  const { id } = useParams<{ id: string }>()
  // const { getToken } = useAuth();
  // Fetch plan data if editing (not creating)
  const {
    data: planData,
    isLoading,
    isError,
  } = useQuery({
    queryKey: ["plan", id],
    queryFn: () => fetchPlanById(id!),
    enabled: !!id && id !== "new", // Only run if we are editing an existing plan
  })

  // Mutation for creating or updating a plan
  const { mutate, data, error, isPending, isSuccess } = useMutation({
    // mutationFn: id === "new" ? createPlan : updatePlan,
    mutationFn: (planData: PlanFormData & { _id?: string }) =>
      id === "new"
        ? createPlan(planData)
        : updatePlan({ ...planData, _id: id! }),

    onSuccess: (data) => {
      if (data.success) {
        toast.success(
          `Plan ${id === "new" ? "created" : "updated"} successfully`,
        )
        router.push("/dashboard/plans") // Redirect to orders list after creating or updating
      } else {
        toast.error(`Error ${id === "new" ? "creating" : "updating"} Payment`)
      }
    },
  })

  const handleSubmit = (data: PlanFormData) => {
    mutate(data)
  }

  if (isLoading) {
    return <div>Loading...</div> // Show loading state while fetching
  }

  if (isError) {
    return <div>Error fetching plan data</div> // Handle error state
  }
  if (planData && !planData.success) {
    return <div>Error fetching plan data</div> // Handle error state
  }

  return (
    <Container className='p-5 md:p-10'>
      <h1 className='mb-5 text-2xl font-bold'>
        {id === "new" ? "Create New Plan" : "Edit Plan"}
      </h1>
      <PlanForm
        initialData={planData?.data} // Populate form with existing data for edit or empty for new
        onSubmit={handleSubmit}
      />
    </Container>
  )
}
