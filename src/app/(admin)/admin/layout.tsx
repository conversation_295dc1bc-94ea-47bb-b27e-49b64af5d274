"use client"
import React from "react"

import DashboardNavbar from "@/components/dashboard/dashboard-navbar"
import DashboardSidebar from "@/components/dashboard/dashboard-sidebar"

interface Props {
  children: React.ReactNode
}

const DashboardLayout = ({ children }: Props) => {
  return (
    <div className='flex min-h-screen w-full flex-col'>
      <DashboardNavbar />
      <main className='flex size-full flex-1 flex-col lg:flex-row'>
        <DashboardSidebar />
        <div className='w-full pt-14 lg:ml-72 lg:max-w-[calc(100%-18rem)]'>
          {children}
        </div>
      </main>
    </div>
  )
}

export default DashboardLayout
