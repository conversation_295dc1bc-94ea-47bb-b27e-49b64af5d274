"use client"

import { useMutation, useQuery } from "@tanstack/react-query"
import { usePara<PERSON>, useRouter } from "next/navigation"
import { toast } from "sonner"

import {
  createSubscription,
  fetchSubscriptionById,
  updateSubscription,
} from "@/actions/subscriptions" // Assuming you have these API functions
import { SubscriptionForm } from "@/components/forms/subscription-form"
import { Container } from "@/components/layout"
import { SubscriptionFormData } from "@/types/subscription"

export default function CreateEditSubscriptionPage() {
  const router = useRouter()
  const { id } = useParams<{ id: string }>()

  // Fetch subscription data if editing (not creating)
  const {
    data: subscriptionData,
    isLoading,
    isError,
  } = useQuery({
    queryKey: ["subscription", id],
    queryFn: () => fetchSubscriptionById(id!),
    enabled: !!id && id !== "new", // Only run if we are editing an existing subscription
  })

  // Mutation for creating or updating a subscription
  const { mutate, data, error, isPending, isSuccess } = useMutation({
    mutationFn: id === "new" ? createSubscription : updateSubscription,
    onSuccess: (data) => {
      if (data.success) {
        toast.success(
          `Subscription ${id === "new" ? "created" : "updated"} successfully`,
        )
        router.push("/dashboard/subscriptions") // Redirect to products list after creating or updating
      } else {
        // toast.error("Error creating or updating product");
        toast.error(
          `Error ${id === "new" ? "creating" : "updating"} Subscription`,
        )
        toast.error(data.message)
      }
    },
  })

  const handleSubmit = async (data: SubscriptionFormData) => {
    if (id === "new") {
      // Create new subscription
      mutate(data)
    } else {
      // Update existing subscription
      mutate({ ...data, _id: id! })
    }
  }

  if (isLoading) {
    return <div>Loading...</div> // Show loading state while fetching
  }

  if (isError) {
    return <div>Error fetching subscription data</div> // Handle error state
  }
  if (subscriptionData && !subscriptionData.success) {
    return <div>Error fetching subscription data</div> // Handle error state
  }

  return (
    <Container className='p-5 md:p-10'>
      <h1 className='mb-5 text-2xl font-bold'>
        {id === "new" ? "Create New Subscription" : "Edit Subscription"}
      </h1>
      <SubscriptionForm
        initialData={subscriptionData?.data} // Populate form with existing data for edit or empty for new
        onSubmit={handleSubmit}
      />
    </Container>
  )
}
