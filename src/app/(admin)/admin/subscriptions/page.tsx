"use client"
import { useQuery, useQueryClient } from "@tanstack/react-query"
import { Filter, Grid3X3, List, Plus, Search } from "lucide-react"
import Link from "next/link"
import { useRouter, useSearchParams } from "next/navigation"
import { Suspense, useState } from "react"
import { toast } from "sonner"

import { fetchSubscriptionsWithPagination } from "@/actions/subscriptions"
import { SubscriptionCard } from "@/components/cards"
import { Container } from "@/components/layout"
import { DataTable } from "@/components/tables/reusable-table"
import { columns } from "@/components/tables/subscriptions-table/columns"
import { Button } from "@/components/ui/button"
import { Heading } from "@/components/ui/heading"
import { Input } from "@/components/ui/input"
import { Separator } from "@/components/ui/separator"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { Subscription } from "@/types/subscription"

const SubscriptionsPage = () => {
  return (
    <Container>
      <div className='flex-1 space-y-4 p-4 pt-6 md:p-8'>
        <div className='flex items-start justify-between'>
          <Heading
            title={`Subscriptions`}
            description='Manage Subscriptions easily from here'
          />
          <Link href={"/dashboard/subscriptions/new"}>
            <Button className='text-xs md:text-sm'>
              <Plus className='mr-2 h-4 w-4' /> Add New
            </Button>
          </Link>
        </div>
        <Separator />

        <Suspense>
          <Subscriptions />
        </Suspense>
      </div>
    </Container>
  )
}

const Subscriptions = () => {
  const router = useRouter()
  const queryClient = useQueryClient()
  const searchParams = useSearchParams()
  const pageNumber = Number(searchParams.get("page")) || 1
  const pageLimit = Number(searchParams.get("limit")) || 12
  const [viewMode, setViewMode] = useState<"grid" | "list">("grid")
  const [searchQuery, setSearchQuery] = useState<string>("")

  const {
    data: subscriptionResult,
    isLoading: subscriptionsLoading,
    isError: subscriptionsError,
  } = useQuery({
    queryKey: ["subscriptions", pageNumber, pageLimit],
    queryFn: async () =>
      await fetchSubscriptionsWithPagination(pageNumber, pageLimit),
  })

  // Mock delete function - replace with actual implementation
  const handleDeleteSubscription = (id: string) => {
    if (window.confirm("Are you sure you want to delete this subscription?")) {
      // Implement actual delete functionality
      console.log(`Deleting subscription with ID: ${id}`)
      toast.success("Subscription deleted successfully")
      // Refresh data
      queryClient.invalidateQueries({ queryKey: ["subscriptions"] })
    }
  }

  const { data, totalDocuemts, currentPage, totalPages } =
    subscriptionResult || {}
  const subscriptions = data || []

  // Filter subscriptions based on search query
  const filteredSubscriptions = searchQuery.trim()
    ? subscriptions.filter(
        (subscription: Subscription) =>
          subscription._id?.toLowerCase().includes(searchQuery.toLowerCase()) ||
          subscription.userId
            ?.toLowerCase()
            .includes(searchQuery.toLowerCase()) ||
          subscription.planId
            ?.toLowerCase()
            .includes(searchQuery.toLowerCase()) ||
          subscription.paymentStatus
            ?.toLowerCase()
            .includes(searchQuery.toLowerCase()),
      )
    : subscriptions

  if (subscriptionsError) {
    console.error("Error fetching subscriptions:", subscriptionsError)
  }

  return (
    <Container>
      <div className='mb-6 flex flex-col gap-4 sm:flex-row sm:items-center sm:justify-between'>
        <div className='relative w-full sm:max-w-xs'>
          <Search className='absolute left-2.5 top-2.5 h-4 w-4 text-muted-foreground' />
          <Input
            placeholder='Search subscriptions...'
            className='w-full pl-8'
            value={searchQuery}
            onChange={(e: React.ChangeEvent<HTMLInputElement>) =>
              setSearchQuery(e.target.value)
            }
          />
        </div>
        <div className='flex items-center gap-2'>
          <Button variant='outline' size='icon' className='h-9 w-9'>
            <Filter className='h-4 w-4' />
          </Button>
          <Tabs
            defaultValue={viewMode}
            className='w-[120px]'
            onValueChange={(value: string) =>
              setViewMode(value as "grid" | "list")
            }
          >
            <TabsList className='grid w-full grid-cols-2'>
              <TabsTrigger
                value='grid'
                className='flex items-center justify-center'
              >
                <Grid3X3 className='h-4 w-4' />
              </TabsTrigger>
              <TabsTrigger
                value='list'
                className='flex items-center justify-center'
              >
                <List className='h-4 w-4' />
              </TabsTrigger>
            </TabsList>
          </Tabs>
        </div>
      </div>

      <Tabs
        defaultValue={viewMode}
        className='w-full'
        value={viewMode}
        onValueChange={(value: string) => setViewMode(value as "grid" | "list")}
      >
        <TabsContent value='grid' className='mt-0'>
          {subscriptionsLoading ? (
            <div className='grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4'>
              {Array.from({ length: 8 }).map((_, index) => (
                <div
                  key={index}
                  className='h-[250px] animate-pulse rounded-xl bg-muted'
                />
              ))}
            </div>
          ) : filteredSubscriptions.length > 0 ? (
            <div className='grid grid-cols-1 gap-4 sm:grid-cols-2 md:grid-cols-3 lg:grid-cols-4'>
              {filteredSubscriptions.map((subscription: Subscription) => (
                <SubscriptionCard
                  key={subscription._id}
                  subscription={subscription}
                  onDelete={handleDeleteSubscription}
                />
              ))}
            </div>
          ) : (
            <div className='flex h-[400px] w-full flex-col items-center justify-center rounded-lg border border-dashed'>
              <p className='text-xl font-medium'>No subscriptions found</p>
              <p className='text-muted-foreground'>
                Try adjusting your search or filters
              </p>
            </div>
          )}
        </TabsContent>

        <TabsContent value='list' className='mt-0'>
          <DataTable<typeof data, Subscription>
            pageNo={currentPage || 1}
            columns={columns}
            data={filteredSubscriptions}
            searchKey='userId'
            isLoading={subscriptionsLoading}
            pageCount={totalPages || 1}
            totalCount={totalDocuemts || filteredSubscriptions.length}
          />
        </TabsContent>
      </Tabs>

      {viewMode === "grid" && filteredSubscriptions.length > 0 && (
        <div className='mt-8 flex items-center justify-center'>
          <div className='flex items-center space-x-2'>
            <Button
              variant='outline'
              size='sm'
              onClick={() => {
                if (currentPage > 1) {
                  router.push(
                    `/dashboard/subscriptions?page=${currentPage - 1}&limit=${pageLimit}`,
                  )
                }
              }}
              disabled={currentPage <= 1}
            >
              Previous
            </Button>
            <span className='text-sm text-muted-foreground'>
              Page {currentPage || 1} of {totalPages || 1}
            </span>
            <Button
              variant='outline'
              size='sm'
              onClick={() => {
                if (currentPage < (totalPages || 1)) {
                  router.push(
                    `/dashboard/subscriptions?page=${currentPage + 1}&limit=${pageLimit}`,
                  )
                }
              }}
              disabled={currentPage >= (totalPages || 1)}
            >
              Next
            </Button>
          </div>
        </div>
      )}
    </Container>
  )
}

export default SubscriptionsPage
