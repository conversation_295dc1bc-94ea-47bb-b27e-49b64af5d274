type REVIEW = {
  name: string
  username: string
  review: string
  img: string
}

export const REVIEWS: REVIEW[] = [
  {
    name: "<PERSON><PERSON><PERSON>",
    username: "@a<PERSON><PERSON><PERSON><PERSON>",
    review:
      "DesignByte has transformed the way I build websites. The templates are top-notch and customizable!",
    img: "https://randomuser.me/api/portraits/men/1.jpg",
  },
  {
    name: "<PERSON><PERSON> <PERSON>",
    username: "@priyanair",
    review:
      "I love the variety of design resources available. It’s made my mobile app development so much faster.",
    img: "https://randomuser.me/api/portraits/women/2.jpg",
  },
  {
    name: "<PERSON><PERSON><PERSON>",
    username: "@rohan<PERSON><PERSON>",
    review:
      "The starter kits for <PERSON><PERSON> and <PERSON><PERSON> are a lifesaver. They saved me hours of work on my projects.",
    img: "https://randomuser.me/api/portraits/men/3.jpg",
  },
  {
    name: "<PERSON><PERSON><PERSON> <PERSON>",
    username: "@snehapatel",
    review:
      "The customization options available on DesignByte are fantastic. It’s perfect for personalizing my projects.",
    img: "https://randomuser.me/api/portraits/women/4.jpg",
  },
  {
    name: "<PERSON><PERSON><PERSON>",
    username: "@ankitsharma",
    review:
      "DesignByte’s platform is incredibly easy to use. I can integrate all my resources with ease.",
    img: "https://randomuser.me/api/portraits/men/5.jpg",
  },
  {
    name: "Meera Kapoor",
    username: "@meerakapoor",
    review:
      "The range of resources available is amazing. Whether it’s Figma designs or mobile app templates, everything is top quality.",
    img: "https://randomuser.me/api/portraits/women/6.jpg",
  },
  {
    name: "Vikram Desai",
    username: "@vikramdesai",
    review:
      "The time-saving templates and resources on DesignByte have helped me streamline my workflow and boost productivity.",
    img: "https://randomuser.me/api/portraits/men/7.jpg",
  },
  {
    name: "Anjali Menon",
    username: "@anjalimenon",
    review:
      "I’m blown away by the quality of the templates and the regular updates. DesignByte has become an essential tool for me.",
    img: "https://randomuser.me/api/portraits/women/8.jpg",
  },
  {
    name: "Karan Gupta",
    username: "@karangupta",
    review:
      "The ability to customize templates with ease has been a game-changer. DesignByte is now my go-to platform.",
    img: "https://randomuser.me/api/portraits/men/9.jpg",
  },
  {
    name: "Neha Verma",
    username: "@nehaverma",
    review:
      "Everything I need in one place—templates, starter kits, and resources that help me develop faster.",
    img: "https://randomuser.me/api/portraits/women/10.jpg",
  },
  {
    name: "Siddharth Jain",
    username: "@siddharthjain",
    review:
      "DesignByte’s resources are of the highest quality. The customizable nature of everything has sped up my projects.",
    img: "https://randomuser.me/api/portraits/men/11.jpg",
  },
  {
    name: "Divya Iyer",
    username: "@divyaiyer",
    review:
      "The regular updates with new templates and tools keep me ahead of the curve in my design and development projects.",
    img: "https://randomuser.me/api/portraits/women/12.jpg",
  },
]
