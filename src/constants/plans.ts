type PLAN = {
  id: string
  title: string
  desc: string
  monthlyPrice: number
  yearlyPrice: number
  badge?: string
  buttonText: string
  features: string[]
  link: string
}

export const PLANS: PLAN[] = [
  {
    id: "free",
    title: "Free",
    desc: "Start building with essential resources and templates",
    monthlyPrice: 0,
    yearlyPrice: 0,
    buttonText: "Get Started",
    features: [
      "Access to basic templates",
      "Limited customization options",
      "Community support",
      "1 project limit",
      "Standard design assets",
    ],
    link: "https://stripe.com/free-plan-link",
  },
  {
    id: "pro",
    title: "Pro",
    desc: "Unlock advanced tools, templates, and resources for your projects",
    monthlyPrice: 10,
    yearlyPrice: 120,
    badge: "Most Popular",
    buttonText: "Upgrade to Pro",
    features: [
      "Access to premium templates",
      "Advanced customization options",
      "Priority email support",
      "10 project limit",
      "Extended design asset library",
      "Team collaboration tools",
      "Custom branding options",
    ],
    link: "https://stripe.com/pro-plan-link",
  },
  {
    id: "enterprise",
    title: "Enterprise",
    desc: "Tailored solutions for teams, agencies, and large organizations",
    monthlyPrice: 15,
    yearlyPrice: 180,
    badge: "Contact Sales",
    buttonText: "Upgrade to Enterprise",
    features: [
      "Unlimited access to all templates",
      "Unlimited customization options",
      "Dedicated account manager",
      "Unlimited projects",
      "Custom design assets & tools",
      "Enterprise-grade security",
      "Free updates",
      "Priority support",
    ],
    link: "https://stripe.com/enterprise-plan-link",
  },
]
