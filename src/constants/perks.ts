import {
  ChartSplineIcon,
  LifeBuoyIcon,
  PaletteIcon,
  ShieldCheckIcon,
  WaypointsIcon,
  ZapIcon,
} from "lucide-react"

export const PERKS = [
  {
    icon: ZapIcon,
    title: "Type-Safe Development",
    description:
      "Resources designed for type safety, providing better developer experience, fewer errors, and improved code reliability.",
  },
  {
    icon: ChartSplineIcon,
    title: "Beautiful UI Components",
    description:
      "Responsive, accessible interfaces with consistent design systems that adapt perfectly to any screen size.",
  },
  {
    icon: LifeBuoyIcon,
    title: "Quality Assurance Built-in",
    description:
      "Every resource includes code quality tools and pre-commit hooks to maintain consistent standards across your project.",
  },
  {
    icon: PaletteIcon,
    title: "Modern Development Practices",
    description:
      "Resources built with the latest frameworks and tools, incorporating industry best practices for optimal performance.",
  },
  {
    icon: ShieldCheckIcon,
    title: "Ready for Production",
    description:
      "Thoroughly tested resources with proper testing setups included to ensure reliability and maintainability of your projects.",
  },
  {
    icon: WaypointsIcon,
    title: "Complete Resource Packages",
    description:
      "Get templates, starter kits, themes, and Figma designs that work seamlessly together for a complete development workflow.",
  },
]
