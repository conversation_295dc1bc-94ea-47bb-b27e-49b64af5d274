export interface Technology {
  name: string
  logo: string
  description: string
  color: string
  url: string
  category: string
}

export interface TechnologyCategory {
  title: string
  description: string
  icon: string
  technologies: Technology[]
}

export const technologiesData: TechnologyCategory[] = [
  {
    title: "Frontend Development",
    description:
      "Modern frameworks and libraries for building interactive user interfaces",
    icon: "🎨",
    technologies: [
      {
        name: "React",
        logo: "https://cdn.jsdelivr.net/gh/devicons/devicon/icons/react/react-original.svg",
        description:
          "JavaScript library for building user interfaces with component-based architecture",
        color: "#61DAFB",
        url: "https://react.dev/",
        category: "frontend",
      },
      {
        name: "Next.js",
        logo: "https://cdn.jsdelivr.net/gh/devicons/devicon/icons/nextjs/nextjs-original.svg",
        description: "Full-stack React framework with SSR, SSG, and API routes",
        color: "#000000",
        url: "https://nextjs.org/",
        category: "frontend",
      },
      {
        name: "TypeScript",
        logo: "https://cdn.jsdelivr.net/gh/devicons/devicon/icons/typescript/typescript-original.svg",
        description:
          "Strongly typed superset of JavaScript for better code quality",
        color: "#3178C6",
        url: "https://www.typescriptlang.org/",
        category: "frontend",
      },
      {
        name: "Tailwind CSS",
        logo: "https://cdn.jsdelivr.net/gh/devicons/devicon/icons/tailwindcss/tailwindcss-original.svg",
        description: "Utility-first CSS framework for rapid UI development",
        color: "#38B2AC",
        url: "https://tailwindcss.com/",
        category: "frontend",
      },
      {
        name: "Shadcn UI",
        logo: "https://ui.shadcn.com/apple-touch-icon.png",
        description:
          "Beautifully designed components built with Radix UI and Tailwind CSS",
        color: "#000000",
        url: "https://ui.shadcn.com/",
        category: "frontend",
      },
      {
        name: "Radix UI",
        logo: "https://workos.imgix.net/images/54cdf174-0a37-4d9a-92ad-53d18370ad9f.png",
        description:
          "Low-level UI primitives for building accessible design systems",
        color: "#111111",
        url: "https://www.radix-ui.com/",
        category: "frontend",
      },
      {
        name: "Framer Motion",
        logo: "https://www.framer.com/images/favicons/128.png",
        description:
          "Production-ready motion library for React with powerful animations",
        color: "#0055FF",
        url: "https://www.framer.com/motion/",
        category: "frontend",
      },
    ],
  },
  {
    title: "Backend & Database",
    description:
      "Server-side technologies and database solutions for robust applications",
    icon: "⚙️",
    technologies: [
      {
        name: "Node.js",
        logo: "https://cdn.jsdelivr.net/gh/devicons/devicon/icons/nodejs/nodejs-original.svg",
        description:
          "JavaScript runtime built on Chrome's V8 engine for server-side development",
        color: "#339933",
        url: "https://nodejs.org/",
        category: "backend",
      },
      {
        name: "Prisma",
        logo: "https://cdn.jsdelivr.net/gh/devicons/devicon/icons/prisma/prisma-original.svg",
        description:
          "Next-generation ORM with type safety and auto-generated client",
        color: "#2D3748",
        url: "https://www.prisma.io/",
        category: "backend",
      },
      {
        name: "PostgreSQL",
        logo: "https://cdn.jsdelivr.net/gh/devicons/devicon/icons/postgresql/postgresql-original.svg",
        description:
          "Advanced open-source relational database with powerful features",
        color: "#336791",
        url: "https://www.postgresql.org/",
        category: "backend",
      },
      {
        name: "MongoDB",
        logo: "https://cdn.jsdelivr.net/gh/devicons/devicon/icons/mongodb/mongodb-original.svg",
        description:
          "NoSQL document database for flexible and scalable applications",
        color: "#47A248",
        url: "https://www.mongodb.com/",
        category: "backend",
      },
      {
        name: "Supabase",
        logo: "https://supabase.com/favicon/favicon-96x96.png",
        description:
          "Open-source Firebase alternative with PostgreSQL and real-time features",
        color: "#3ECF8E",
        url: "https://supabase.com/",
        category: "backend",
      },
      {
        name: "Vercel",
        logo: "https://assets.vercel.com/image/upload/front/favicon/vercel/152x152.png",
        description:
          "Platform for frontend frameworks with global edge network",
        color: "#000000",
        url: "https://vercel.com/",
        category: "backend",
      },
    ],
  },
  {
    title: "Authentication & Security",
    description:
      "Secure authentication solutions and security tools for modern applications",
    icon: "🔐",
    technologies: [
      {
        name: "Clerk",
        logo: "https://clerk.com/favicon.ico",
        description: "Complete authentication and user management platform",
        color: "#6C47FF",
        url: "https://clerk.dev/",
        category: "auth",
      },
      {
        name: "NextAuth.js",
        logo: "https://next-auth.js.org/img/logo/logo-sm.png",
        description: "Complete authentication library for Next.js applications",
        color: "#000000",
        url: "https://next-auth.js.org/",
        category: "auth",
      },
      {
        name: "Better Auth",
        logo: "https://www.better-auth.com/favicon.ico",
        description:
          "Modern authentication library with TypeScript-first approach",
        color: "#FF6B35",
        url: "https://www.better-auth.com/",
        category: "auth",
      },
      {
        name: "Supabase Auth",
        logo: "https://supabase.com/favicon/favicon-96x96.png",
        description:
          "Built-in authentication with social providers and row-level security",
        color: "#3ECF8E",
        url: "https://supabase.com/auth",
        category: "auth",
      },
      {
        name: "Auth0",
        logo: "https://cdn.auth0.com/website/auth0_favicon.ico",
        description:
          "Identity platform with single sign-on and multi-factor authentication",
        color: "#EB5424",
        url: "https://auth0.com/",
        category: "auth",
      },
    ],
  },
  {
    title: "Development Tools",
    description:
      "Essential tools for code quality, testing, and development workflow",
    icon: "🛠️",
    technologies: [
      {
        name: "ESLint",
        logo: "https://cdn.jsdelivr.net/gh/devicons/devicon/icons/eslint/eslint-original.svg",
        description: "Pluggable linting utility for JavaScript and TypeScript",
        color: "#4B32C3",
        url: "https://eslint.org/",
        category: "tools",
      },
      {
        name: "Prettier",
        logo: "https://prettier.io/icon.png",
        description: "Opinionated code formatter for consistent code style",
        color: "#F7B93E",
        url: "https://prettier.io/",
        category: "tools",
      },
      {
        name: "Husky",
        logo: "https://typicode.github.io/husky/dog.svg",
        description: "Git hooks made easy for better commit workflow",
        color: "#42B883",
        url: "https://typicode.github.io/husky/",
        category: "tools",
      },
      {
        name: "Jest",
        logo: "https://cdn.jsdelivr.net/gh/devicons/devicon/icons/jest/jest-plain.svg",
        description:
          "JavaScript testing framework with built-in test runner and assertions",
        color: "#C21325",
        url: "https://jestjs.io/",
        category: "tools",
      },
      {
        name: "Vite",
        logo: "https://vitejs.dev/logo.svg",
        description: "Next generation frontend tooling with lightning fast HMR",
        color: "#646CFF",
        url: "https://vitejs.dev/",
        category: "tools",
      },
      {
        name: "Turbo",
        logo: "https://turbo.build/favicon/favicon-32x32.png",
        description:
          "High-performance build system for JavaScript and TypeScript codebases",
        color: "#000000",
        url: "https://turbo.build/",
        category: "tools",
      },
    ],
  },
  {
    title: "State Management & Data",
    description: "Tools for managing application state and data fetching",
    icon: "📊",
    technologies: [
      {
        name: "Zustand",
        logo: "https://docs.pmnd.rs/apple-touch-icon.png",
        description: "Small, fast, and scalable state management solution",
        color: "#FF6B35",
        url: "https://zustand-demo.pmnd.rs/",
        category: "state",
      },
      {
        name: "TanStack Query",
        logo: "https://tanstack.com/_build/assets/logo-color-600w-Bx4vtR8J.png",
        description:
          "Powerful data synchronization for React with caching and background updates",
        color: "#FF4154",
        url: "https://tanstack.com/query",
        category: "state",
      },
      {
        name: "Zod",
        logo: "https://zod.dev/logo.svg",
        description:
          "TypeScript-first schema validation with static type inference",
        color: "#3068B7",
        url: "https://zod.dev/",
        category: "state",
      },
      {
        name: "React Hook Form",
        logo: "https://react-hook-form.com/images/logo/react-hook-form-logo-only.png",
        description:
          "Performant forms with easy validation and minimal re-renders",
        color: "#EC5990",
        url: "https://react-hook-form.com/",
        category: "state",
      },
    ],
  },
  {
    title: "Payment & Analytics",
    description:
      "Payment processing and analytics solutions for business applications",
    icon: "💳",
    technologies: [
      {
        name: "Stripe",
        logo: "https://stripe.com/favicon.ico",
        description:
          "Complete payment platform with APIs for online and in-person payments",
        color: "#635BFF",
        url: "https://stripe.com/",
        category: "payments",
      },
      {
        name: "PostHog",
        logo: "https://posthog.com/favicon.ico",
        description:
          "Product analytics platform with feature flags and session recordings",
        color: "#1D4AFF",
        url: "https://posthog.com/",
        category: "analytics",
      },
      {
        name: "Gumroad",
        logo: "https://gumroad.com/favicon.ico",
        description:
          "E-commerce platform for digital products and creator economy",
        color: "#FF90B3",
        url: "https://gumroad.com/",
        category: "payments",
      },
      {
        name: "Google Analytics",
        logo: "https://www.google.com/analytics/favicon.ico",
        description:
          "Web analytics service for tracking and reporting website traffic",
        color: "#E37400",
        url: "https://analytics.google.com/",
        category: "analytics",
      },
    ],
  },
]

// Legacy export for backward compatibility
export const technologies = technologiesData.flatMap(
  (category) => category.technologies,
)
