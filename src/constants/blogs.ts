export interface BlogPost {
  id: string
  title: string
  description: string
  date: string
  readTime: string
  author: {
    name: string
    avatar?: string
    bio?: string
  }
  image: string
  slug: string
  category: string
  tags: string[]
  featured?: boolean
  views?: number
}

export const BLOG_CATEGORIES = [
  {
    id: "typescript",
    name: "TypeScript",
    color: "blue",
  },
  {
    id: "ui-design",
    name: "UI Design",
    color: "purple",
  },
  {
    id: "developer-tools",
    name: "Developer Tools",
    color: "green",
  },
  {
    id: "nextjs",
    name: "Next.js",
    color: "gray",
  },
  {
    id: "web-development",
    name: "Web Development",
    color: "orange",
  },
  {
    id: "react",
    name: "React",
    color: "cyan",
  },
] as const

export const BLOG_POSTS: BlogPost[] = [
  {
    id: "1",
    title: "Building Type-Safe Applications with TypeScript",
    description:
      "Learn how to leverage TypeScript's powerful type system to build more reliable and maintainable applications. This guide covers advanced typing techniques, best practices, and common pitfalls to avoid.",
    date: "May 15, 2024",
    readTime: "8 min read",
    author: {
      name: "<PERSON>",
      bio: "Senior Frontend Developer with 8+ years of experience in TypeScript and React",
    },
    image:
      "https://images.unsplash.com/photo-1555066931-4365d14bab8c?q=80&w=2070&auto=format&fit=crop",
    slug: "building-type-safe-applications",
    category: "typescript",
    tags: ["TypeScript", "Development", "Best Practices"],
    featured: true,
    views: 2450,
  },
  {
    id: "2",
    title: "Modern UI Design with Tailwind CSS and Shadcn",
    description:
      "Discover how to create beautiful, responsive user interfaces using Tailwind CSS and Shadcn UI components. This tutorial walks through building a complete dashboard from scratch.",
    date: "June 3, 2024",
    readTime: "12 min read",
    author: {
      name: "Michael Chen",
      bio: "UI/UX Designer and Frontend Developer specializing in modern design systems",
    },
    image:
      "https://images.unsplash.com/photo-1551650975-87deedd944c3?q=80&w=1974&auto=format&fit=crop",
    slug: "modern-ui-design-tailwind-shadcn",
    category: "ui-design",
    tags: ["Tailwind CSS", "Shadcn", "UI Design", "Components"],
    featured: true,
    views: 3210,
  },
  {
    id: "3",
    title: "Optimizing Developer Workflow with Quality Tools",
    description:
      "Improve your development process with essential quality tools. Learn how to set up and configure Prettier, ESLint, and Husky pre-commit hooks to maintain consistent code quality across your team.",
    date: "July 22, 2024",
    readTime: "10 min read",
    author: {
      name: "Alex Rodriguez",
      bio: "DevOps Engineer passionate about developer experience and automation",
    },
    image:
      "https://images.unsplash.com/photo-1607799279861-4dd421887fb3?q=80&w=2070&auto=format&fit=crop",
    slug: "optimizing-developer-workflow",
    category: "developer-tools",
    tags: ["DevOps", "Prettier", "ESLint", "Husky", "Code Quality"],
    views: 1890,
  },
  {
    id: "4",
    title: "Building Scalable Applications with Next.js 14",
    description:
      "Explore the latest features in Next.js 14 and learn how to build scalable, production-ready applications. This comprehensive guide covers App Router, Server Components, and optimization techniques.",
    date: "August 10, 2024",
    readTime: "15 min read",
    author: {
      name: "Emily Parker",
      bio: "Full-stack Developer with expertise in React ecosystem and performance optimization",
    },
    image:
      "https://images.unsplash.com/photo-1517694712202-14dd9538aa97?q=80&w=2070&auto=format&fit=crop",
    slug: "building-scalable-applications-nextjs",
    category: "nextjs",
    tags: ["Next.js", "React", "Performance", "App Router"],
    featured: true,
    views: 4120,
  },
  {
    id: "5",
    title: "The Future of Web Development: 2025 Trends",
    description:
      "Stay ahead of the curve with this analysis of emerging trends in web development. From WebAssembly to Edge computing, discover the technologies that will shape the future of the web.",
    date: "September 5, 2024",
    readTime: "7 min read",
    author: {
      name: "David Wilson",
      bio: "Tech Lead and Web Architecture consultant with focus on emerging technologies",
    },
    image:
      "https://images.unsplash.com/photo-1498050108023-c5249f4df085?q=80&w=2072&auto=format&fit=crop",
    slug: "future-web-development-trends",
    category: "web-development",
    tags: ["WebAssembly", "Edge Computing", "Future Tech", "Trends"],
    views: 2780,
  },
  {
    id: "6",
    title: "Mastering State Management in React Applications",
    description:
      "Compare different state management approaches in React, from Context API to Redux and Zustand. Learn when to use each solution and how to implement them effectively in your projects.",
    date: "October 18, 2024",
    readTime: "11 min read",
    author: {
      name: "Sophia Martinez",
      bio: "React specialist with deep knowledge of state management patterns and performance",
    },
    image:
      "https://images.unsplash.com/photo-1633356122544-f134324a6cee?q=80&w=2070&auto=format&fit=crop",
    slug: "mastering-state-management-react",
    category: "react",
    tags: ["React", "State Management", "Redux", "Zustand", "Context API"],
    views: 3050,
  },
  {
    id: "7",
    title: "Advanced React Patterns for Enterprise Applications",
    description:
      "Deep dive into advanced React patterns including compound components, render props, and custom hooks. Learn how to build maintainable and scalable enterprise-grade applications.",
    date: "November 12, 2024",
    readTime: "14 min read",
    author: {
      name: "James Thompson",
      bio: "Senior React Developer with 10+ years building enterprise applications",
    },
    image:
      "https://images.unsplash.com/photo-1551288049-bebda4e38f71?q=80&w=2070&auto=format&fit=crop",
    slug: "advanced-react-patterns-enterprise",
    category: "react",
    tags: ["React", "Patterns", "Enterprise", "Architecture"],
    views: 1650,
  },
  {
    id: "8",
    title: "Creating Accessible Web Components",
    description:
      "Learn how to build web components that are accessible to all users. This guide covers ARIA attributes, keyboard navigation, and screen reader compatibility.",
    date: "December 3, 2024",
    readTime: "9 min read",
    author: {
      name: "Lisa Kim",
      bio: "Accessibility advocate and frontend developer focused on inclusive design",
    },
    image:
      "https://images.unsplash.com/photo-1573496359142-b8d87734a5a2?q=80&w=2070&auto=format&fit=crop",
    slug: "creating-accessible-web-components",
    category: "web-development",
    tags: ["Accessibility", "ARIA", "Web Components", "Inclusive Design"],
    views: 2100,
  },
]

export const FEATURED_POSTS = BLOG_POSTS.filter((post) => post.featured)

export const POPULAR_POSTS = BLOG_POSTS.sort(
  (a, b) => (b.views || 0) - (a.views || 0),
).slice(0, 3)
