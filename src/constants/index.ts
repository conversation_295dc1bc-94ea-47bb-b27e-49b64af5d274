import {
  CHILD_VARIANTS,
  FADE_IN_VARIANTS,
  LIST_ITEM_VARIANTS,
  MODAL_VARIANTS,
} from "./animation"
import {
  BLOG_CATEGORIES,
  BLOG_POSTS,
  FEATURED_POSTS,
  POPULAR_POSTS,
} from "./blogs"
import { aeonik, inter } from "./fonts"
import { FOOTER_LINKS } from "./links"
import { PERKS } from "./perks"
import { PLANS } from "./plans"
import { REVIEWS } from "./reviews"
import { APP_DOMAIN, APP_HOSTNAMES, APP_NAME } from "./site"
import { technologies, technologiesData } from "./technologies"
import { SAMPLE_TEMPLATES } from "./template"

export {
  aeonik,
  APP_DOMAIN,
  APP_HOSTNAMES,
  APP_NAME,
  BLOG_CATEGORIES,
  BLOG_POSTS,
  CHILD_VARIANTS,
  FADE_IN_VARIANTS,
  FEATURED_POSTS,
  FOOTER_LINKS,
  inter,
  LIST_ITEM_VARIANTS,
  MODAL_VARIANTS,
  PERKS,
  PLANS,
  POPULAR_POSTS,
  R<PERSON>VIE<PERSON>,
  <PERSON><PERSON>LE_TEMPLATES,
  technologies,
  technologiesData,
}
