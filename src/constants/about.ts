import {
  Award,
  CheckCircle,
  Clock,
  Code,
  Copy,
  Globe,
  Heart,
  Lightbulb,
  Rocket,
  Shield,
  Users,
  Zap,
} from "lucide-react"
// Stats data
export const statsData = [
  {
    icon: Code,
    value: "500+",
    label: "Premium Resources",
    badge: "And Growing",
    className:
      "bg-gradient-to-br from-blue-50 to-indigo-100 dark:from-blue-950 dark:to-indigo-900",
    iconBg: "bg-blue-600",
  },
  {
    icon: Users,
    value: "10,000+",
    label: "Happy Developers",
    badge: "Worldwide",
    className:
      "bg-gradient-to-br from-emerald-50 to-green-100 dark:from-emerald-950 dark:to-green-900",
    iconBg: "bg-emerald-600",
  },
  {
    icon: Award,
    value: "99%",
    label: "Quality Score",
    badge: "Verified",
    className:
      "bg-gradient-to-br from-purple-50 to-violet-100 dark:from-purple-950 dark:to-violet-900",
    iconBg: "bg-purple-600",
  },
]

// Mission data
export const missionData = [
  {
    icon: Rocket,
    title: "Premium Quality Resources",
    description:
      "Every resource follows strict standards and best practices for modern web development, ensuring consistent, maintainable code that scales with your projects.",
    badgeText: "Quality Guaranteed",
    badgeColor: "green" as const,
  },
  {
    icon: Heart,
    title: "Developer Experience Focus",
    description:
      "Optimized folder structures, comprehensive documentation, and testing setups that prioritize developer experience and boost productivity.",
    badgeText: "Developer First",
    badgeColor: "yellow" as const,
  },
  {
    icon: Shield,
    title: "Quality Assurance",
    description:
      "Thoroughly tested code with proper quality assurance tools to ensure reliability and consistency across all resources we provide.",
    badgeText: "Fully Tested",
    badgeColor: "purple" as const,
  },
]

// Values data
export const valuesData = [
  {
    icon: Code,
    title: "Quality First",
    description:
      "We never compromise on quality and maintain the highest standards.",
    colorClass:
      "bg-blue-100 text-blue-600 group-hover:bg-blue-600 dark:bg-blue-900 dark:text-blue-400",
  },
  {
    icon: Globe,
    title: "Community",
    description: "Supporting and empowering the global developer community.",
    colorClass:
      "bg-green-100 text-green-600 group-hover:bg-green-600 dark:bg-green-900 dark:text-green-400",
  },
  {
    icon: Lightbulb,
    title: "Innovation",
    description:
      "Constantly pushing boundaries and embracing new technologies.",
    colorClass:
      "bg-purple-100 text-purple-600 group-hover:bg-purple-600 dark:bg-purple-900 dark:text-purple-400",
  },
  {
    icon: Heart,
    title: "Passion",
    description: "Driven by genuine passion for development and design.",
    colorClass:
      "bg-orange-100 text-orange-600 group-hover:bg-orange-600 dark:bg-orange-900 dark:text-orange-400",
  },
]

// Feature data
export const featuresData = [
  {
    icon: Code,
    title: "Strict Code Standards",
    description:
      "Every line of code follows TypeScript best practices, ESLint rules, and modern development standards.",
    badges: ["TypeScript", "ESLint", "Prettier"],
    gradientFrom: "from-blue-500",
    gradientTo: "to-blue-600",
  },
  {
    icon: Zap,
    title: "Performance Optimized",
    description:
      "Built with performance in mind, featuring optimized images, lazy loading, and efficient code splitting.",
    badges: ["Image Optimization", "Code Splitting", "Lazy Loading"],
    gradientFrom: "from-emerald-500",
    gradientTo: "to-emerald-600",
  },
  {
    icon: Globe,
    title: "Accessibility First",
    description:
      "WCAG compliant components with proper ARIA labels, keyboard navigation, and screen reader support.",
    badges: ["WCAG Compliant", "ARIA Labels", "Keyboard Nav"],
    gradientFrom: "from-purple-500",
    gradientTo: "to-purple-600",
  },
  {
    icon: CheckCircle,
    title: "Comprehensive Testing",
    description:
      "Unit tests, integration tests, and E2E testing setups to ensure your code works as expected.",
    badges: ["Jest", "Cypress", "Testing Library"],
    gradientFrom: "from-orange-500",
    gradientTo: "to-orange-600",
  },
  {
    icon: Copy,
    title: "Documentation Included",
    description:
      "Detailed documentation, setup guides, and examples to get you started quickly and efficiently.",
    badges: ["Setup Guides", "API Docs", "Examples"],
    gradientFrom: "from-pink-500",
    gradientTo: "to-pink-600",
  },
  {
    icon: Clock,
    title: "Regular Updates",
    description:
      "Stay current with the latest frameworks, security patches, and feature enhancements.",
    badges: ["Security Patches", "Framework Updates", "New Features"],
    gradientFrom: "from-teal-500",
    gradientTo: "to-teal-600",
  },
]
