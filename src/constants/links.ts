import {
  ChartPieIcon,
  CreditCardIcon,
  GalleryVerticalIcon,
  LineChartIcon,
  LucideIcon,
  MegaphoneIcon,
  MessageSquareTextIcon,
  PlaneIcon,
  SettingsIcon,
  SubscriptIcon,
  UserIcon,
  UsersIcon,
  VideoIcon,
} from "lucide-react"

type Link = {
  title?: string
  href: string
  label: string
  icon: LucideIcon
}
export interface SideLink extends Link {
  sub?: Link[]
}

export const DASHBOARD_LINKS: Link[] = [
  {
    href: "/dashboard",
    label: "Dashboard",
    icon: ChartPieIcon,
  },
  {
    href: "/dashboard/blogs",
    label: "Blogs",
    icon: MegaphoneIcon,
  },
  {
    href: "/dashboard/categories",
    label: "Categories",
    icon: LineChartIcon,
  },
  {
    href: "/dashboard/coupons",
    label: "Coupons",
    icon: MessageSquareTextIcon,
  },
  {
    href: "/dashboard/orders",
    label: "Orders",
    icon: UsersIcon,
  },
  {
    href: "/dashboard/payments",
    label: "Payments",
    icon: CreditCardIcon,
  },
  {
    href: "/dashboard/plans",
    label: "plans",
    icon: PlaneIcon,
  },
  {
    href: "/dashboard/products",
    label: "products",
    icon: VideoIcon,
  },
  {
    href: "/dashboard/subscriptions",
    label: "Subscriptions",
    icon: SubscriptIcon,
  },
  // {
  //   href: "/dashboard/templates",
  //   label: "Templates",
  //   icon: SettingsIcon,
  // },
  {
    href: "/dashboard/users",
    label: "Users",
    icon: UserIcon,
  },
  {
    href: "/dashboard/gallery",
    label: "gallery",
    icon: GalleryVerticalIcon,
  },
]

export const SIDEBAR_LINKS: Link[] = [
  {
    href: "/all",
    label: "ALL",
    icon: MegaphoneIcon,
  },
  {
    href: "/templates",
    label: "Templates",
    icon: LineChartIcon,
  },
  {
    href: "/starter-kits",
    label: "Starter Kits",
    icon: MessageSquareTextIcon,
  },
  {
    href: "/landing-pages",
    label: "Landing Pages",
    icon: UsersIcon,
  },
  {
    href: "/themes",
    label: "Themes",
    icon: CreditCardIcon,
  },
  {
    href: "/blocks",
    label: "Blocks",
    icon: SettingsIcon,
  },
]

export const FOOTER_LINKS = [
  {
    title: "Resources",
    links: [
      { name: "Templates", href: "/templates" },
      { name: "Starter Kits", href: "/starter-kits" },
      { name: "Themes", href: "/themes" },
      { name: "Figma Kits", href: "/figma-kits" },
      { name: "Blocks", href: "/blocks" },
    ],
  },
  {
    title: "Company",
    links: [
      { name: "About Us", href: "/about-us" },
      { name: "Blog", href: "/blog" },
      { name: "Pricing", href: "/pricing" },
      { name: "Contact", href: "/contact-us" },
    ],
  },
  {
    title: "Legal",
    links: [
      { name: "Privacy", href: "/privacy" },
      { name: "Terms", href: "/terms" },
      { name: "Cookies", href: "/cookies" },
    ],
  },
  {
    title: "Developers",
    links: [
      { name: "Documentation", href: "/docs" },
      { name: "Standards", href: "/standards" },
      { name: "TypeScript", href: "/typescript" },
      { name: "Tailwind CSS", href: "/tailwind" },
      { name: "Shadcn UI", href: "/shadcn" },
    ],
  },
]
