import { z } from "zod"

import { Category } from "./category"
import { PRODUCT_TYPES } from "./constants"
import { FormConfig } from "./form"

// Interface for Product
export interface Product {
  _id?: string // Product ID (MongoDB ObjectId as a string)
  name: string // Name of the product
  fullName: string // Full name of the product
  slug?: string // Unique slug for the product
  creator?: string // Creator of the product
  description?: string // Product description
  images: string // Comma-separated image URLs
  type: (typeof PRODUCT_TYPES)[number] // Product type (enum)
  category?: string // Category ID
  // categories?: string[]; // Array of Category objects
  // superCategory?: string; // Reference to a Category ID
  subTypes?: string[] // Array of SubTypes (e.g., "portfolio", "eCommerce", "SaaS")
  tags?: string[] // Array of tags
  techStack?: string[] // Array of tech stack strings
  price?: number // Price of the product
  isPaid?: boolean // Whether the product is paid or free
  isFeatured?: boolean // Is the product featured?
  githubUrl?: string // URL for preview
  previewUrl?: string // URL for preview
  paymentLink?: string // URL for preview
  // paymentLink?: {
  //   platform?: string; // Payment platform (e.g., Razorpay, Stripe)
  //   link?: string; // Payment link
  // };
  // authorId: string; // Author's User ID
  downloads?: number // Number of downloads
  rating?: number // Product rating (0-5)
  likes?: number // Number of likes
  keyFeatures?: string // Key features of the product
  highlights?: string // Highlights of the product
  status: "active" | "inactive" | "archived" // Product status
  discount?: number // Discount percentage
  createdAt?: string // Creation timestamp
  updatedAt?: string // Update timestamp
}

// Zod Schema for Product
export const productSchema = z.object({
  name: z.string().min(1, "Name is required"),
  fullName: z.string().min(1, "Full name is required"),
  creator: z.string().optional(),
  slug: z.string().optional(),
  images: z.string().min(1, "Images are required"), // Comma-separated string of image URLs
  description: z.string().optional(),
  type: z.enum(PRODUCT_TYPES), // Enum validation for product type
  subTypes: z.array(z.string()).optional(),
  // categories: z.array(z.string()).optional(),
  // superCategory: z.string().optional(),
  category: z.string().optional(),
  tags: z.array(z.string()).optional(),
  techStack: z.array(z.string()).optional(),
  price: z.number().nonnegative("Price must be non-negative").optional(),
  isPaid: z.boolean().default(true),
  downloads: z
    .number()
    .nonnegative("Downloads must be non-negative")
    .optional(),
  githubUrl: z.string().url("Invalid URL").optional(),
  previewUrl: z.string().url("Invalid URL").optional(),
  paymentLink: z.string().url("Invalid URL").optional(),
  // paymentLink: z
  //   .object({
  //     platform: z.string().optional(),
  //     link: z.string().url("Invalid URL").optional(),
  //   })
  //   .optional(),
  // authorId: z.string().min(1, "Author ID is required"),
  rating: z.number().min(0).max(5).default(0),
  likes: z.number().min(0).default(0),
  keyFeatures: z.string().optional(),
  highlights: z.string().optional(),
  status: z.enum(["active", "inactive", "archived"]).default("active"),
  isFeatured: z.boolean().default(false),
  discount: z.number().min(0).max(100).default(0),
  createdAt: z.string().optional(),
  updatedAt: z.string().optional(),
})

export type ProductFormData = z.infer<typeof productSchema>

// Form Props for Product
export interface ProductFormProps {
  initialData?: ProductFormData
  onSubmit: (data: ProductFormData) => void
  categories: Category[] | []
}

// Product Form Config
export type ProductFormConfig = FormConfig<typeof productSchema>
