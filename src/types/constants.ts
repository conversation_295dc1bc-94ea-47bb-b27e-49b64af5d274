import { object } from "zod"

export const ROLES = {
  USER: "user",
  ADMIN: "admin",
  SUPER_ADMIN: "super_admin",
  MODERATOR: "moderator",
  SALES: "sales",
  SUPPORT: "support",
  MARKETING: "marketing",
  ACCOUNTANT: "accountant",
  HR: "hr",
  DEVELOPER: "developer",
  DESIGNER: "designer",
} as const

// Product Types
export const PRODUCT_TYPES = [
  "templates",
  "starter-kits",
  "themes",
  "plugins",
  "extensions",
  "scripts",
  "design-assets",
  "e-books",
  "courses",
  "audios",
  "videos",
  "softwares",
  "others",
] as const

interface TechStack {
  label: string
  image: string
}

export const TECH_STACK: Record<string, TechStack> = {
  angular: { label: "Angular", image: "angular_image_url" },
  apollo: { label: "Apollo", image: "apollo_image_url" },
  astro: { label: "Astro", image: "astro_image_url" },
  bootstrap: { label: "Bootstrap", image: "bootstrap_image_url" },
  "chakra-ui": { label: "Chakra UI", image: "chakra_ui_image_url" },
  cloudflare: { label: "Cloudflare", image: "cloudflare_image_url" },
  daisyui: { label: "DaisyUI", image: "daisyui_image_url" },
  deno: { label: "Deno", image: "deno_image_url" },
  django: { label: "Django", image: "django_image_url" },
  elasticsearch: { label: "Elasticsearch", image: "elasticsearch_image_url" },
  express: { label: "Express", image: "express_image_url" },
  flask: { label: "Flask", image: "flask_image_url" },
  graphql: { label: "GraphQL", image: "graphql_image_url" },
  "material-ui": { label: "Material UI", image: "material_ui_image_url" },
  mongodb: { label: "MongoDB", image: "mongodb_image_url" },
  nextjs: { label: "Next.js", image: "nextjs_image_url" },
  node: { label: "Node.js", image: "node_image_url" },
  postgresql: { label: "PostgreSQL", image: "postgresql_image_url" },
  react: { label: "React", image: "react_image_url" },
  redux: { label: "Redux", image: "redux_image_url" },
  "rest-api": { label: "REST API", image: "rest_api_image_url" },
  "ruby-on-rails": { label: "Ruby on Rails", image: "ruby_on_rails_image_url" },
  sass: { label: "Sass", image: "sass_image_url" },
  shadcn: { label: "ShadCN UI", image: "shadcn_image_url" },
  solidjs: { label: "Solid.js", image: "solidjs_image_url" },
  tailwind: { label: "Tailwind CSS", image: "tailwind_image_url" },
  typescript: { label: "TypeScript", image: "typescript_image_url" },
  vue: { label: "Vue.js", image: "vue_image_url" },
}

// Sub Types
export const SUB_TYPES: Record<string, TechStack> = {
  portfolio: { label: "Portfolio", image: "portfolio_image_url" },
  ecommerce: { label: "eCommerce", image: "ecommerce_image_url" },
  saas: { label: "SaaS", image: "saas_image_url" },
  blog: { label: "Blog", image: "blog_image_url" },
  "social-media": { label: "Social Media", image: "social_media_image_url" },
  marketplace: { label: "Marketplace", image: "marketplace_image_url" },
  forum: { label: "Forum", image: "forum_image_url" },
  "job-board": { label: "Job Board", image: "job_board_image_url" },
  lms: { label: "LMS", image: "lms_image_url" },
  crm: { label: "CRM", image: "crm_image_url" },
  booking: { label: "Booking", image: "booking_image_url" },
  news: { label: "News", image: "news_image_url" },
  directory: { label: "Directory", image: "directory_image_url" },
  wiki: { label: "Wiki", image: "wiki_image_url" },
  analytics: { label: "Analytics", image: "analytics_image_url" },
  finance: { label: "Finance", image: "finance_image_url" },
  health: { label: "Health", image: "health_image_url" },
  education: { label: "Education", image: "education_image_url" },
  entertainment: { label: "Entertainment", image: "entertainment_image_url" },
}

// console.log(PRODUCT_TYPES);
// console.log(Object.values(PRODUCT_TYPES));

export const PAYMENT_STATUSES = {
  PENDING: "pending",
  SUCCESSFUL: "successful",
  FAILED: "failed",
} as const

export const DEFAULT_CURRENCY = "USD"

export const PLAN_DURATIONS = {
  MONTHLY: "monthly",
  YEARLY: "yearly",
  LIFETIME: "lifetime",
} as const
