import { z } from "zod"

import { ROL<PERSON> } from "./constants"
import { FormConfig } from "./form"

// TypeScript Interface for User
export interface User {
  _id?: string // User ID (MongoDB ObjectId as a string)
  email: string // Email of the user
  username: string // Username (required)
  password?: string // User's password (hashed, optional for OAuth)
  googleId?: string // Google authentication ID (optional)
  provider: "google" | "email" // Provider for authentication
  firstName?: string // User's first name
  lastName?: string // User's last name
  profileImage?: string // URL for user's profile image
  phoneNumber?: string // User's phone number
  role:
    | "user"
    | "admin"
    | "super_admin"
    | "moderator"
    | "sales"
    | "support"
    | "marketing"
    | "accountant"
    | "hr"
    | "developer"
    | "designer" // User role
  isDeleted: boolean // Whether the user is deleted
  lastLogin?: Date // Timestamp of the user's last login
  isEmailVerified: boolean // Whether email is verified
  emailVerificationToken?: string // Email verification token
  passwordResetToken?: string // Password reset token
  passwordResetExpires?: Date // Password reset expiration
  isPro: boolean // Whether user has pro subscription
  isLifetimePro: boolean // Whether user has lifetime pro
  subscriptionStartDate?: Date // Subscription start date
  subscriptionEndDate?: Date // Subscription end date
  currentPlan?: string // Current plan reference (ObjectId as string)
  createdAt?: Date // Timestamp of user creation
  updatedAt?: Date // Timestamp of last user update
}

// Zod Schema for User Validation
export const userSchema = z.object({
  email: z.string().min(1, "Email is required").email("Invalid email format"),
  username: z.string().min(1, "Username is required"),
  password: z
    .string()
    .min(6, "Password must be at least 6 characters long")
    .optional(),
  googleId: z.string().optional(),
  provider: z.enum(["google", "email"]).default("email"),
  firstName: z.string().optional(),
  lastName: z.string().optional(),
  profileImage: z.string().optional(),
  phoneNumber: z.string().optional(),
  role: z
    .enum(
      Object.values(ROLES) as [
        "user",
        "admin",
        "super_admin",
        "moderator",
        "sales",
        "support",
        "marketing",
        "accountant",
        "hr",
        "developer",
        "designer",
      ],
    )
    .default(ROLES.USER),
  isDeleted: z.boolean().default(false),
  lastLogin: z.date().optional(),
  isEmailVerified: z.boolean().default(false),
  emailVerificationToken: z.string().optional(),
  passwordResetToken: z.string().optional(),
  passwordResetExpires: z.date().optional(),
  isPro: z.boolean().default(false),
  isLifetimePro: z.boolean().default(false),
  subscriptionStartDate: z.date().optional(),
  subscriptionEndDate: z.date().optional(),
  currentPlan: z.string().optional(),
  createdAt: z.date().optional(),
  updatedAt: z.date().optional(),
})

// TypeScript Type for User Form Data
export type UserFormData = z.infer<typeof userSchema>

// Form Props for User
export interface UserFormProps {
  initialData?: UserFormData
  onSubmit: (data: UserFormData) => void
}

// User Form Config
export type UserFormConfig = FormConfig<typeof userSchema>
