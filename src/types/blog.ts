import { z } from "zod"

import { FormConfig } from "./form"

// TypeScript Interface for Blog
export interface Blog {
  _id?: string // Blog ID (MongoDB ObjectId as a string)
  title: string // Title of the blog
  slug?: string // Unique slug for the blog
  content: string // Content of the blog
  author: string // User ID of the author
  categories?: string[] // Array of category strings
  tags?: string[] // Array of tag strings
  thumbnailUrl?: string // URL of the thumbnail image
  isPublished?: boolean // Whether the blog is published
  publishDate?: Date | string // Publish date of the blog
  views?: number // Number of views
  likes?: number // Number of likes
  createdAt?: string // Timestamp of creation
  updatedAt?: string // Timestamp of last update
}

// Zod Schema for Blog Validation
export const blogSchema = z.object({
  title: z
    .string()
    .min(3, "Title must be at least 3 characters")
    .max(150, "Title cannot exceed 150 characters"),
  slug: z.string().optional(),
  // .min(1, "Slug is required")
  // .regex(/^[a-z0-9]+(?:-[a-z0-9]+)*$/, "Slug must be lowercase and hyphen-separated"),
  content: z.string().min(1, "Content is required"),
  author: z.string().min(1, "Author ID is required"),
  categories: z.array(z.string()).optional(),
  tags: z.array(z.string()).optional(),
  thumbnailUrl: z.string().url("Invalid URL").optional(),
  isPublished: z.boolean().default(false),
  // publishDate: z.date().optional(), // ISO string format
  publishDate: z.date().or(z.string()), // ISO string format
  views: z.number().nonnegative("Views must be non-negative").default(0),
  likes: z.number().nonnegative("Likes must be non-negative").default(0),
})

export type BlogFormData = z.infer<typeof blogSchema>

// Form Props for Blog
export interface BlogFormProps {
  initialData?: BlogFormData
  onSubmit: (data: BlogFormData) => void
}

// Blog Form Config
export type BlogFormConfig = FormConfig<typeof blogSchema>
