import { z } from "zod"

import { FormConfig } from "./form"

// TypeScript Interface for Subscription
export interface Subscription {
  _id?: string // Subscription ID (MongoDB ObjectId as a string)
  userId: string // User ID (MongoDB ObjectId as a string)
  planId: string // Plan ID (MongoDB ObjectId as a string)
  startDate: Date | string // Start date of the subscription (ISO format string)
  endDate?: Date | string // End date of the subscription (ISO format string)
  isActive: boolean // Whether the subscription is currently active
  isCurrent: boolean // Indicates the active subscription
  autoRenew: boolean // Whether auto-renewal is enabled
  paymentMethod?: string // Payment method (optional)
  paymentStatus: "paid" | "failed" | "pending" // Payment status
  history?: SubscriptionHistory[] // Past subscription history
  createdAt?: Date // Timestamp of creation
  updatedAt?: Date // Timestamp of last update
}

// TypeScript Interface for Subscription History
export interface SubscriptionHistory {
  planId: string // Plan ID (MongoDB ObjectId as a string)
  startDate: string // Start date of the historical record (ISO format string)
  endDate?: string // End date of the historical record (ISO format string)
  paymentStatus: "paid" | "failed" | "pending" // Payment status
  isActive: boolean // Whether the subscription was active at that time
}

export const subscriptionSchema = z.object({
  userId: z.string().min(1, "User ID is required"),
  planId: z.string().min(1, "Plan ID is required"),
  // startDate: z.string().min(1, "Start date is required"), // ISO string format
  // endDate: z.string().optional(), // ISO string format (optional)

  startDate: z.date({ required_error: "Valid Start date is required" }),
  endDate: z.date().optional(),

  isActive: z.boolean().default(true),
  isCurrent: z.boolean().default(false), // Default is not current
  autoRenew: z.boolean().default(true),
  paymentMethod: z.string().optional(),
  paymentStatus: z.enum(["paid", "failed", "pending"]).default("pending"),
  history: z
    .array(
      z.object({
        planId: z.string().min(1, "Plan ID is required"),
        startDate: z.string().min(1, "Start date is required"),
        endDate: z.string().optional(),
        paymentStatus: z.enum(["paid", "failed", "pending"]),
        isActive: z.boolean(),
      }),
    )
    .optional(),
  // createdAt: z.date().optional(),
  // updatedAt: z.date().optional(),
})

// TypeScript Type for Subscription Form Data
export type SubscriptionFormData = z.infer<typeof subscriptionSchema>

export interface SubscriptionFormProps {
  initialData?: SubscriptionFormData
  onSubmit: (data: SubscriptionFormData) => void
}

// Subscription Form Config
export type SubscriptionFormConfig = FormConfig<typeof subscriptionSchema>
