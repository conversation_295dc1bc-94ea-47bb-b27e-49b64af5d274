import { z } from "zod"

import { FormConfig } from "./form"

// Plan Durations Enum
export const PLAN_DURATIONS = ["monthly", "yearly", "lifetime"] as const

// Plan TypeScript Interface
export interface Plan {
  _id?: string // Plan ID (MongoDB ObjectId as a string)
  name: string // Name of the plan (e.g., "Basic", "Pro")
  description?: string // Description of the plan
  price: number // Price for the plan
  currency: string // Currency for the price
  duration: (typeof PLAN_DURATIONS)[number] // Plan duration (monthly, yearly, lifetime)
  isActive: boolean // Whether the plan is active or not
  maxTemplates?: number // Maximum number of templates a user can access
  maxUsers?: number // Maximum number of users (for team plans)
  // benefits?: string[]; // Array of benefits
  benefits?: string // Array of benefits
  createdBy?: string // User ID of the admin who created the plan
  createdAt?: string
  updatedAt?: string
}

// Zod Schema for Plan
export const planSchema = z.object({
  name: z.string().min(1, "Name is required"),
  description: z.string().optional(),
  price: z.number().positive("Price must be a positive number"),
  currency: z.string().min(1, "Currency is required").default("USD"),
  duration: z.enum(PLAN_DURATIONS).default("monthly"),
  isActive: z.boolean().default(true),
  maxTemplates: z
    .number()
    .nonnegative("Max templates must be non-negative")
    .optional(),
  maxUsers: z.number().nonnegative("Max users must be non-negative").optional(),
  benefits: z.string().optional(),
  // benefits: z.array(z.string()).optional(),
  createdBy: z.string().optional(),
  createdAt: z.string().optional(),
  updatedAt: z.string().optional(),
})

export type PlanFormData = z.infer<typeof planSchema>

// Form Props for Plan
export interface PlanFormProps {
  initialData?: PlanFormData
  onSubmit: (data: PlanFormData) => void
}

// Plan Form Config
export type PlanFormConfig = FormConfig<typeof planSchema>
