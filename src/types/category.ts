import { z } from "zod"

import { FormConfig } from "./form"

export interface Category {
  _id?: string
  isActive?: boolean
  name: string
  type: string
  slug?: string
  description?: string
  parent?: string | null
}

export const categorySchema = z.object({
  name: z.string().min(1, "Name is required"),
  slug: z.string().optional(),
  type: z.string().default("product"),
  isActive: z.boolean().default(true),
  description: z.string().optional(),
  parent: z.string().nullable().optional(),
  // parent: z
  //   .object({
  //     _id: z.string(),
  //     name: z.string(),
  //   })
  //   .optional(),
})

export type CategoryFormData = z.infer<typeof categorySchema>

export interface CategoryFormProps {
  initialData?: CategoryFormData
  onSubmit: (data: CategoryFormData) => void
  // categories: { _id: string; name: string }[];
  categories: Category[]
}

export type CategoryFormConfig = FormConfig<typeof categorySchema>
