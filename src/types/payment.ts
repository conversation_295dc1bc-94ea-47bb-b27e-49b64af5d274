import { z } from "zod"

import { PAYMENT_STATUSES } from "./constants"
import { FormConfig } from "./form"

// Define Payment Methods
export const PAYMENT_METHODS = [
  "credit_card",
  "paypal",
  "bank_transfer",
] as const

// Payment TypeScript Interface
export interface Payment {
  _id?: string // Payment ID (MongoDB ObjectId as a string)
  user: string // User ID (MongoDB ObjectId as a string)
  amount: number
  currency: string
  status: (typeof PAYMENT_STATUSES)[keyof typeof PAYMENT_STATUSES]
  method: (typeof PAYMENT_METHODS)[number]
  transactionId?: string
  description?: string
  isRefunded: boolean
  paymentMethodDetails?: Record<string, any> // Store sensitive payment details, if applicable
  createdAt?: string
  updatedAt?: string
}

// Zod Schema for Payment
export const paymentSchema = z.object({
  user: z.string().min(1, "User ID is required"),
  amount: z.number().positive("Amount must be a positive number"),
  currency: z.string().min(1, "Currency is required").default("USD"),
  status: z
    .enum(["pending", "successful", "failed"] as const)
    .default(PAYMENT_STATUSES.PENDING),
  method: z.enum(PAYMENT_METHODS),
  transactionId: z.string().optional(),
  description: z.string().optional(),
  isRefunded: z.boolean().default(false),
  paymentMethodDetails: z.record(z.any()).optional(),
  createdAt: z.string().optional(),
  updatedAt: z.string().optional(),
})

export type PaymentFormData = z.infer<typeof paymentSchema>

// Form Props for Payment
export interface PaymentFormProps {
  initialData?: PaymentFormData
  onSubmit: (data: PaymentFormData) => void
  // users: { _id: string; name: string }[]; // List of users for dropdown
}

// Payment Form Config
export type PaymentFormConfig = FormConfig<typeof paymentSchema>
