import { z } from "zod"

import { FormConfig } from "./form"

export interface Order {
  _id?: string // Order ID (MongoDB ObjectId as a string)
  user: string // User ID (MongoDB ObjectId as a string)
  products: string[] // Product ID (MongoDB ObjectId as a string)
  totalAmount: number
  payment: string // Payment ID (MongoDB ObjectId as a string)
  status: "completed" | "failed" | "pending"
  deliveryEmail: string
  shippingAddress?: {
    street: string
    city: string
    state: string
    postalCode: string
    country: string
  }
  created_at?: Date
  updated_at?: Date
}

export const orderSchema = z.object({
  user: z.string().min(1, "User is required"),
  products: z.array(z.string()).nonempty("At least one product is required"),
  totalAmount: z.number().nonnegative("Total amount must be a positive number"),
  payment: z.string().min(1, "Payment ID is required"),
  status: z.enum(["completed", "failed", "pending"]),
  deliveryEmail: z.string().email("A valid email is required"),
  shippingAddress: z
    .object({
      street: z.string().min(1, "Street is required"),
      city: z.string().min(1, "City is required"),
      state: z.string().min(1, "State is required"),
      postalCode: z.string().min(1, "Postal code is required"),
      country: z.string().min(1, "Country is required"),
    })
    .optional(),
})

export type OrderFormData = z.infer<typeof orderSchema>

export interface OrderFormProps {
  initialData?: OrderFormData
  onSubmit: (data: OrderFormData) => void
  // users: { _id: string; name: string }[]; // List of users for dropdown
  // templates: { _id: string; name: string; price: number }[]; // List of templates
  // payments: { _id: string; name: string }[]; // List of payment methods
}

export type OrderFormConfig = FormConfig<typeof orderSchema>
