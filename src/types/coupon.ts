import { z } from "zod"

import { FormConfig } from "./form"

// Define the Coupon interface
export interface Coupon {
  _id?: string
  code: string
  discountType: "percentage" | "fixed"
  discountValue: number
  maxRedemptions?: number
  currentRedemptions?: number
  validFrom: Date | string
  validUntil: Date | string
  isActive?: boolean
  expirationDate?: Date | string
}

// Zod schema for validating coupon data
export const couponSchema = z.object({
  code: z.string().min(1, "Code is required"),
  discountType: z.enum(["percentage", "fixed"], {
    errorMap: () => ({
      message: "Discount type must be 'percentage' or 'fixed'",
    }),
  }),
  discountValue: z.number().min(1, "Discount value must be at least 1"),
  maxRedemptions: z
    .number()
    .min(1, "Maximum redemptions must be at least 1")
    .optional(),
  currentRedemptions: z
    .number()
    .min(0, "Current redemptions must be at least 0")
    .optional(),

  //can be date or sting
  // validFrom: z.date({ required_error: "Valid from date is required" }),
  // validUntil: z.date({ required_error: "Valid until date is required" }),
  validFrom: z.date().or(z.string()),
  validUntil: z.date().or(z.string()),

  expirationDate: z.date().optional(),
  // validFrom: z.string().min(1, "Valid from date is required"),
  // validUntil: z.string().min(1, "Valid until date is required"),
  // expirationDate: z.string().optional(),
  isActive: z.boolean().optional().default(true),
})

// Infer the TypeScript type from the Zod schema
export type CouponFormData = z.infer<typeof couponSchema>

// Define the props for the coupon form
export interface CouponFormProps {
  initialData?: CouponFormData
  onSubmit: (data: CouponFormData) => void
}

// Define the form configuration type
export type CouponFormConfig = FormConfig<typeof couponSchema>
