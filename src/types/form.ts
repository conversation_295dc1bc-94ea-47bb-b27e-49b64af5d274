import { UseFormReturn } from "react-hook-form"
import { z } from "zod"

export type FieldConfig = {
  name: string
  label: string
  isRequired?: boolean
  type:
    | "text"
    | "email"
    | "password"
    | "number"
    | "select"
    | "textarea"
    | "checkbox"
    | "radio"
    | "gallery"
    | "select-multiple"
    | "select-single"
    | "image"
    | "array"
    | "date"
    | "s3-image"
    | "image-preview"

  placeholder?: string
  options?: { label: string; value: string }[]
}

export type FormConfig<T extends z.ZodType> = {
  fields: FieldConfig[]
  schema: T
  onSubmit: (values: z.infer<T>) => void
}

export type FormProps<T extends z.ZodType> = FormConfig<T> & {
  defaultValues?: Partial<z.infer<T>>
}

export type FormFieldProps = {
  field: FieldConfig
  form: UseFormReturn<any>
}
