import { z } from "zod"

import { FormConfig } from "./form"

// Gallery interface
export interface Gallery {
  _id?: string
  imageUrl: string // URL or path to the image
  imageKey?: string // Key/ID for S3 or similar storage

  title: string // Image title
  description?: string // Optional description

  tags?: string[] // Tags for filtering/categorization
  isPublic?: boolean // Visibility of the image
  isDeleted?: boolean // For soft delete
  isFeatured?: boolean // To highlight featured images

  fileName?: string // Original file name
  fileSize?: number // File size in bytes
  fileType?: string // MIME type of the file
  category?: string // Category of the image (e.g., nature, portraits, etc.)

  source: "S3" | "UploadThing" | "Unsplash" | "External" // Source of the image
  sourceDetails?: string // Additional details about the source, like an external URL or metadata

  uploadedBy?: string // User who uploaded the image (reference to User ID)
}

// Zod schema for Gallery
export const gallerySchema = z.object({
  imageUrl: z.string().url("Invalid image URL").min(1, "Image URL is required"),
  imageKey: z.string().optional(),

  title: z.string().min(1, "Title is required"),
  description: z.string().optional(),

  tags: z.array(z.string()).optional(),
  isPublic: z.boolean().optional(),
  isDeleted: z.boolean().optional(),
  isFeatured: z.boolean().optional(),

  fileName: z.string().optional(),
  fileSize: z
    .number()
    .positive("File size must be a positive number")
    .optional(),
  fileType: z.string().optional(),
  category: z.string().optional(),

  source: z.enum(["S3", "UploadThing", "Unsplash", "External"], {
    required_error: "Source is required",
  }),
  sourceDetails: z.string().optional(),

  uploadedBy: z.string().optional(),
})

export type GalleryFormData = z.infer<typeof gallerySchema>

// Props for the gallery form
export interface GalleryFormProps {
  initialData?: GalleryFormData
  onSubmit: (data: GalleryFormData) => void
  // categories: { _id: string; name: string }[]; // Categories for dropdown selection
}

export type GalleryFormConfig = FormConfig<typeof gallerySchema>
