"use client"

import { motion, useMotionTemplate, useMotionValue } from "framer-motion"
import React, { useCallback, useEffect } from "react"

import { cn } from "@/functions"

import { Particles } from "../ui/particles"

export interface Props extends React.HTMLAttributes<HTMLDivElement> {
  children: React.ReactNode
  gradientSize?: number
  gradientColor?: string
  borderColor?: string
  particles?: boolean
  count?: number
}

export default function MagicCard({
  children,
  particles = false,
  count = 20,
}: Props) {
  const onMouseMove = (e: React.MouseEvent<HTMLDivElement>) => {
    const { currentTarget } = e
    const rect = currentTarget.getBoundingClientRect()
    const x = e.clientX - rect.left
    const y = e.clientY - rect.top

    currentTarget.style.setProperty("--pos-x", `${x}px`)
    currentTarget.style.setProperty("--pos-y", `${y}px`)
  }

  return (
    <div
      className='card overflow-hidden rounded-xl lg:rounded-2xl'
      onMouseMove={onMouseMove}
    >
      <div className='content'>
        {particles && (
          <Particles
            className='absolute inset-0 z-10 h-full w-full'
            quantity={count}
            ease={80}
            color='#d4d4d8'
            refresh
          />
        )}
        {children}
      </div>
    </div>
  )
}
