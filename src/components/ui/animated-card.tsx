import { motion } from "framer-motion"
import { ReactNode } from "react"

import { cn } from "@/lib/utils"

interface AnimatedCardProps {
  children: ReactNode
  className?: string
  delay?: number
}

const AnimatedCard = ({
  children,
  className,
  delay = 0,
}: AnimatedCardProps) => {
  return (
    <motion.div
      initial={{ opacity: 0, y: 20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5, delay }}
      whileHover={{
        scale: 1.03,
        boxShadow:
          "0 20px 25px -5px rgba(16, 185, 129, 0.1), 0 10px 10px -5px rgba(16, 185, 129, 0.04)",
      }}
      className={cn(
        "rounded-xl border border-neutral-700 bg-neutral-800/50 p-6 backdrop-blur-lg transition-all duration-300 hover:border-emerald-500/50",
        className,
      )}
    >
      {children}
    </motion.div>
  )
}

export default AnimatedCard
