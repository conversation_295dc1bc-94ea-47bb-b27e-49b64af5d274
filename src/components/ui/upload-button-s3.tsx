"use client"

import { UploadCloud } from "lucide-react"
import { useRef, useState } from "react"
import { toast } from "sonner"

import { Button } from "./button"

export interface S3UploadResponse {
  title: string
  fileName: string
  imageUrl: string
  imageKey: string
  fileSize: number
  fileType: string
  sourceDetails: string
  source: string
}

interface UploadButtonS3Props {
  onClientUploadComplete?: (response: S3UploadResponse) => void
  onUploadError?: (error: Error) => void
  onUploadBegin?: () => void
}

export function UploadButtonS3({
  onClientUploadComplete,
  onUploadError,
  onUploadBegin,
}: UploadButtonS3Props) {
  const [isUploading, setIsUploading] = useState(false)
  const inputRef = useRef<HTMLInputElement>(null)

  const handleUpload = async (file: File) => {
    if (!file) return

    try {
      setIsUploading(true)
      onUploadBegin?.()

      const formData = new FormData()
      formData.append("file", file)
      formData.append("prefix", "designbyte")

      const response = await fetch(
        `${process.env.NEXT_PUBLIC_SERVICE_API_URL}/s3/designbyte-upload`,
        {
          method: "POST",
          body: formData,
        },
      )

      if (!response.ok) {
        throw new Error(`Upload failed: ${response.statusText}`)
      }

      const data = await response.json()

      if (data.error) {
        throw new Error(data.error)
      }

      // Assuming the API returns { url, key } in the response
      const uploadResponse = {
        title: data.originalName,
        fileName: data.modifiedName,
        imageUrl: data.url,
        imageKey: data.key,
        fileSize: data.size,
        fileType: data.type,
        sourceDetails: "Uploaded to S3",
        source: "S3",
      }

      onClientUploadComplete?.(uploadResponse)
      toast.success("File uploaded successfully")
    } catch (error) {
      const errorMessage =
        error instanceof Error ? error.message : "Upload failed"
      onUploadError?.(new Error(errorMessage))
      toast.error(errorMessage)
    } finally {
      setIsUploading(false)
      // Reset input value to allow uploading the same file again
      if (inputRef.current) {
        inputRef.current.value = ""
      }
    }
  }

  const handleFileChange = (event: React.ChangeEvent<HTMLInputElement>) => {
    const file = event.target.files?.[0]
    if (file) {
      // Basic file validation
      if (file.size > 4 * 1024 * 1024) {
        // 4MB limit
        toast.error("File size must be less than 4MB")
        return
      }

      const allowedTypes = [
        "image/jpeg",
        "image/png",
        "image/jpg",
        "image/gif",
        "image/webp",
        "image/svg+xml",
        "image/tiff",
        "image/bmp",
        "image/heic",
        "image/heif",
        "image/avif",
        "image/jxr",
      ]
      if (!allowedTypes.includes(file.type)) {
        toast.error("Only JPEG, PNG, GIF, and WebP files are allowed")
        return
      }

      handleUpload(file)
    }
  }

  return (
    <div className='flex flex-col items-center gap-2'>
      <input
        ref={inputRef}
        type='file'
        accept='image/*'
        className='hidden'
        onChange={handleFileChange}
        disabled={isUploading}
      />
      <Button
        type='button'
        variant='outline'
        disabled={isUploading}
        onClick={() => inputRef.current?.click()}
        className='w-full'
      >
        <UploadCloud className='mr-2 h-4 w-4' />
        {isUploading ? "Uploading..." : "Upload Image"}
      </Button>
    </div>
  )
}
