/* eslint-disable import/order */
"use client"
import { motion } from "framer-motion"
import Link from "next/link"
import { usePathname } from "next/navigation"
import { useEffect, useState } from "react"

import { FOOTER_LINKS } from "@/constants"
import { cn } from "@/functions"

import Icons from "../global/icons"
import { includesUrls } from "../navigation/navbar"
import { Button } from "../ui/button"
import { Particles } from "../ui/particles"
import { TextHoverEffect } from "../ui/text-hover-effect"
import Container from "./container"
import Wrapper from "./wrapper"

export const Footer = () => {
  const pathname = usePathname()
  const [showFooter, setShowFooter] = useState<boolean>(false)

  useEffect(() => {
    // if (excludesUrls?.some((url) => pathname.startsWith(url))) {
    //   setShowFooter(false);
    // } else {
    //   setShowFooter(true);
    // }

    if (includesUrls?.some((url) => pathname === url)) {
      setShowFooter(true)
    } else {
      setShowFooter(false)
    }
  }, [pathname])

  return (
    <motion.footer
      //animate as we have in classname like play iwth margin and width not opacity
      // initial={{ marginLeft }}

      className={cn(
        "relative w-full border-t border-neutral-900 pt-20",
        !showFooter && "w-full lg:ml-72 lg:max-w-[calc(100%-18rem)]",
      )}
    >
      <Container>
        <Wrapper className='footer relative flex flex-col justify-between gap-10 overflow-hidden md:flex-row md:gap-20 md:pb-20'>
          <Particles
            className='absolute inset-0 -z-10 w-full'
            quantity={40}
            ease={10}
            color='#d4d4d8'
            refresh
          />
          <div className='flex max-w-48 flex-col items-start'>
            <div className='flex items-center gap-2'>
              <Icons.icon className='h-5 w-5' />
              <span className='text-xl font-medium'>DesignByte</span>
            </div>
            <p className='max-w mt-4 text-base'>
              Empower your business with our AI tools.
            </p>
            <Button className='mt-8'>
              <Link href='/'>Start for free</Link>
            </Button>
          </div>
          <div className='mt-10 grid w-full max-w-4xl grid-cols-2 gap-8 md:mt-0 lg:grid-cols-4'>
            {FOOTER_LINKS?.map((section, index) => (
              <div key={index} className='flex flex-col gap-4'>
                <h4 className='text-sm font-medium'>{section.title}</h4>
                <ul className='w-full space-y-4'>
                  {section.links.map((link, index) => (
                    <li
                      key={index}
                      className='w-full text-sm text-muted-foreground transition-all hover:text-foreground'
                    >
                      <Link href={link.href} className='w-full'>
                        {link.name}
                      </Link>
                    </li>
                  ))}
                </ul>
              </div>
            ))}
          </div>
        </Wrapper>
      </Container>
      <Container>
        <Wrapper className='relative flex items-center justify-between pt-10'>
          <p className='text-sm text-secondary-foreground'>
            &copy; {new Date().getFullYear()} DesignByte. All rights reserved.
          </p>
          <div className='flex items-center gap-4'>
            <Link href='#' className='p-1'>
              <Icons.instagram className='h-5 w-5 text-muted-foreground hover:text-secondary-foreground' />
            </Link>
            <Link href='#' className='p-1'>
              <Icons.twitter className='h-5 w-5 text-muted-foreground hover:text-secondary-foreground' />
            </Link>
            <Link href='#' className='p-1'>
              <Icons.discord className='h-5 w-5 text-muted-foreground hover:text-secondary-foreground' />
            </Link>
          </div>
        </Wrapper>

        <TextHoverEffect text='DESIGNBYTE' />
      </Container>
    </motion.footer>
  )
}
