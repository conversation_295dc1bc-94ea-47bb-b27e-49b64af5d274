import React from "react"

import { cn } from "@/functions"

import Container from "./container"

interface SectionWrapperProps {
  children: React.ReactNode
  className?: string
  containerClassName?: string
  withContainer?: boolean
  id?: string
}

const SectionWrapper = ({
  children,
  className,
  containerClassName,
  withContainer = true,
  id,
}: SectionWrapperProps) => {
  const content = withContainer ? (
    <Container className={containerClassName}>{children}</Container>
  ) : (
    children
  )

  return (
    <section
      id={id}
      className={cn("relative w-full py-16 md:py-20 lg:py-24", className)}
    >
      {content}
    </section>
  )
}

export default SectionWrapper
