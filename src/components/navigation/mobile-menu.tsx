"use client"

import { motion } from "framer-motion"
import {
  <PERSON>,
  Calendar<PERSON>lock,
  Captions,
  CircleHelp,
  CopyCheck,
  FileText,
  Gem,
  Layers3,
  LineChart,
  Newspaper,
  UserCog,
  Waypoints,
} from "lucide-react"
import Link from "next/link"
import React from "react"

import {
  Accordion,
  Accordion<PERSON>ontent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion"
import { cn } from "@/functions"
import { useClickOutside } from "@/hooks"

interface Props {
  isOpen: boolean
  setIsOpen: React.Dispatch<React.SetStateAction<boolean>>
}

export const MobileMenu = ({ isOpen, setIsOpen }: Props) => {
  const ref = useClickOutside(() => setIsOpen(false))

  const variants = {
    open: { opacity: 1, y: 20 },
    closed: { opacity: 0, y: 0 },
  }

  return (
    <div
      ref={ref}
      className={cn(
        "absolute inset-x-0 top-12 z-20 flex size-full flex-1 bg-inherit p-4",
        isOpen ? "flex" : "hidden",
      )}
    >
      <motion.div
        initial='closed'
        animate={isOpen ? "open" : "closed"}
        variants={variants}
        transition={{
          type: "spring",
          bounce: 0.15,
          duration: 0.5,
        }}
        className='flex size-full flex-col justify-start'
      >
        <ul className='flex w-full flex-1 flex-col items-start space-y-3'>
          <li
            onClick={() => setIsOpen(false)}
            className='w-full transform cursor-pointer rounded-md px-4 py-2 text-start text-lg font-normal text-foreground transition hover:bg-muted/20 hover:text-muted-foreground active:scale-95 active:opacity-80'
          >
            <Link
              href='/how-it-works'
              className='flex w-full items-center text-start'
            >
              <UserCog className='mr-2 h-4 w-4' />
              How it works
            </Link>
          </li>
          <Accordion type='single' collapsible className='w-full'>
            <AccordionItem value='item-1' className='border-transparent'>
              <AccordionTrigger className='px-4 py-2 text-lg font-normal hover:text-muted-foreground'>
                <span className='flex items-center'>
                  <CopyCheck className='mr-2 h-4 w-4' />
                  Features
                </span>
              </AccordionTrigger>
              <AccordionContent
                onClick={() => setIsOpen(false)}
                className='mt-1 flex flex-col items-start gap-1'
              >
                <li className='w-full transform cursor-pointer rounded-md px-4 py-2 text-start text-lg font-normal text-foreground/80 transition hover:bg-muted/20 hover:text-muted-foreground active:scale-95 active:opacity-80'>
                  <Link
                    href='/'
                    className='flex w-full items-center text-start'
                  >
                    <Captions className='mr-2 h-4 w-4' />
                    Caption Generation
                  </Link>
                </li>
                <li className='w-full transform cursor-pointer rounded-md px-4 py-2 text-start text-lg font-normal text-foreground/80 transition hover:bg-muted/20 hover:text-muted-foreground active:scale-95 active:opacity-80'>
                  <Link
                    href='/'
                    className='flex w-full items-center text-start'
                  >
                    <CalendarClock className='mr-2 h-4 w-4' />
                    Post Scheduling
                  </Link>
                </li>
                <li className='w-full transform cursor-pointer rounded-md px-4 py-2 text-start text-lg font-normal text-foreground/80 transition hover:bg-muted/20 hover:text-muted-foreground active:scale-95 active:opacity-80'>
                  <Link
                    href='/'
                    className='flex w-full items-center text-start'
                  >
                    <LineChart className='mr-2 h-4 w-4' />
                    Analytics Dashboard
                  </Link>
                </li>
              </AccordionContent>
            </AccordionItem>
          </Accordion>
          <li
            onClick={() => setIsOpen(false)}
            className='w-full transform cursor-pointer rounded-md px-4 py-2 text-start text-lg font-normal text-foreground transition hover:bg-muted/20 hover:text-muted-foreground active:scale-95 active:opacity-80'
          >
            <Link href='/' className='flex w-full items-center text-start'>
              <Gem className='mr-2 h-4 w-4' />
              Pricing
            </Link>
          </li>
          <li
            onClick={() => setIsOpen(false)}
            className='w-full transform cursor-pointer rounded-md px-4 py-2 text-start text-lg font-normal text-foreground transition hover:bg-muted/20 hover:text-muted-foreground active:scale-95 active:opacity-80'
          >
            <Link href='/' className='flex w-full items-center text-start'>
              <Waypoints className='mr-2 h-4 w-4' />
              Integrations
            </Link>
          </li>
          <Accordion type='single' collapsible className='w-full'>
            <AccordionItem value='item-1' className='border-transparent'>
              <AccordionTrigger className='px-4 py-2 text-lg font-normal hover:text-muted-foreground'>
                <span className='flex items-center'>
                  <Layers3 className='mr-2 h-4 w-4' />
                  Resources
                </span>
              </AccordionTrigger>
              <AccordionContent
                onClick={() => setIsOpen(false)}
                className='mt-1 flex flex-col items-start gap-1'
              >
                <li className='w-full transform cursor-pointer rounded-md px-4 py-2 text-start text-lg font-normal text-foreground/80 transition hover:bg-muted/20 hover:text-muted-foreground active:scale-95 active:opacity-80'>
                  <Link
                    href='/'
                    className='flex w-full items-center text-start'
                  >
                    <Newspaper className='mr-2 h-4 w-4' />
                    Blog
                  </Link>
                </li>
                <li className='w-full transform cursor-pointer rounded-md px-4 py-2 text-start text-lg font-normal text-foreground/80 transition hover:bg-muted/20 hover:text-muted-foreground active:scale-95 active:opacity-80'>
                  <Link
                    href='/'
                    className='flex w-full items-center text-start'
                  >
                    <FileText className='mr-2 h-4 w-4' />
                    Case Studies
                  </Link>
                </li>
                <li className='w-full transform cursor-pointer rounded-md px-4 py-2 text-start text-lg font-normal text-foreground/80 transition hover:bg-muted/20 hover:text-muted-foreground active:scale-95 active:opacity-80'>
                  <Link
                    href='/'
                    className='flex w-full items-center text-start'
                  >
                    <Box className='mr-2 h-4 w-4' />
                    Tools
                  </Link>
                </li>
                <li className='w-full transform cursor-pointer rounded-md px-4 py-2 text-start text-lg font-normal text-foreground/80 transition hover:bg-muted/20 hover:text-muted-foreground active:scale-95 active:opacity-80'>
                  <Link
                    href='/'
                    className='flex w-full items-center text-start'
                  >
                    <CircleHelp className='mr-2 h-4 w-4' />
                    Support
                  </Link>
                </li>
              </AccordionContent>
            </AccordionItem>
          </Accordion>
        </ul>
      </motion.div>
    </div>
  )
}
