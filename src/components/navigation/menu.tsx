"use client"

import {
  CalendarRangeIcon,
  CircleHelp,
  HashIcon,
  Newspaper,
  UserCircleIcon,
  UsersIcon,
} from "lucide-react"
import Link from "next/link"
import React from "react"

import {
  NavigationMenu,
  NavigationMenuContent,
  NavigationMenuItem,
  NavigationMenuLink,
  NavigationMenuList,
  NavigationMenuTrigger,
} from "@/components/ui/navigation-menu"

import { Icons } from "../global"

interface ItemProps {
  title: string
  href: string
  children: React.ReactNode
  icon: React.ReactNode
}

export const Menu = () => {
  return (
    <NavigationMenu>
      <NavigationMenuList>
        <NavigationMenuItem>
          <Link href='/templates' legacyBehavior passHref>
            <NavigationMenuLink className='h-10 w-max rounded-md px-4 py-2 text-sm font-normal text-muted-foreground hover:bg-none hover:text-foreground'>
              Templates
            </NavigationMenuLink>
          </Link>
        </NavigationMenuItem>
        <NavigationMenuItem>
          <NavigationMenuTrigger className='text-muted-foreground hover:text-foreground'>
            Products
          </NavigationMenuTrigger>
          <NavigationMenuContent>
            <ul className='grid gap-3 rounded-3xl p-4 md:w-[400px] lg:w-[500px] lg:grid-cols-[.75fr_1fr] xl:w-[550px]'>
              <li className='row-span-3'>
                <NavigationMenuLink asChild>
                  <Link
                    href='/'
                    className='flex h-full w-full select-none flex-col justify-end rounded-lg bg-gradient-to-tr from-accent to-accent/50 p-4 no-underline outline-none focus:shadow-md'
                  >
                    <Icons.icon className='h-6 w-6' />
                    <div className='my-2 text-lg font-normal'>DesignByte</div>
                    <p className='text-sm text-muted-foreground'>
                      Quality-first developer resources built to strict
                      standards.
                    </p>
                  </Link>
                </NavigationMenuLink>
              </li>
              <Item
                title='Templates'
                href='/templates'
                icon={<CalendarRangeIcon className='h-5 w-5' />}
              >
                TypeScript-first templates with Tailwind CSS and Shadcn UI
              </Item>
              <Item
                title='Themes'
                href='/themes'
                icon={<HashIcon className='h-5 w-5' />}
              >
                Modern themes with proper tooling setup and best practices
              </Item>
              <Item
                title='Starter Kits'
                href='/starter-kits'
                icon={<UsersIcon className='h-5 w-5' />}
              >
                Production-ready starter kits with quality standards built-in
              </Item>
            </ul>
          </NavigationMenuContent>
        </NavigationMenuItem>
        <NavigationMenuItem>
          <Link href='/pricing' legacyBehavior passHref>
            <NavigationMenuLink className='h-10 w-max rounded-md px-4 py-2 text-sm font-normal text-muted-foreground hover:bg-none hover:text-foreground'>
              Pricing
            </NavigationMenuLink>
          </Link>
        </NavigationMenuItem>

        {/* <NavigationMenuItem>
          <Link href="/blogs" legacyBehavior passHref>
            <NavigationMenuLink className="h-10 px-4 py-2 text-sm font-normal rounded-md text-muted-foreground hover:text-foreground w-max hover:bg-none">
              Blogs
            </NavigationMenuLink>
          </Link>
        </NavigationMenuItem> */}

        <NavigationMenuItem>
          <NavigationMenuTrigger className='text-muted-foreground hover:text-foreground'>
            Company
          </NavigationMenuTrigger>
          <NavigationMenuContent>
            <ul className='grid w-[400px] gap-3 p-4 md:w-[400px] md:grid-cols-2 lg:w-[500px] xl:w-[500px]'>
              <Item
                title='Figma Kits'
                href='/figma-kits'
                icon={<HashIcon className='h-5 w-5' />}
              >
                Design resources that pair perfectly with our code templates.
              </Item>
              <Item
                title='Blog'
                href='/blogs'
                icon={<Newspaper className='h-5 w-5' />}
              >
                Read our latest articles and updates.
              </Item>
              <Item
                title='About'
                href='/about-us'
                icon={<UserCircleIcon className='h-5 w-5' />}
              >
                Learn more about our company and team.
              </Item>
              <Item
                title='Contact'
                href='/contact-us'
                icon={<CircleHelp className='h-5 w-5' />}
              >
                Get help with any issues you may have.
              </Item>
            </ul>
          </NavigationMenuContent>
        </NavigationMenuItem>
      </NavigationMenuList>
    </NavigationMenu>
  )
}

const Item = ({ title, href, children, icon, ...props }: ItemProps) => {
  return (
    <li>
      <NavigationMenuLink asChild>
        <Link
          passHref
          href={href}
          {...props}
          className='group grid select-none grid-cols-[.15fr_1fr] space-y-1 rounded-lg p-3 leading-none no-underline outline-none transition-colors hover:bg-accent/50 hover:text-accent-foreground focus:bg-accent focus:text-accent-foreground'
        >
          <div className='mt-1 flex h-8 w-8 items-center justify-center rounded-md border border-border/80 p-1'>
            {icon}
          </div>
          <div className='ml-3 text-start'>
            <span className='text-sm font-normal leading-none group-hover:text-foreground'>
              {title}
            </span>
            <p className='mt-0.5 line-clamp-2 text-sm text-muted-foreground'>
              {children}
            </p>
          </div>
        </Link>
      </NavigationMenuLink>
    </li>
  )
}

Item.displayName = "Item"
