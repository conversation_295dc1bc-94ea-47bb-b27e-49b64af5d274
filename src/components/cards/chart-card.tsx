import {
  Area,
  Area<PERSON>hart,
  CartesianGrid,
  ResponsiveContainer,
  <PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON>,
} from "recharts"

import { Container } from "@/components/layout"
import { Card, CardContent, CardHeader } from "@/components/ui/card"
import { ChartConfig } from "@/components/ui/chart"
import { GlowingEffect } from "@/components/ui/glowing-effect"
import { Tabs, TabsList, TabsTrigger } from "@/components/ui/tabs"

interface ChartCardProps {
  title: string
  subtitle: string
  data: any[]
  config: ChartConfig
  delay?: number
}

export function ChartCard({
  title,
  subtitle,
  data,
  config,
  delay = 0,
}: ChartCardProps) {
  return (
    <Container delay={delay} className='col-span-full h-max lg:col-span-4'>
      <div className='relative h-full rounded-xl border p-1'>
        <GlowingEffect
          spread={30}
          glow={true}
          disabled={false}
          proximity={50}
        />
        <Card className='h-full bg-transparent'>
          <CardHeader>
            <div className='flex items-center justify-between'>
              <div>
                <h3 className='text-lg font-semibold'>{title}</h3>
                <p className='text-sm text-muted-foreground'>{subtitle}</p>
              </div>
              <Tabs defaultValue='weekly' className='w-[200px]'>
                <TabsList className='grid w-full grid-cols-3'>
                  <TabsTrigger value='daily'>Daily</TabsTrigger>
                  <TabsTrigger value='weekly'>Weekly</TabsTrigger>
                  <TabsTrigger value='monthly'>Monthly</TabsTrigger>
                </TabsList>
              </Tabs>
            </div>
          </CardHeader>
          <CardContent>
            <div className='mt-4 flex items-center justify-between'>
              {Object.entries(config).map(([key, value]) => (
                <div key={key} className='flex items-center gap-2'>
                  <div
                    className='h-3 w-3 rounded-full'
                    style={{ backgroundColor: value.color }}
                  />
                  <span className='text-sm text-muted-foreground'>
                    {value.label}
                  </span>
                </div>
              ))}
            </div>
            <div className='h-[300px] w-full'>
              <ResponsiveContainer width='100%' height='100%'>
                <AreaChart
                  data={data}
                  margin={{ top: 20, right: 0, left: 0, bottom: 0 }}
                >
                  <CartesianGrid
                    strokeDasharray='3 3'
                    className='stroke-muted/20'
                  />
                  <XAxis
                    dataKey='name'
                    tickLine={false}
                    axisLine={false}
                    className='text-xs text-muted-foreground'
                  />
                  <YAxis
                    tickLine={false}
                    axisLine={false}
                    className='text-xs text-muted-foreground'
                  />
                  {Object.keys(config).map((key) => (
                    <Area
                      key={key}
                      type='monotone'
                      dataKey={key}
                      stroke={config[key].color}
                      fill={config[key].color}
                      fillOpacity={0.1}
                      strokeWidth={2}
                    />
                  ))}
                </AreaChart>
              </ResponsiveContainer>
            </div>
          </CardContent>
        </Card>
      </div>
    </Container>
  )
}
