import { LucideIcon } from "lucide-react"

import { Card, CardContent } from "@/components/ui/card"

interface AboutValueCardProps {
  icon: LucideIcon
  title: string
  description: string
  colorClass: string
}

export const AboutValueCard = ({
  icon: Icon,
  title,
  description,
  colorClass,
}: AboutValueCardProps) => {
  return (
    <Card className='group relative transition-all duration-300 hover:shadow-lg'>
      <CardContent className='p-6 text-center'>
        <div
          className={`mb-4 inline-flex h-12 w-12 items-center justify-center rounded-xl transition-all duration-300 group-hover:scale-105 ${colorClass}`}
        >
          <Icon className='h-6 w-6' />
        </div>
        <h4 className='mb-2 font-semibold'>{title}</h4>
        <p className='text-sm text-muted-foreground'>{description}</p>
      </CardContent>
    </Card>
  )
}
