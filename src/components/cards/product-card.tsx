"use client"

import { <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Trash } from "lucide-react"
import Image from "next/image"
import Link from "next/link"
import { useState } from "react"

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { GlowingEffect } from "@/components/ui/glowing-effect"
import { Product } from "@/types/product"
import { createArrayFromString } from "@/utils/generic"

interface ProductCardProps {
  product: Product
  onDelete?: (id: string) => void
}

export function ProductCard({ product, onDelete }: ProductCardProps) {
  const [isHovered, setIsHovered] = useState(false)

  // Format price with currency
  const formattedPrice = new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "USD",
  }).format(product.price || 0)

  // Get first image or placeholder
  const imageUrl = product.images
    ? createArrayFromString(product.images)[0]
    : "https://placehold.co/600x400?text=No+Image"

  const handleDelete = () => {
    if (window.confirm("Are you sure you want to delete this product?")) {
      if (!product._id) return
      // eslint-disable-next-line @typescript-eslint/no-unused-expressions
      onDelete && onDelete(product._id)
    }
  }

  return (
    <div
      className='relative rounded-xl border p-1 transition-all duration-300 hover:shadow-md'
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <GlowingEffect
        spread={30}
        glow={isHovered}
        disabled={false}
        proximity={50}
      />
      <div className='relative flex h-full flex-col overflow-hidden rounded-lg bg-card'>
        {/* Product Image */}
        <div className='relative aspect-[4/3] w-full overflow-hidden'>
          <Image
            src={imageUrl}
            alt={product.name || "Product"}
            fill
            className='object-cover transition-transform duration-500 group-hover:scale-105'
          />

          {/* Featured Badge */}
          {product.isFeatured && (
            <div className='absolute left-2 top-2 rounded-full bg-primary px-2 py-1 text-xs font-medium text-primary-foreground'>
              Featured
            </div>
          )}

          {/* Actions Menu */}
          <div className='absolute right-2 top-2'>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <button className='flex h-8 w-8 items-center justify-center rounded-full bg-black/50 text-white backdrop-blur-sm transition-colors hover:bg-black/70'>
                  <MoreHorizontal className='h-4 w-4' />
                </button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align='end'>
                <DropdownMenuLabel>Actions</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem asChild>
                  <Link
                    href={`/dashboard/products/${product._id}`}
                    className='flex cursor-pointer items-center'
                  >
                    <Edit className='mr-2 h-4 w-4' />
                    Edit
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <Link
                    href={`/${product.type}/${product.slug}`}
                    className='flex cursor-pointer items-center'
                  >
                    <Eye className='mr-2 h-4 w-4' />
                    View
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuItem
                  className='flex cursor-pointer items-center text-destructive focus:text-destructive'
                  onClick={handleDelete}
                >
                  <Trash className='mr-2 h-4 w-4' />
                  Delete
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>

        {/* Product Info */}
        <div className='flex flex-1 flex-col p-4'>
          <h3 className='line-clamp-1 text-lg font-semibold'>{product.name}</h3>
          <p className='mt-1 line-clamp-2 text-sm text-muted-foreground'>
            {product.description}
          </p>

          <div className='mt-auto pt-4'>
            <div className='flex items-center justify-between'>
              <span className='font-medium text-primary'>{formattedPrice}</span>
              <span className='text-xs text-muted-foreground'>
                {product.type}
              </span>
            </div>

            {/* Tags/TechStack */}
            {product.techStack && product.techStack.length > 0 && (
              <div className='mt-2 flex flex-wrap gap-1'>
                {product.techStack.slice(0, 3).map((tech, index) => (
                  <span
                    key={index}
                    className='rounded-full bg-secondary px-2 py-0.5 text-xs'
                  >
                    {tech}
                  </span>
                ))}
                {product.techStack.length > 3 && (
                  <span className='rounded-full bg-secondary px-2 py-0.5 text-xs'>
                    +{product.techStack.length - 3}
                  </span>
                )}
              </div>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
