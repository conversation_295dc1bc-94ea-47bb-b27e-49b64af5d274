"use client"

import { <PERSON>, <PERSON>, <PERSON>H<PERSON><PERSON><PERSON>, Trash } from "lucide-react"
import Link from "next/link"
import { useState } from "react"

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { GlowingEffect } from "@/components/ui/glowing-effect"
import { Plan } from "@/types/plan"

interface PlanCardProps {
  plan: Plan
  onDelete?: (id: string) => void
}

export function PlanCard({ plan, onDelete }: PlanCardProps) {
  const [isHovered, setIsHovered] = useState(false)

  // Format price with currency
  const formattedPrice = new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: plan.currency || "USD",
  }).format(plan.price || 0)

  return (
    <div
      className='relative rounded-xl border p-1 transition-all duration-300 hover:shadow-md'
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <GlowingEffect
        spread={30}
        glow={isHovered}
        disabled={false}
        proximity={50}
      />
      <div className='relative flex h-full flex-col overflow-hidden rounded-lg bg-card'>
        {/* Plan Header */}
        <div className='bg-primary/10 p-4'>
          <div className='flex items-center justify-between'>
            <h3 className='text-xl font-bold text-primary'>{plan.name}</h3>

            {/* Actions Menu */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <button className='flex h-8 w-8 items-center justify-center rounded-full bg-black/10 text-foreground transition-colors hover:bg-black/20'>
                  <MoreHorizontal className='h-4 w-4' />
                </button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align='end'>
                <DropdownMenuLabel>Actions</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem asChild>
                  <Link
                    href={`/dashboard/plans/${plan._id}`}
                    className='flex cursor-pointer items-center'
                  >
                    <Edit className='mr-2 h-4 w-4' />
                    Edit
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <Link
                    href={`/dashboard/plans/${plan._id}/view`}
                    className='flex cursor-pointer items-center'
                  >
                    <Eye className='mr-2 h-4 w-4' />
                    View
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuItem
                  className='flex cursor-pointer items-center text-destructive focus:text-destructive'
                  onClick={() => onDelete && onDelete(plan._id!)}
                >
                  <Trash className='mr-2 h-4 w-4' />
                  Delete
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>

          <div className='mt-2 flex items-baseline'>
            <span className='text-2xl font-bold'>{formattedPrice}</span>
            <span className='ml-1 text-sm text-muted-foreground'>
              /{plan.duration}
            </span>
          </div>
        </div>

        {/* Plan Info */}
        <div className='flex flex-1 flex-col p-4'>
          {plan.description && (
            <p className='mb-4 text-sm text-muted-foreground'>
              {plan.description}
            </p>
          )}

          {/* Plan Features */}
          <div className='mt-auto space-y-2'>
            {plan.benefits && (
              <div className='space-y-2'>
                <h4 className='font-medium'>Benefits:</h4>
                <p className='text-sm text-muted-foreground'>{plan.benefits}</p>
              </div>
            )}

            <div className='flex flex-wrap gap-2 pt-4'>
              {plan.maxTemplates && (
                <span className='rounded-full bg-secondary px-2 py-0.5 text-xs'>
                  {plan.maxTemplates} Templates
                </span>
              )}

              {plan.maxUsers && (
                <span className='rounded-full bg-secondary px-2 py-0.5 text-xs'>
                  {plan.maxUsers} Users
                </span>
              )}

              <span
                className={`rounded-full px-2 py-0.5 text-xs font-medium ${
                  plan.isActive
                    ? "bg-green-500/10 text-green-500"
                    : "bg-red-500/10 text-red-500"
                }`}
              >
                {plan.isActive ? "Active" : "Inactive"}
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
