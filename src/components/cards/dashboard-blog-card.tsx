"use client"

import { <PERSON>, Eye, <PERSON>H<PERSON>zon<PERSON>, Trash } from "lucide-react"
import Image from "next/image"
import Link from "next/link"
import { useState } from "react"

import { Container } from "@/components/layout"
import { Card } from "@/components/ui/card"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { GlowingEffect } from "@/components/ui/glowing-effect"
import { Blog } from "@/types/blog"
import { formatDate } from "@/utils/date"

interface DashboardBlogCardProps {
  blog: Blog
  onDelete?: (id: string) => void
  delay?: number
}

export function DashboardBlogCard({
  blog,
  onDelete,
  delay = 0,
}: DashboardBlogCardProps) {
  const [isHovered, setIsHovered] = useState(false)

  // Format date
  const formattedDate = blog.createdAt ? formatDate(blog.createdAt) : "No date"

  // Get image or placeholder
  const imageUrl =
    blog?.thumbnailUrl || "https://placehold.co/600x400?text=No+Image"

  return (
    <Container delay={delay}>
      <div
        className='relative h-full rounded-xl border p-1 transition-all duration-300'
        onMouseEnter={() => setIsHovered(true)}
        onMouseLeave={() => setIsHovered(false)}
      >
        <GlowingEffect
          spread={30}
          glow={isHovered}
          disabled={false}
          proximity={50}
        />
        <Card className='h-full overflow-hidden bg-transparent'>
          {/* Blog Image */}
          <div className='relative aspect-video w-full overflow-hidden'>
            <Image
              src={imageUrl}
              alt={blog.title || "Blog"}
              fill
              className='object-cover transition-transform duration-500 hover:scale-105'
            />

            {/* Actions Menu */}
            <div className='absolute right-2 top-2 z-10'>
              <DropdownMenu>
                <DropdownMenuTrigger asChild>
                  <button className='flex h-8 w-8 items-center justify-center rounded-full bg-black/50 text-white backdrop-blur-sm transition-colors hover:bg-black/70'>
                    <MoreHorizontal className='h-4 w-4' />
                  </button>
                </DropdownMenuTrigger>
                <DropdownMenuContent align='end'>
                  <DropdownMenuLabel>Actions</DropdownMenuLabel>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem asChild>
                    <Link
                      href={`/dashboard/blogs/${blog._id}`}
                      className='flex cursor-pointer items-center'
                    >
                      <Edit className='mr-2 h-4 w-4' />
                      Edit
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuItem asChild>
                    <Link
                      href={`/blogs/${blog.slug}`}
                      className='flex cursor-pointer items-center'
                    >
                      <Eye className='mr-2 h-4 w-4' />
                      View
                    </Link>
                  </DropdownMenuItem>
                  <DropdownMenuSeparator />
                  <DropdownMenuItem
                    className='flex cursor-pointer items-center text-destructive focus:text-destructive'
                    onClick={() => {
                      if (!blog?._id) return
                      onDelete?.(blog?._id)
                    }}
                  >
                    <Trash className='mr-2 h-4 w-4' />
                    Delete
                  </DropdownMenuItem>
                </DropdownMenuContent>
              </DropdownMenu>
            </div>

            {/* Gradient Overlay */}
            <div className='absolute inset-0 bg-gradient-to-t from-black/60 to-transparent' />
          </div>

          {/* Blog Info */}
          <div className='flex flex-1 flex-col p-4'>
            <div className='mb-2 flex items-center gap-2'>
              <span className='rounded-full bg-primary/10 px-2 py-0.5 text-xs font-medium text-primary'>
                {blog.author || "Unknown Author"}
              </span>
              <span className='text-xs text-muted-foreground'>
                {formattedDate}
              </span>
            </div>

            <h3 className='line-clamp-2 text-lg font-semibold'>{blog.title}</h3>
            <p className='mt-1 line-clamp-2 text-sm text-muted-foreground'>
              {blog.content?.substring(0, 120)}...
            </p>

            {/* Categories */}
            {blog.categories && blog.categories.length > 0 && (
              <div className='mt-auto flex flex-wrap gap-1 pt-4'>
                {blog.categories.slice(0, 3).map((category, index) => (
                  <span
                    key={index}
                    className='rounded-full bg-secondary px-2 py-0.5 text-xs'
                  >
                    {category}
                  </span>
                ))}
                {blog.categories.length > 3 && (
                  <span className='rounded-full bg-secondary px-2 py-0.5 text-xs'>
                    +{blog.categories.length - 3}
                  </span>
                )}
              </div>
            )}
          </div>
        </Card>
      </div>
    </Container>
  )
}
