import { <PERSON>, <PERSON>, Eye, User } from "lucide-react"
import Image from "next/image"
import Link from "next/link"

import { Badge } from "@/components/ui/badge"
import { <PERSON><PERSON> } from "@/components/ui/button"
import { <PERSON>, Card<PERSON>ontent, <PERSON><PERSON>ooter, CardHeader } from "@/components/ui/card"
import type { BlogPost } from "@/constants/blogs"
import { cn } from "@/lib/utils"

interface BlogPostCardProps {
  post: BlogPost
  variant?: "default" | "featured" | "compact"
  className?: string
}

const getCategoryColor = (category: string) => {
  const colors = {
    typescript: "bg-blue-500/10 text-blue-400 border-blue-500/20",
    "ui-design": "bg-purple-500/10 text-purple-400 border-purple-500/20",
    "developer-tools": "bg-green-500/10 text-green-400 border-green-500/20",
    nextjs: "bg-gray-500/10 text-gray-400 border-gray-500/20",
    "web-development": "bg-orange-500/10 text-orange-400 border-orange-500/20",
    react: "bg-cyan-500/10 text-cyan-400 border-cyan-500/20",
  }
  return (
    colors[category as keyof typeof colors] ||
    "bg-gray-500/10 text-gray-400 border-gray-500/20"
  )
}

export const BlogPostCard = ({
  post,
  variant = "default",
  className,
}: BlogPostCardProps) => {
  const isCompact = variant === "compact"
  const isFeatured = variant === "featured"

  return (
    <Card
      className={cn(
        "group relative overflow-hidden transition-all duration-300 hover:shadow-lg",
        isFeatured && "border-primary/20 shadow-md",
        isCompact && "flex-row",
        className,
      )}
    >
      <div
        className={cn(
          "relative overflow-hidden",
          isCompact ? "w-1/3" : "aspect-video w-full",
        )}
      >
        <Image
          src={post.image}
          alt={post.title}
          fill
          className='object-cover transition-transform duration-500 group-hover:scale-105'
        />
        {isFeatured && (
          <div className='absolute left-3 top-3'>
            <Badge
              variant='secondary'
              className='bg-primary/90 text-primary-foreground'
            >
              Featured
            </Badge>
          </div>
        )}
        <div className='absolute inset-0 bg-gradient-to-t from-background/80 via-transparent to-transparent opacity-0 transition-opacity duration-300 group-hover:opacity-100' />
      </div>

      <div className={cn("flex flex-col", isCompact ? "w-2/3" : "w-full")}>
        <CardHeader className={cn("pb-3", isCompact && "pb-2")}>
          <div className='flex flex-wrap items-center gap-2 text-sm text-muted-foreground'>
            <Badge
              variant='outline'
              className={cn("text-xs", getCategoryColor(post.category))}
            >
              {post.category.replace("-", " ")}
            </Badge>
            {post.views && (
              <div className='flex items-center gap-1'>
                <Eye className='h-3 w-3' />
                <span className='text-xs'>{post.views.toLocaleString()}</span>
              </div>
            )}
          </div>

          <h3
            className={cn(
              "font-semibold leading-tight transition-colors group-hover:text-primary",
              isFeatured ? "text-xl" : "text-lg",
              isCompact && "text-base",
            )}
          >
            <Link
              href={`/blogs/${post.slug}`}
              className='after:absolute after:inset-0'
            >
              {post.title}
            </Link>
          </h3>
        </CardHeader>

        <CardContent className={cn("flex-1 pb-3", isCompact && "pb-2")}>
          <p
            className={cn(
              "line-clamp-2 text-muted-foreground",
              isCompact ? "line-clamp-2 text-sm" : "line-clamp-3 text-sm",
            )}
          >
            {post.description}
          </p>

          <div className='mt-3 flex flex-wrap gap-1'>
            {post.tags.slice(0, isCompact ? 2 : 3).map((tag) => (
              <Badge key={tag} variant='secondary' className='text-xs'>
                {tag}
              </Badge>
            ))}
            {post.tags.length > (isCompact ? 2 : 3) && (
              <Badge variant='secondary' className='text-xs'>
                +{post.tags.length - (isCompact ? 2 : 3)}
              </Badge>
            )}
          </div>
        </CardContent>

        <CardFooter
          className={cn(
            "flex items-center justify-between pt-0",
            isCompact && "flex-col items-start gap-2",
          )}
        >
          <div className='flex items-center gap-4 text-xs text-muted-foreground'>
            <div className='flex items-center gap-1'>
              <User className='h-3 w-3' />
              <span>{post.author.name}</span>
            </div>
            <div className='flex items-center gap-1'>
              <Calendar className='h-3 w-3' />
              <span>{post.date}</span>
            </div>
            <div className='flex items-center gap-1'>
              <Clock className='h-3 w-3' />
              <span>{post.readTime}</span>
            </div>
          </div>

          {!isCompact && (
            <Button size='sm' variant='ghost' asChild className='relative z-10'>
              <Link href={`/blogs/${post.slug}`}>Read More</Link>
            </Button>
          )}
        </CardFooter>
      </div>
    </Card>
  )
}
