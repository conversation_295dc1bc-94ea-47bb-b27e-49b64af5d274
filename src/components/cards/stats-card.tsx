import { LucideIcon } from "lucide-react"

import { Container } from "@/components/layout"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { GlowingEffect } from "@/components/ui/glowing-effect"

interface StatsCardProps {
  title: string
  value: string
  change: {
    value: string
    trend: "up" | "down"
  }
  icon: LucideIcon
  iconColor: string
  delay?: number
}

export function StatsCard({
  title,
  value,
  change,
  icon: Icon,
  iconColor,
  delay = 0,
}: StatsCardProps) {
  return (
    <Container delay={delay}>
      <div className='relative h-full rounded-xl border p-1'>
        <GlowingEffect
          spread={30}
          glow={true}
          disabled={false}
          proximity={50}
        />
        <Card className='h-full bg-transparent'>
          <CardHeader className='flex flex-row items-center justify-between space-y-0 pb-2'>
            <CardTitle className='text-sm font-medium'>{title}</CardTitle>
            <div className={`rounded-full bg-${iconColor}/10 p-2`}>
              <Icon className={`h-4 w-4 text-${iconColor}`} />
            </div>
          </CardHeader>
          <CardContent>
            <div className='text-2xl font-bold'>{value}</div>
            <p className='flex items-center text-xs text-muted-foreground'>
              <span
                className={`mr-1 rounded-sm bg-${
                  change.trend === "up" ? "green" : "red"
                }-500/20 px-1 py-0.5 text-${
                  change.trend === "up" ? "green" : "red"
                }-500`}
              >
                {change.value}
              </span>
              from last month
            </p>
          </CardContent>
        </Card>
      </div>
    </Container>
  )
}
