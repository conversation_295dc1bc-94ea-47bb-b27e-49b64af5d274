import { CheckCircle, LucideIcon, Shield, Star } from "lucide-react"

import { Card, CardContent } from "@/components/ui/card"

interface AboutMissionCardProps {
  icon: LucideIcon
  title: string
  description: string
  badgeText: string
  badgeColor: "green" | "yellow" | "purple"
}

const BadgeIcon = {
  green: CheckCircle,
  yellow: Star,
  purple: Shield,
}

const badgeColors = {
  green: "text-green-600",
  yellow: "text-yellow-600",
  purple: "text-purple-600",
}

const iconGradients = {
  green: "bg-gradient-to-br from-blue-500 to-blue-600",
  yellow: "bg-gradient-to-br from-emerald-500 to-emerald-600",
  purple: "bg-gradient-to-br from-purple-500 to-purple-600",
}

const hoverColors = {
  green: "hover:border-blue-200 dark:hover:border-blue-800",
  yellow: "hover:border-emerald-200 dark:hover:border-emerald-800",
  purple: "hover:border-purple-200 dark:hover:border-purple-800",
}

export const AboutMissionCard = ({
  icon: Icon,
  title,
  description,
  badgeText,
  badgeColor,
}: AboutMissionCardProps) => {
  const BadgeIconComponent = BadgeIcon[badgeColor]
  const badgeColorClass = badgeColors[badgeColor]
  const iconGradient = iconGradients[badgeColor]
  const hoverColor = hoverColors[badgeColor]

  return (
    <Card
      className={`group relative overflow-hidden transition-all duration-500 hover:shadow-2xl ${hoverColor}`}
    >
      <CardContent className='relative p-8'>
        <div
          className={`mb-6 inline-flex h-16 w-16 items-center justify-center rounded-2xl ${iconGradient} text-white shadow-lg transition-all duration-500 group-hover:shadow-xl`}
        >
          <Icon className='h-8 w-8' />
        </div>
        <h3 className='mb-4 text-xl font-semibold'>{title}</h3>
        <p className='leading-relaxed text-muted-foreground'>{description}</p>
        <div className='mt-4 flex items-center gap-2'>
          <BadgeIconComponent className={`h-4 w-4 ${badgeColorClass}`} />
          <span className={`text-sm font-medium ${badgeColorClass}`}>
            {badgeText}
          </span>
        </div>
      </CardContent>
    </Card>
  )
}
