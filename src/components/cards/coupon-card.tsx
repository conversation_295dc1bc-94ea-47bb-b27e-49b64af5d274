"use client"

import { <PERSON>, <PERSON>, <PERSON>H<PERSON>zon<PERSON>, Trash } from "lucide-react"
import Link from "next/link"
import { useState } from "react"

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { GlowingEffect } from "@/components/ui/glowing-effect"
import { Coupon } from "@/types/coupon"

interface CouponCardProps {
  coupon: Coupon
  onDelete?: (id: string) => void
}

export function CouponCard({ coupon, onDelete }: CouponCardProps) {
  const [isHovered, setIsHovered] = useState(false)

  // Format dates
  const formatDate = (date: Date | string | undefined) => {
    if (!date) return "N/A"
    return new Date(date).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    })
  }

  const validFrom = formatDate(coupon.validFrom)
  const validUntil = formatDate(coupon.validUntil)

  // Format discount
  const formatDiscount = () => {
    if (coupon.discountType === "percentage") {
      return `${coupon.discountValue}% off`
    } else {
      return `$${coupon.discountValue} off`
    }
  }

  // Check if coupon is expired
  const isExpired = coupon.validUntil
    ? new Date(coupon.validUntil) < new Date()
    : false

  // Check if coupon is active
  const isActive = coupon.isActive && !isExpired

  return (
    <div
      className='relative rounded-xl border p-1 transition-all duration-300 hover:shadow-md'
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <GlowingEffect
        spread={30}
        glow={isHovered}
        disabled={false}
        proximity={50}
      />
      <div className='relative flex h-full flex-col overflow-hidden rounded-lg bg-card'>
        {/* Coupon Header */}
        <div className='bg-primary/10 p-4'>
          <div className='flex items-center justify-between'>
            <h3 className='text-xl font-bold uppercase text-primary'>
              {coupon.code}
            </h3>

            {/* Actions Menu */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <button className='flex h-8 w-8 items-center justify-center rounded-full bg-black/10 text-foreground transition-colors hover:bg-black/20'>
                  <MoreHorizontal className='h-4 w-4' />
                </button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align='end'>
                <DropdownMenuLabel>Actions</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem asChild>
                  <Link
                    href={`/dashboard/coupons/${coupon._id}`}
                    className='flex cursor-pointer items-center'
                  >
                    <Edit className='mr-2 h-4 w-4' />
                    Edit
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <Link
                    href={`/dashboard/coupons/${coupon._id}/view`}
                    className='flex cursor-pointer items-center'
                  >
                    <Eye className='mr-2 h-4 w-4' />
                    View
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuItem
                  className='flex cursor-pointer items-center text-destructive focus:text-destructive'
                  onClick={() => onDelete && onDelete(coupon._id!)}
                >
                  <Trash className='mr-2 h-4 w-4' />
                  Delete
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>

          <div className='mt-2'>
            <span className='text-2xl font-bold'>{formatDiscount()}</span>
          </div>
        </div>

        {/* Coupon Info */}
        <div className='flex flex-1 flex-col p-4'>
          <div className='mb-4 space-y-2'>
            <div className='flex items-center justify-between'>
              <span className='text-sm font-medium'>Valid From:</span>
              <span className='text-sm'>{validFrom}</span>
            </div>
            <div className='flex items-center justify-between'>
              <span className='text-sm font-medium'>Valid Until:</span>
              <span className='text-sm'>{validUntil}</span>
            </div>

            {coupon.maxRedemptions && (
              <div className='flex items-center justify-between'>
                <span className='text-sm font-medium'>Usage Limit:</span>
                <span className='text-sm'>
                  {coupon.currentRedemptions || 0}/{coupon.maxRedemptions}
                </span>
              </div>
            )}
          </div>

          {/* Status Indicators */}
          <div className='mt-auto flex flex-wrap gap-2'>
            <span
              className={`rounded-full px-2 py-0.5 text-xs font-medium ${
                isActive
                  ? "bg-green-500/10 text-green-500"
                  : "bg-red-500/10 text-red-500"
              }`}
            >
              {isActive ? "Active" : "Inactive"}
            </span>

            {isExpired && (
              <span className='rounded-full bg-red-500/10 px-2 py-0.5 text-xs font-medium text-red-500'>
                Expired
              </span>
            )}

            <span className='rounded-full bg-secondary px-2 py-0.5 text-xs'>
              {coupon.discountType === "percentage"
                ? "Percentage"
                : "Fixed Amount"}
            </span>
          </div>
        </div>
      </div>
    </div>
  )
}
