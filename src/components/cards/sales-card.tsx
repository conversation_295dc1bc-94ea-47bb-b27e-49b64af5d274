import { ArrowUpIcon, CreditCardIcon, TrendingUpIcon } from "lucide-react"

import { Container } from "@/components/layout"
import { Card, CardContent, CardHeader } from "@/components/ui/card"
import { GlowingEffect } from "@/components/ui/glowing-effect"
import { Sale } from "@/constants/dashboard"

interface SalesCardProps {
  sales: Sale[]
  totalSales: string
  percentageIncrease: string
  delay?: number
}

const statusColors = {
  completed: "text-green-500",
  pending: "text-yellow-500",
  failed: "text-red-500",
}

export function SalesCard({
  sales,
  totalSales,
  percentageIncrease,
  delay = 0,
}: SalesCardProps) {
  return (
    <Container delay={delay}>
      <div className='relative h-full rounded-xl border p-1'>
        <GlowingEffect
          spread={30}
          glow={true}
          disabled={false}
          proximity={50}
        />
        <Card className='h-full bg-transparent'>
          <CardHeader>
            <div className='flex items-center justify-between'>
              <div>
                <h3 className='text-lg font-semibold'>Recent Sales</h3>
                <div className='mt-1 flex items-center gap-2'>
                  <span className='text-2xl font-bold'>{totalSales}</span>
                  <div className='flex items-center gap-1 rounded-full bg-green-500/10 px-2 py-1 text-xs font-medium text-green-500'>
                    <ArrowUpIcon className='h-3 w-3' />
                    {percentageIncrease}
                  </div>
                </div>
              </div>
              <div className='rounded-full bg-primary/10 p-3'>
                <TrendingUpIcon className='h-5 w-5 text-primary' />
              </div>
            </div>
          </CardHeader>
          <CardContent>
            <div className='space-y-4'>
              {sales.map((sale) => (
                <div
                  key={sale.email}
                  className='flex items-center justify-between rounded-lg border p-3 transition-colors hover:bg-muted/50'
                >
                  <div className='flex items-center gap-3'>
                    <div className='flex h-9 w-9 items-center justify-center rounded-full bg-primary/10'>
                      <CreditCardIcon className='h-4 w-4 text-primary' />
                    </div>
                    <div className='space-y-1'>
                      <p className='text-sm font-medium leading-none'>
                        {sale.name}
                      </p>
                      <div className='flex items-center gap-2'>
                        <span className='text-xs text-muted-foreground'>
                          {sale.email}
                        </span>
                        <span className='text-xs text-muted-foreground'>•</span>
                        <span className='text-xs text-muted-foreground'>
                          {sale.time}
                        </span>
                      </div>
                    </div>
                  </div>
                  <div className='text-right'>
                    <p className='font-medium'>{sale.amount}</p>
                    <p className={`text-xs ${statusColors[sale.status]}`}>
                      {sale.status.charAt(0).toUpperCase() +
                        sale.status.slice(1)}
                    </p>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </Container>
  )
}
