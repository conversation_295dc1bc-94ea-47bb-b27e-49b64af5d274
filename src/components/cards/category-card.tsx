"use client"

import { <PERSON>, <PERSON>, More<PERSON><PERSON><PERSON><PERSON>, Trash } from "lucide-react"
import Link from "next/link"
import { useState } from "react"

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { GlowingEffect } from "@/components/ui/glowing-effect"
import { Category } from "@/types/category"

interface CategoryCardProps {
  category: Category
  onDelete?: (id: string) => void
}

export function CategoryCard({ category, onDelete }: CategoryCardProps) {
  const [isHovered, setIsHovered] = useState(false)

  return (
    <div
      className='relative rounded-xl border p-1 transition-all duration-300 hover:shadow-md'
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <GlowingEffect
        spread={30}
        glow={isHovered}
        disabled={false}
        proximity={50}
      />
      <div className='relative flex h-full flex-col overflow-hidden rounded-lg bg-card'>
        {/* Category Info */}
        <div className='flex flex-1 flex-col p-6'>
          <div className='mb-4 flex items-center justify-between'>
            <h3 className='text-xl font-semibold'>{category.name}</h3>

            {/* Actions Menu */}
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <button className='flex h-8 w-8 items-center justify-center rounded-full bg-black/10 text-foreground transition-colors hover:bg-black/20'>
                  <MoreHorizontal className='h-4 w-4' />
                </button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align='end'>
                <DropdownMenuLabel>Actions</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem asChild>
                  <Link
                    href={`/dashboard/categories/${category._id}`}
                    className='flex cursor-pointer items-center'
                  >
                    <Edit className='mr-2 h-4 w-4' />
                    Edit
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <Link
                    href={`/dashboard/categories/${category._id}/view`}
                    className='flex cursor-pointer items-center'
                  >
                    <Eye className='mr-2 h-4 w-4' />
                    View
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuItem
                  className='flex cursor-pointer items-center text-destructive focus:text-destructive'
                  onClick={() => onDelete && onDelete(category._id!)}
                >
                  <Trash className='mr-2 h-4 w-4' />
                  Delete
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>

          {category.description && (
            <p className='mb-4 line-clamp-2 text-sm text-muted-foreground'>
              {category.description}
            </p>
          )}

          <div className='mt-auto flex flex-wrap gap-2'>
            <span className='rounded-full bg-primary/10 px-2 py-0.5 text-xs font-medium text-primary'>
              {category.type}
            </span>

            {category.isActive !== undefined && (
              <span
                className={`rounded-full px-2 py-0.5 text-xs font-medium ${
                  category.isActive
                    ? "bg-green-500/10 text-green-500"
                    : "bg-red-500/10 text-red-500"
                }`}
              >
                {category.isActive ? "Active" : "Inactive"}
              </span>
            )}

            {category.parent && (
              <span className='rounded-full bg-secondary px-2 py-0.5 text-xs'>
                Has Parent
              </span>
            )}

            <span className='rounded-full bg-secondary px-2 py-0.5 text-xs'>
              {category.slug || "No Slug"}
            </span>
          </div>
        </div>
      </div>
    </div>
  )
}
