"use client"
import { <PERSON><PERSON><PERSON>, Repeat2 } from "lucide-react"
import Image from "next/image"
import { useState } from "react"

import { cn } from "@/lib/utils"

interface TechCardProps {
  name: string
  logo: string
  description?: string
  className?: string
}

export const TechCard = ({
  name,
  logo,
  description,
  className,
}: TechCardProps) => {
  const [isFlipped, setIsFlipped] = useState(false)

  return (
    <div
      className={cn(
        "group relative aspect-square h-full w-full [perspective:2000px]",
        className,
      )}
      onMouseEnter={() => setIsFlipped(true)}
      onMouseLeave={() => setIsFlipped(false)}
    >
      <div
        className={cn(
          "relative h-full w-full transition-all duration-700 [transform-style:preserve-3d]",
          isFlipped
            ? "[transform:rotateY(180deg)]"
            : "[transform:rotateY(0deg)]",
        )}
      >
        {/* Front of card */}
        <div
          className={cn(
            "shadow-xs absolute inset-0 h-full w-full overflow-hidden rounded-2xl border border-zinc-200 bg-zinc-50 transition-all duration-700 [backface-visibility:hidden] group-hover:shadow-lg dark:border-zinc-800/50 dark:bg-zinc-900 dark:shadow-lg dark:group-hover:shadow-xl",
            isFlipped ? "opacity-0" : "opacity-100",
          )}
        >
          <div className='relative h-full overflow-hidden bg-gradient-to-b from-zinc-100 to-white dark:from-zinc-900 dark:to-black'>
            <div className='relative flex h-full w-full items-start justify-center pt-4 md:pt-8 lg:pt-10'>
              <div className='relative z-10 h-20 w-20 rounded-lg bg-background/80 p-2 ring-1 ring-border/50 transition-all duration-300 group-hover:scale-110 group-hover:bg-primary/5 group-hover:ring-primary/30'>
                <Image
                  src={logo}
                  alt={`${name} logo`}
                  fill
                  className='object-contain transition-transform duration-300'
                />
              </div>
            </div>
          </div>

          <div className='absolute bottom-0 left-0 right-0 p-5'>
            <div className='flex items-center justify-between gap-3'>
              <h3 className='text-lg font-semibold leading-snug tracking-tighter text-zinc-900 transition-all duration-500 ease-out group-hover:translate-y-[-4px] dark:text-white'>
                {name}
              </h3>
              <div className='group/icon relative'>
                <div
                  className={cn(
                    "absolute inset-[-8px] rounded-lg bg-gradient-to-br from-primary/30 via-primary/20 to-transparent transition-opacity duration-300",
                  )}
                />
                <Repeat2 className='relative z-10 h-4 w-4 text-primary transition-transform duration-300 group-hover/icon:-rotate-12 group-hover/icon:scale-110' />
              </div>
            </div>
          </div>
        </div>

        {/* Back of card */}
        <div
          className={cn(
            "shadow-xs absolute inset-0 flex h-full w-full flex-col rounded-2xl border border-zinc-200 bg-gradient-to-b from-zinc-100 to-white p-6 transition-all duration-700 [backface-visibility:hidden] [transform:rotateY(180deg)] group-hover:shadow-lg dark:border-zinc-800 dark:from-zinc-900 dark:to-black dark:shadow-lg dark:group-hover:shadow-xl",
            !isFlipped ? "opacity-0" : "opacity-100",
          )}
        >
          <div className='flex-1 space-y-6'>
            <div className='space-y-2'>
              <h3 className='text-lg font-semibold leading-snug tracking-tight text-zinc-900 transition-all duration-500 ease-out group-hover:translate-y-[-2px] dark:text-white'>
                {name}
              </h3>
              <p className='line-clamp-3 text-sm tracking-tight text-zinc-600 transition-all duration-500 ease-out group-hover:translate-y-[-2px] dark:text-zinc-400'>
                {description ||
                  `Explore the power of ${name} and discover what makes it an essential tool for modern development.`}
              </p>
            </div>
          </div>
          <div className='mt-6 border-t border-zinc-200 pt-6 dark:border-zinc-800'>
            <div
              className={cn(
                "group/start relative -m-3 flex items-center justify-between rounded-xl bg-gradient-to-r from-zinc-100 via-zinc-100 to-zinc-100 p-3 transition-all duration-300 hover:scale-[1.02] hover:cursor-pointer hover:from-primary/10 hover:from-0% hover:via-primary/5 hover:via-100% hover:to-transparent hover:to-100% dark:from-zinc-800 dark:via-zinc-800 dark:to-zinc-800 dark:hover:from-primary/40 dark:hover:from-0% dark:hover:via-primary/30 dark:hover:via-100% dark:hover:to-transparent dark:hover:to-100%",
              )}
            >
              <span className='text-sm font-medium text-zinc-900 transition-colors duration-300 group-hover/start:text-primary/90 dark:text-white dark:group-hover/start:text-primary'>
                Learn more
              </span>
              <div className='group/icon relative'>
                <div
                  className={cn(
                    "absolute inset-[-6px] scale-90 rounded-lg bg-gradient-to-br from-primary/20 via-primary/10 to-transparent opacity-0 transition-all duration-300 group-hover/start:scale-100 group-hover/start:opacity-100",
                  )}
                />
                <ArrowRight className='relative z-10 h-4 w-4 text-primary transition-all duration-300 group-hover/start:translate-x-0.5 group-hover/start:scale-110' />
              </div>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
