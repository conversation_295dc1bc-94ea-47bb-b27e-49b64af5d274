import { LucideIcon } from "lucide-react"

import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"

interface AboutStatsCardProps {
  icon: LucideIcon
  value: string
  label: string
  badge?: string
  className?: string
  iconBg?: string
}

export const AboutStatsCard = ({
  icon: Icon,
  value,
  label,
  badge,
  className,
  iconBg = "bg-blue-600",
}: AboutStatsCardProps) => {
  return (
    <Card
      className={`group relative overflow-hidden border-none transition-all duration-500 hover:scale-105 hover:shadow-xl ${className}`}
    >
      <div className='absolute inset-0 bg-gradient-to-br opacity-0 transition-opacity duration-500 group-hover:opacity-100' />
      <CardContent className='relative p-6 text-center'>
        <div
          className={`mb-4 inline-flex h-12 w-12 items-center justify-center rounded-xl ${iconBg} text-white transition-transform duration-300 group-hover:rotate-12`}
        >
          <Icon className='h-6 w-6' />
        </div>
        <h3 className='mb-2 text-2xl font-bold'>{value}</h3>
        <p className='text-sm font-medium text-muted-foreground'>{label}</p>
        {badge && (
          <Badge variant='secondary' className='mt-2 text-xs'>
            {badge}
          </Badge>
        )}
      </CardContent>
    </Card>
  )
}
