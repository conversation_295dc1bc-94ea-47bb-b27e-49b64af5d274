import { LucideIcon } from "lucide-react"

import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"

interface AboutFeatureCardProps {
  icon: LucideIcon
  title: string
  description: string
  badges: string[]
  gradientFrom: string
  gradientTo: string
}

export const AboutFeatureCard = ({
  icon: Icon,
  title,
  description,
  badges,
  gradientFrom,
  gradientTo,
}: AboutFeatureCardProps) => {
  return (
    <Card className='group transition-all duration-300 hover:shadow-lg'>
      <CardContent className='flex items-start gap-6 p-6'>
        <div
          className={`flex h-12 w-12 shrink-0 items-center justify-center rounded-xl bg-gradient-to-br ${gradientFrom} ${gradientTo} text-white shadow-lg transition-transform duration-300`}
        >
          <Icon className='h-6 w-6' />
        </div>
        <div>
          <h4 className='mb-3 text-lg font-semibold'>{title}</h4>
          <p className='text-muted-foreground'>{description}</p>
          <div className='mt-3 flex flex-wrap gap-2'>
            {badges.map((badge, index) => (
              <Badge key={index} variant='outline' className='text-xs'>
                {badge}
              </Badge>
            ))}
          </div>
        </div>
      </CardContent>
    </Card>
  )
}
