import { CalendarIcon, CircleIcon } from "lucide-react"

import { Container } from "@/components/layout"
import { Card, CardContent, CardHeader } from "@/components/ui/card"
import { GlowingEffect } from "@/components/ui/glowing-effect"
import { Activity } from "@/constants/dashboard"

interface ActivityCardProps {
  activities: Activity[]
  delay?: number
}

const typeColors = {
  success: "text-green-500",
  warning: "text-yellow-500",
  error: "text-red-500",
  info: "text-blue-500",
}

export function ActivityCard({ activities, delay = 0 }: ActivityCardProps) {
  return (
    <Container delay={delay}>
      <div className='relative h-full rounded-xl border p-1'>
        <GlowingEffect
          spread={30}
          glow={true}
          disabled={false}
          proximity={50}
        />
        <Card className='h-full bg-transparent'>
          <CardHeader>
            <div className='flex items-center justify-between'>
              <h3 className='text-lg font-semibold'>Recent Activities</h3>
              <span className='text-sm text-muted-foreground'>
                Last 24 hours
              </span>
            </div>
          </CardHeader>
          <CardContent>
            <div className='relative'>
              {activities.map((activity, index) => (
                <div
                  key={activity.id}
                  className='group relative flex items-start gap-4 pb-8 last:pb-0'
                >
                  {index !== activities.length - 1 && (
                    <div className='absolute left-[17px] top-[24px] h-full w-[2px] bg-muted-foreground/20 group-last:hidden' />
                  )}
                  <div
                    className={`rounded-full p-1 ${typeColors[activity.type]} bg-background`}
                  >
                    <CircleIcon className='h-4 w-4' />
                  </div>
                  <div className='flex flex-col space-y-1'>
                    <p className='text-sm font-medium leading-none'>
                      {activity.text}
                    </p>
                    <div className='flex items-center gap-2'>
                      <CalendarIcon className='h-3 w-3 text-muted-foreground' />
                      <span className='text-xs text-muted-foreground'>
                        {activity.time}
                      </span>
                    </div>
                  </div>
                </div>
              ))}
            </div>
          </CardContent>
        </Card>
      </div>
    </Container>
  )
}
