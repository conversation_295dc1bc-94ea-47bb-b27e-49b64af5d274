import { Eye, ShoppingCart } from "lucide-react"
import Image from "next/image"
import Link from "next/link"

import { But<PERSON> } from "@/components/ui/button"
import { Card, CardContent, CardFooter } from "@/components/ui/card"

interface BlogCardProps {
  data: any
}

export function BlogCard({ data }: BlogCardProps) {
  return (
    <Card className='relative mx-auto max-w-7xl rounded-xl border border-neutral-200/50 border-neutral-700 bg-neutral-800/50 p-2 backdrop-blur-lg md:p-3 lg:rounded-[32px]'>
      {/* <div className="absolute top-1/4 left-1/2 -z-10 gradient w-3/4 -translate-x-1/2 h-1/4 -translate-y-1/2 inset-0 blur-[10rem]"></div> */}

      {/* <Link href={`/${data?.url.toLowerCase().replace(/\s+/g, "-")}`}> */}
      <Link href={`/`}>
        {/* <div className="relative mx-auto max-w-7xl rounded-xl lg:rounded-[32px] border border-neutral-200/50 p-2 backdrop-blur-lg border-neutral-700 bg-neutral-800/50 md:p-4 mt-12"> */}
        <div className='rounded-lg border border-neutral-700 bg-black p-2 lg:rounded-[24px]'>
          <Image
            src='/images/dashboard.png'
            alt='dashboard'
            width={600}
            height={600}
            className='rounded-lg lg:rounded-[20px]'
          />
          {/* </div> */}
        </div>
        <CardContent className='my-2 p-2'>
          <h3 className='mb-2 font-heading text-lg font-semibold'>
            {/* {data.title} */}
            Blog Title
          </h3>
          <div className='flex items-center justify-start gap-2'>
            <span className='text-2xl font-bold text-emerald-600 dark:text-emerald-400'>
              {/* ${discountedPrice} */}
            </span>
            <div className='flex items-center gap-2'>
              <span className='text-sm text-gray-500 line-through'>
                {/* ${data.price} */}
              </span>
              <span className='rounded-full bg-red-500 px-2 py-1 text-xs text-white'>
                {/* {Math.round((1 - mainDiscount) * 100)}% off */}
              </span>
            </div>
          </div>
          {/* <p className="text-sm text-purple-600 dark:text-purple-400 font-semibold mt-2">
            (Exclusive Offer)
          </p> */}
        </CardContent>
      </Link>
      <CardFooter className='flex justify-between gap-2 p-0 md:gap-4'>
        <Button className='h-full flex-1 rounded-3xl' variant='outline' asChild>
          <Link href={data?.preview_url ?? "/"} target='_blank'>
            <Eye className='mr-2 h-4 w-4' />
            Preview
          </Link>
        </Button>
        <Button className='flex-1 rounded-3xl' disabled={!data?.isReady}>
          <ShoppingCart className='mr-2 h-4 w-4' />
          {data?.isReady ? "Get it now" : "Coming soon"}
        </Button>
      </CardFooter>
    </Card>
  )
}
