"use client"

import { LucideIcon } from "lucide-react"
import React from "react"

import {
  Card,
  CardContent,
  CardDescription,
  CardHeader,
  CardTitle,
} from "@/components/ui/card"
import { cn } from "@/lib/utils"

interface FeatureCardProps {
  title: string
  description: string
  icon: LucideIcon | React.ReactNode
  className?: string
  variant?: "default" | "outline" | "ghost"
}

export const FeatureCard = ({
  title,
  description,
  icon,
  className,
  variant = "default",
}: FeatureCardProps) => {
  const getCardStyles = () => {
    switch (variant) {
      case "outline":
        return "border-2 border-dashed border-muted-foreground/25 bg-transparent hover:border-primary/30 hover:bg-card/50"
      case "ghost":
        return "border-none bg-transparent shadow-none hover:bg-card/50"
      default:
        return "bg-card/50 hover:border-primary/30 hover:shadow-lg hover:shadow-primary/5"
    }
  }

  const renderIcon = () => {
    if (typeof icon === "function") {
      const IconComponent = icon as LucideIcon
      return <IconComponent className='h-6 w-6' />
    }
    return icon
  }

  return (
    <Card
      className={cn(
        "group transition-all duration-300 hover:scale-[1.02]",
        getCardStyles(),
        className,
      )}
    >
      <CardHeader className='pb-4'>
        {/* Icon container */}
        <div className='mb-3 inline-flex w-fit rounded-xl bg-primary/10 p-3 text-primary transition-all duration-300 group-hover:scale-110 group-hover:bg-primary/20'>
          {renderIcon()}
        </div>

        <CardTitle className='text-xl font-semibold tracking-tight transition-colors duration-300 group-hover:text-primary'>
          {title}
        </CardTitle>
      </CardHeader>

      <CardContent className='pt-0'>
        <CardDescription className='text-base leading-relaxed'>
          {description}
        </CardDescription>
      </CardContent>
    </Card>
  )
}
