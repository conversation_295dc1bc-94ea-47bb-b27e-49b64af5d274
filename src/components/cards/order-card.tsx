"use client"

import { <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Trash } from "lucide-react"
import Link from "next/link"
import { useState } from "react"

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { GlowingEffect } from "@/components/ui/glowing-effect"
import { Order } from "@/types/order"

interface OrderCardProps {
  order: Order
  onDelete?: (id: string) => void
}

export function OrderCard({ order, onDelete }: OrderCardProps) {
  const [isHovered, setIsHovered] = useState(false)

  // Format price with currency
  const formattedAmount = new Intl.NumberFormat("en-US", {
    style: "currency",
    currency: "USD",
  }).format(order.totalAmount || 0)

  // Format date
  const formattedDate = order.created_at
    ? new Date(order.created_at).toLocaleDateString("en-US", {
        year: "numeric",
        month: "short",
        day: "numeric",
      })
    : "No date"

  // Get status color
  const getStatusColor = (status: string) => {
    switch (status) {
      case "completed":
        return "bg-green-500/10 text-green-500"
      case "pending":
        return "bg-yellow-500/10 text-yellow-500"
      case "failed":
        return "bg-red-500/10 text-red-500"
      default:
        return "bg-secondary text-secondary-foreground"
    }
  }

  return (
    <div
      className='relative rounded-xl border p-1 transition-all duration-300 hover:shadow-md'
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <GlowingEffect
        spread={30}
        glow={isHovered}
        disabled={false}
        proximity={50}
      />
      <div className='relative flex h-full flex-col overflow-hidden rounded-lg bg-card'>
        {/* Order Header */}
        <div className='flex items-center justify-between border-b p-4'>
          <div>
            <h3 className='font-medium'>Order #{order._id?.substring(0, 8)}</h3>
            <p className='text-xs text-muted-foreground'>{formattedDate}</p>
          </div>

          {/* Status Badge */}
          <span
            className={`rounded-full px-2 py-1 text-xs font-medium ${getStatusColor(order.status)}`}
          >
            {order.status.charAt(0).toUpperCase() + order.status.slice(1)}
          </span>

          {/* Actions Menu */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <button className='flex h-8 w-8 items-center justify-center rounded-full bg-black/10 text-foreground transition-colors hover:bg-black/20'>
                <MoreHorizontal className='h-4 w-4' />
              </button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align='end'>
              <DropdownMenuLabel>Actions</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem asChild>
                <Link
                  href={`/dashboard/orders/${order._id}`}
                  className='flex cursor-pointer items-center'
                >
                  <Edit className='mr-2 h-4 w-4' />
                  Edit
                </Link>
              </DropdownMenuItem>
              <DropdownMenuItem asChild>
                <Link
                  href={`/dashboard/orders/${order._id}/view`}
                  className='flex cursor-pointer items-center'
                >
                  <Eye className='mr-2 h-4 w-4' />
                  View
                </Link>
              </DropdownMenuItem>
              <DropdownMenuItem
                className='flex cursor-pointer items-center text-destructive focus:text-destructive'
                onClick={() => onDelete && onDelete(order._id!)}
              >
                <Trash className='mr-2 h-4 w-4' />
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        {/* Order Info */}
        <div className='flex flex-1 flex-col p-4'>
          <div className='mb-4'>
            <div className='mb-2 flex items-center justify-between'>
              <span className='text-sm font-medium'>Customer:</span>
              <span className='text-sm'>{order.user.substring(0, 12)}...</span>
            </div>
            <div className='mb-2 flex items-center justify-between'>
              <span className='text-sm font-medium'>Email:</span>
              <span className='text-sm'>{order.deliveryEmail}</span>
            </div>
            <div className='flex items-center justify-between'>
              <span className='text-sm font-medium'>Amount:</span>
              <span className='text-sm font-bold text-primary'>
                {formattedAmount}
              </span>
            </div>
          </div>

          {/* Products Count */}
          <div className='mt-auto'>
            <div className='flex items-center justify-between'>
              <span className='text-sm font-medium'>Products:</span>
              <span className='rounded-full bg-secondary px-2 py-0.5 text-xs'>
                {order.products.length} items
              </span>
            </div>
          </div>
        </div>
      </div>
    </div>
  )
}
