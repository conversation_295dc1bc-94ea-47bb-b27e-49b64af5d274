"use client"

import { <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Trash } from "lucide-react"
import Image from "next/image"
import Link from "next/link"
import { useState } from "react"

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { GlowingEffect } from "@/components/ui/glowing-effect"
import { User } from "@/types/user"

interface UserCardProps {
  user: User
  onDelete?: (id: string) => void
}

export function UserCard({ user, onDelete }: UserCardProps) {
  const [isHovered, setIsHovered] = useState(false)

  // Get profile image or placeholder
  const imageUrl = user.profileImage || "https://placehold.co/400x400?text=User"

  // Format name
  const fullName =
    user.firstName && user.lastName
      ? `${user.firstName} ${user.lastName}`
      : user.email.split("@")[0]

  return (
    <div
      className='relative rounded-xl border p-1 transition-all duration-300 hover:shadow-md'
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <GlowingEffect
        spread={30}
        glow={isHovered}
        disabled={false}
        proximity={50}
      />
      <div className='relative flex h-full flex-col overflow-hidden rounded-lg bg-card'>
        {/* User Image */}
        <div className='flex items-center justify-center p-4'>
          <div className='relative h-24 w-24 overflow-hidden rounded-full border-2 border-primary/20'>
            <Image
              src={imageUrl}
              alt={fullName}
              fill
              className='object-cover'
            />
          </div>

          {/* Admin Badge */}
          {user.role !== "user" && (
            <div className='absolute right-2 top-2 rounded-full bg-primary px-2 py-1 text-xs font-medium text-primary-foreground'>
              {user.role}
            </div>
          )}

          {/* Actions Menu */}
          <div className='absolute right-2 top-2'>
            <DropdownMenu>
              <DropdownMenuTrigger asChild>
                <button className='flex h-8 w-8 items-center justify-center rounded-full bg-black/50 text-white backdrop-blur-sm transition-colors hover:bg-black/70'>
                  <MoreHorizontal className='h-4 w-4' />
                </button>
              </DropdownMenuTrigger>
              <DropdownMenuContent align='end'>
                <DropdownMenuLabel>Actions</DropdownMenuLabel>
                <DropdownMenuSeparator />
                <DropdownMenuItem asChild>
                  <Link
                    href={`/dashboard/users/${user._id}`}
                    className='flex cursor-pointer items-center'
                  >
                    <Edit className='mr-2 h-4 w-4' />
                    Edit
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuItem asChild>
                  <Link
                    href={`/dashboard/users/${user._id}/view`}
                    className='flex cursor-pointer items-center'
                  >
                    <Eye className='mr-2 h-4 w-4' />
                    View
                  </Link>
                </DropdownMenuItem>
                <DropdownMenuItem
                  className='flex cursor-pointer items-center text-destructive focus:text-destructive'
                  onClick={() => onDelete && onDelete(user._id!)}
                >
                  <Trash className='mr-2 h-4 w-4' />
                  Delete
                </DropdownMenuItem>
              </DropdownMenuContent>
            </DropdownMenu>
          </div>
        </div>

        {/* User Info */}
        <div className='flex flex-1 flex-col p-4 pt-0 text-center'>
          <h3 className='line-clamp-1 text-lg font-semibold'>{fullName}</h3>
          <p className='mt-1 line-clamp-1 text-sm text-muted-foreground'>
            {user.email}
          </p>

          <div className='mt-auto pt-4'>
            <div className='flex items-center justify-center gap-2'>
              {user.phoneNumber && (
                <span className='rounded-full bg-secondary px-2 py-0.5 text-xs'>
                  {user.phoneNumber}
                </span>
              )}
              <span className='rounded-full bg-primary/10 px-2 py-0.5 text-xs font-medium text-primary'>
                {user.provider}
              </span>
            </div>

            {user.address && (
              <p className='mt-2 line-clamp-1 text-xs text-muted-foreground'>
                {[user.address.city, user.address.country]
                  .filter(Boolean)
                  .join(", ")}
              </p>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
