"use client"
import { motion } from "framer-motion"
import { Eye, Heart, ShoppingCart } from "lucide-react"
import Image from "next/image"
import Link from "next/link"

import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { Product } from "@/types/product"
import { createArrayFromString } from "@/utils/generic"

interface ProductCardProps {
  data: Product
}

const techStack = [
  "Next.js",
  "Shadcn/ui",
  "TypeScript",
  "Node.js",
  "MongoDB",
  "Express.js",
  "TailwindCSS",
]

export function MainCard({ data }: ProductCardProps) {
  const discountPercentage = data.discount || 20 // 20% default discount
  const discountMultiplier = (100 - discountPercentage) / 100
  const discountedPrice = data.price
    ? (data.price * discountMultiplier).toFixed(2)
    : 0

  const formatPrice = (price: number) => {
    return new Intl.NumberFormat("en-US", {
      style: "currency",
      currency: "USD",
      minimumFractionDigits: 2,
      maximumFractionDigits: 2,
    }).format(price)
  }

  return (
    <Card className='group relative mx-auto max-w-7xl cursor-pointer overflow-hidden rounded-md p-0 lg:rounded-xl'>
      <Link href={`/${data.type}/${data.slug}`}>
        <div className='relative rounded-t-md bg-black lg:rounded-t-xl'>
          <Image
            src={createArrayFromString(data?.images)[0]}
            alt='dashboard'
            width={600}
            height={600}
            className='object-contain'
          />

          {/* Hover Overlay */}
          <motion.div className='absolute inset-0 flex items-center justify-center gap-4 bg-black/50 opacity-0 transition-opacity duration-300 ease-in-out group-hover:opacity-100'>
            {/* Preview Button */}
            <Button
              variant='outline'
              size='sm'
              className='flex h-12 w-12 items-center justify-center rounded-full transition-all duration-300 hover:border-white'
            >
              <Eye className='text-neutral-300 transition-all hover:text-white' />
            </Button>
            {/* Cart Button */}
            <Button
              variant='outline'
              size='sm'
              className='flex h-12 w-12 items-center justify-center rounded-full transition-all duration-300 hover:border-white'
            >
              <ShoppingCart className='text-neutral-300 transition-all hover:text-white' />
            </Button>
          </motion.div>

          {/* Like Button */}
          <motion.div className='absolute right-2 top-2 opacity-0 transition-opacity duration-300 ease-in-out group-hover:opacity-100'>
            <Button
              variant='ghost'
              size='sm'
              className='flex h-10 w-10 items-center justify-center rounded-full transition-all duration-300 hover:border-white'
            >
              <Heart
                size={24}
                className='text-neutral-300 transition-all hover:text-white'
              />
            </Button>
          </motion.div>

          {/* Discount Badge */}
          {discountPercentage > 0 && (
            <div className='absolute left-2 top-2 rounded-full bg-red-500/90 px-2 py-1 text-xs font-semibold text-white'>
              -{discountPercentage}% OFF
            </div>
          )}
        </div>
        <CardContent className='px-4 py-2.5'>
          <div className='flex items-center justify-between gap-2'>
            <h3 className='line-clamp-1 font-heading text-sm font-medium'>
              {data.fullName}
            </h3>
            <div className='flex items-center justify-start gap-2'>
              <span className='text-base font-bold text-emerald-600 dark:text-emerald-400'>
                {formatPrice(Number(discountedPrice))}
              </span>
              {discountPercentage > 0 && (
                <div className='flex items-center gap-2'>
                  <span className='text-sm text-gray-500 line-through'>
                    {formatPrice(data.price || 0)}
                  </span>
                </div>
              )}
            </div>
          </div>

          {data.techStack && (
            <div className='mt-2 flex flex-wrap gap-1'>
              {data.techStack.slice(0, 3).map((tech, index) => (
                <span
                  key={index}
                  className='rounded-full bg-gray-700/50 px-2 py-0.5 text-xs text-gray-300'
                >
                  {tech}
                </span>
              ))}
              {data.techStack.length > 3 && (
                <span className='rounded-full bg-gray-700/50 px-2 py-0.5 text-xs text-gray-300'>
                  +{data.techStack.length - 3}
                </span>
              )}
            </div>
          )}
        </CardContent>
      </Link>
    </Card>
  )
}
