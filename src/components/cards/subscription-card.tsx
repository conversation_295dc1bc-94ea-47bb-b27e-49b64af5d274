"use client"

import { <PERSON>, <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Trash } from "lucide-react"
import Link from "next/link"
import { useState } from "react"

import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuSeparator,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { GlowingEffect } from "@/components/ui/glowing-effect"
import { Subscription } from "@/types/subscription"

interface SubscriptionCardProps {
  subscription: Subscription
  onDelete?: (id: string) => void
}

export function SubscriptionCard({
  subscription,
  onDelete,
}: SubscriptionCardProps) {
  const [isHovered, setIsHovered] = useState(false)

  // Format dates
  const formatDate = (date: Date | string | undefined) => {
    if (!date) return "N/A"
    return new Date(date).toLocaleDateString("en-US", {
      year: "numeric",
      month: "short",
      day: "numeric",
    })
  }

  const startDate = formatDate(subscription.startDate)
  const endDate = formatDate(subscription.endDate)

  // Get status color
  const getStatusColor = (status: string) => {
    switch (status) {
      case "paid":
        return "bg-green-500/10 text-green-500"
      case "pending":
        return "bg-yellow-500/10 text-yellow-500"
      case "failed":
        return "bg-red-500/10 text-red-500"
      default:
        return "bg-secondary text-secondary-foreground"
    }
  }

  return (
    <div
      className='relative rounded-xl border p-1 transition-all duration-300 hover:shadow-md'
      onMouseEnter={() => setIsHovered(true)}
      onMouseLeave={() => setIsHovered(false)}
    >
      <GlowingEffect
        spread={30}
        glow={isHovered}
        disabled={false}
        proximity={50}
      />
      <div className='relative flex h-full flex-col overflow-hidden rounded-lg bg-card'>
        {/* Subscription Header */}
        <div className='flex items-center justify-between border-b p-4'>
          <div>
            <h3 className='font-medium'>
              Subscription #{subscription._id?.substring(0, 8)}
            </h3>
            <p className='text-xs text-muted-foreground'>
              Plan: {subscription.planId}
            </p>
          </div>

          {/* Status Badge */}
          <div className='flex items-center gap-2'>
            <span
              className={`rounded-full px-2 py-1 text-xs font-medium ${getStatusColor(subscription.paymentStatus)}`}
            >
              {subscription.paymentStatus.charAt(0).toUpperCase() +
                subscription.paymentStatus.slice(1)}
            </span>

            {subscription.isCurrent && (
              <span className='rounded-full bg-blue-500/10 px-2 py-1 text-xs font-medium text-blue-500'>
                Current
              </span>
            )}
          </div>

          {/* Actions Menu */}
          <DropdownMenu>
            <DropdownMenuTrigger asChild>
              <button className='flex h-8 w-8 items-center justify-center rounded-full bg-black/10 text-foreground transition-colors hover:bg-black/20'>
                <MoreHorizontal className='h-4 w-4' />
              </button>
            </DropdownMenuTrigger>
            <DropdownMenuContent align='end'>
              <DropdownMenuLabel>Actions</DropdownMenuLabel>
              <DropdownMenuSeparator />
              <DropdownMenuItem asChild>
                <Link
                  href={`/dashboard/subscriptions/${subscription._id}`}
                  className='flex cursor-pointer items-center'
                >
                  <Edit className='mr-2 h-4 w-4' />
                  Edit
                </Link>
              </DropdownMenuItem>
              <DropdownMenuItem asChild>
                <Link
                  href={`/dashboard/subscriptions/${subscription._id}/view`}
                  className='flex cursor-pointer items-center'
                >
                  <Eye className='mr-2 h-4 w-4' />
                  View
                </Link>
              </DropdownMenuItem>
              <DropdownMenuItem
                className='flex cursor-pointer items-center text-destructive focus:text-destructive'
                onClick={() => onDelete && onDelete(subscription._id!)}
              >
                <Trash className='mr-2 h-4 w-4' />
                Delete
              </DropdownMenuItem>
            </DropdownMenuContent>
          </DropdownMenu>
        </div>

        {/* Subscription Info */}
        <div className='flex flex-1 flex-col p-4'>
          <div className='mb-4 space-y-2'>
            <div className='flex items-center justify-between'>
              <span className='text-sm font-medium'>User:</span>
              <span className='text-sm'>
                {subscription.userId.substring(0, 12)}...
              </span>
            </div>
            <div className='flex items-center justify-between'>
              <span className='text-sm font-medium'>Start Date:</span>
              <span className='text-sm'>{startDate}</span>
            </div>
            <div className='flex items-center justify-between'>
              <span className='text-sm font-medium'>End Date:</span>
              <span className='text-sm'>{endDate}</span>
            </div>
          </div>

          {/* Status Indicators */}
          <div className='mt-auto flex flex-wrap gap-2'>
            <span
              className={`rounded-full px-2 py-0.5 text-xs font-medium ${
                subscription.isActive
                  ? "bg-green-500/10 text-green-500"
                  : "bg-red-500/10 text-red-500"
              }`}
            >
              {subscription.isActive ? "Active" : "Inactive"}
            </span>

            <span
              className={`rounded-full px-2 py-0.5 text-xs font-medium ${
                subscription.autoRenew
                  ? "bg-blue-500/10 text-blue-500"
                  : "bg-yellow-500/10 text-yellow-500"
              }`}
            >
              {subscription.autoRenew ? "Auto Renew" : "Manual Renew"}
            </span>

            {subscription.paymentMethod && (
              <span className='rounded-full bg-secondary px-2 py-0.5 text-xs'>
                {subscription.paymentMethod}
              </span>
            )}
          </div>
        </div>
      </div>
    </div>
  )
}
