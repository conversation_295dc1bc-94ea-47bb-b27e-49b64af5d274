"use client"

import { useMemo } from "react"

import { OrderFormConfig, OrderFormProps, orderSchema } from "@/types/order" // Assuming the Order interface is defined in this path

import { Form } from "./common/custom-form"

export function OrderForm({ initialData, onSubmit }: OrderFormProps) {
  const formConfig: OrderFormConfig = useMemo(
    () => ({
      fields: [
        {
          name: "user",
          label: "User ID",
          type: "text",
          placeholder: "User ID",
        },

        {
          name: "payment",
          label: "Payment ID",
          type: "text",
          placeholder: "Payment ID",
        },

        {
          name: "products",
          label: "Products",
          type: "array",
          placeholder: "Products",
        },

        {
          name: "status",
          label: "Status",
          type: "select",
          options: [
            { label: "Completed", value: "completed" },
            { label: "Failed", value: "failed" },
            { label: "Pending", value: "pending" },
          ],
        },
        {
          name: "deliveryEmail",
          label: "Delivery Email",
          type: "email",
          placeholder: "Delivery email",
        },

        //handle this formfield in the future

        // {
        //   name: "shippingAddress",
        //   label: "Shipping Address",
        //   type: "object",
        //   fields: [
        //     {
        //       name: "street",
        //       label: "Street",
        //       type: "text",
        //       placeholder: "Street",
        //     },
        //     {
        //       name: "city",
        //       label: "City",
        //       type: "text",
        //       placeholder: "City",
        //     },
        //     {
        //       name: "state",
        //       label: "State",
        //       type: "text",
        //       placeholder: "State",
        //     },
        //     {
        //       name: "postalCode",
        //       label: "Postal Code",
        //       type: "text",
        //       placeholder: "Postal code",
        //     },
        //     {
        //       name: "country",
        //       label: "Country",
        //       type: "text",
        //       placeholder: "Country",
        //     },
        //   ],
        // },

        {
          name: "totalAmount",
          label: "Total Amount",
          type: "number",
          placeholder: "Total amount",
        },
      ],
      schema: orderSchema,
      onSubmit: onSubmit,
    }),
    [onSubmit],
  )

  return <Form {...formConfig} defaultValues={initialData} />
}
