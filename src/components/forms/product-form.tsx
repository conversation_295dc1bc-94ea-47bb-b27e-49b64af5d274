"use client"

import { useMemo } from "react"

import { PRODUCT_TYPES, SUB_TYPES, TECH_STACK } from "@/types/constants"
import {
  ProductFormConfig,
  ProductFormProps,
  productSchema,
} from "@/types/product" // Assuming the Product interface is defined in this path

import { Form } from "./common/custom-form"

export function ProductForm({
  initialData,
  onSubmit,
  categories,
}: ProductFormProps) {
  const formConfig: ProductFormConfig = useMemo(
    () => ({
      fields: [
        {
          name: "name",
          label: "Product Name",
          isRequired: true,
          type: "text",
          placeholder: "Product Name(SEO Spark)",
        },
        {
          name: "fullName",
          label: "Product Full Name Name",
          isRequired: true,
          type: "text",
          placeholder: "Product Name",
        },
        {
          name: "creator",
          label: "Creator",

          type: "text",
          placeholder: "Creator Name",
        },
        {
          name: "slug",
          label: "Slug(Leave empty to auto generate)",
          type: "text",
          placeholder: "Slug",
        },
        {
          name: "images",
          label: "Images",
          type: "gallery",
          placeholder: "Enter image URL separated by comma",
        },
        {
          name: "images",
          label: "Images Preview",
          isRequired: true,
          type: "image-preview",
          placeholder: "Enter image URL separated by comma",
        },
        {
          name: "description",
          label: "Description",
          type: "textarea",
          placeholder: "Description",
        },
        {
          name: "type",
          label: "Select Type",
          type: "select",
          isRequired: true,
          options: PRODUCT_TYPES.map((type) => ({
            label: type,
            value: type,
          })),
        },
        // {
        //   name: "categories",
        //   label: "Categories",
        //   type: "select-multiple",
        //   placeholder: "Select Categories",
        //   options: categories?.map((category) => ({
        //     label: category?.name,
        //     value: category?._id ?? "",
        //   })),
        // },

        {
          name: "category",
          label: "Category",
          type: "select-single",
          placeholder: "Select Category",
          options: categories?.map((category) => ({
            label: category?.name,
            value: category?._id ?? "",
          })),
        },

        {
          name: "subTypes",
          label: "Sub Types",
          type: "select-multiple",
          // placeholder: "Tech Stack",
          placeholder: "Select Sub Types",
          options: Object.keys(SUB_TYPES).map((key: any) => ({
            value: key,
            // label: TECH_STACK[key].label, //
            label: SUB_TYPES[key as keyof typeof SUB_TYPES].label, // Safely accessing label using keyof
          })),
        },
        {
          name: "techStack",
          label: "Tech Stack",
          type: "select-multiple",
          // placeholder: "Tech Stack",
          placeholder: "Select Tech Stack",
          options: Object.keys(TECH_STACK).map((key: any) => ({
            value: key,
            // label: TECH_STACK[key].label, //
            label: TECH_STACK[key as keyof typeof TECH_STACK].label, // Safely accessing label using keyof
          })),
        },

        {
          name: "status",
          label: "Status",
          type: "select",
          isRequired: true,

          options: [
            { label: "Active", value: "active" },
            { label: "Inactive", value: "inactive" },
            { label: "Archived", value: "archived" },
          ],
        },

        {
          name: "tags",
          label: "Tags",
          type: "array",
          placeholder: "Tags",
        },

        {
          name: "price",
          label: "Price",
          type: "number",
          placeholder: "Price",
        },
        {
          name: "isPaid",
          label: "Is Paid",
          type: "checkbox",
        },
        {
          name: "downloads",
          label: "Downloads",
          type: "number",
          placeholder: "Downloads",
        },
        {
          name: "previewUrl",
          label: "Preview URL",
          type: "text",
          placeholder: "Preview URL",
        },
        {
          name: "githubUrl",
          label: "Gihtub Url",
          type: "text",
          placeholder: "Gihtub Url",
        },
        {
          name: "paymentLink",
          label: "Payment Link",
          type: "text",
          placeholder: "Payment Link",
        },

        // {
        //   name: "paymentLink.platform",
        //   label: "Payment Platform",
        //   type: "text",
        //   placeholder: "Payment Platform",
        // },
        // {
        //   name: "paymentLink.link",
        //   label: "Payment Link",
        //   type: "text",
        //   placeholder: "Payment Link",
        // },
        // {
        //   name: "authorId",
        //   label: "Author ID",
        //   type: "text",
        //   isRequired: true,

        //   placeholder: "Author ID",
        // },
        {
          name: "rating",
          label: "Rating",
          type: "number",
          placeholder: "Rating",
        },
        {
          name: "likes",
          label: "Likes",
          type: "number",
          placeholder: "LIkes",
        },
        {
          name: "keyFeatures",
          label: "Key Features",
          type: "textarea",
          placeholder: "Key Features",
        },
        {
          name: "highlights",
          label: "Highlights",
          type: "textarea",
          placeholder: "Highlights",
        },

        {
          name: "isFeatured",
          label: "Is Featured",
          type: "checkbox",
        },
        {
          name: "discount",
          label: "Discount",
          type: "number",
          placeholder: "Discount",
        },
      ],
      schema: productSchema,

      onSubmit: onSubmit,
    }),
    [onSubmit, categories], // Ensure categories is a dependency
  )

  return (
    <Form
      {...formConfig}
      defaultValues={{
        likes: 234,
        downloads: 56,
        rating: 4.5,
        isFeatured: false,
        isPaid: true,
        discount: 0,
        status: "active",

        ...initialData,
      }}
    />
  )
}
