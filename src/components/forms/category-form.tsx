"use client"

import { useMemo } from "react"

import {
  CategoryFormConfig,
  CategoryFormProps,
  categorySchema,
} from "@/types/category"

import { Form } from "./common/custom-form"

export function CategoryForm({
  initialData,
  onSubmit,
  categories,
}: CategoryFormProps) {
  const formConfig: CategoryFormConfig = useMemo(
    () => ({
      fields: [
        {
          name: "name",
          label: "Name",
          type: "text",
          placeholder: "Category name",
        },
        {
          name: "slug",
          label: "Slug",
          type: "text",
          placeholder: "category-slug",
        },
        {
          name: "type",
          label: "Type",
          type: "select",
          // placeholder: "category-slug",
          options: [
            { label: "Product", value: "product" },
            { label: "Blog", value: "blog" },
            { label: "Service", value: "service" },
          ],
        },
        {
          name: "description",
          label: "Description",
          type: "textarea",
          placeholder: "Category description",
        },
        {
          name: "parent",
          label: "Parent Category",
          type: "select",
          placeholder: "Select a parent category",
          options: [
            { label: "None", value: "N/A" },

            ...(Array.isArray(categories)
              ? categories.map((cat) => ({
                  label: cat.name,
                  value: cat._id || "",
                }))
              : []),
          ],
        },
      ],
      schema: categorySchema,
      onSubmit: onSubmit,
    }),
    [categories, onSubmit],
  )

  return <Form {...formConfig} defaultValues={initialData} />
}
