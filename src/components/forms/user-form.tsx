"use client"

import { useMemo } from "react"

import { UserFormConfig, UserFormProps, userSchema } from "@/types/user" // Assuming the User interface is defined in this path

import { Form } from "./common/custom-form"

export function UserForm({ initialData, onSubmit }: UserFormProps) {
  const formConfig: UserFormConfig = useMemo(
    () => ({
      fields: [
        {
          name: "firstName",
          label: "First Name",
          type: "text",
          placeholder: "First Name",
        },
        {
          name: "lastName",
          label: "Last Name",
          type: "text",
          placeholder: "Last Name",
        },
        {
          name: "email",
          label: "Email",
          type: "email",
          placeholder: "Email",
        },
        {
          name: "password",
          label: "Password",
          type: "password",
          placeholder: "Password",
        },
        {
          name: "profileImage",
          label: "Profile Image",
          type: "text",
          placeholder: "Profile Image URL",
        },
        {
          name: "phoneNumber",
          label: "Phone Number",
          type: "text",
          placeholder: "Phone Number",
        },
        {
          name: "address.street",
          label: "Street",
          type: "text",
          placeholder: "Street",
        },
        {
          name: "address.city",
          label: "City",
          type: "text",
          placeholder: "City",
        },
        {
          name: "address.state",
          label: "State",
          type: "text",
          placeholder: "State",
        },
        {
          name: "address.postalCode",
          label: "Postal Code",
          type: "text",
          placeholder: "Postal Code",
        },
        {
          name: "address.country",
          label: "Country",
          type: "text",
          placeholder: "Country",
        },
        {
          name: "role",
          label: "Role",
          type: "select",
          options: [
            { label: "Admin", value: "admin" },
            { label: "User", value: "user" },
          ],
        },
        {
          name: "isDeleted",
          label: "Is Deleted",
          type: "checkbox",
        },
        {
          name: "lastLogin",
          label: "Last Login",
          type: "text",
          placeholder: "Last Login Timestamp",
        },
        {
          name: "createdBy",
          label: "Created By",
          type: "select",
          options: [
            { label: "Self", value: "self" },
            { label: "Admin", value: "admin" },
          ],
        },
      ],
      schema: userSchema,
      onSubmit: onSubmit,
    }),
    [onSubmit],
  )

  return <Form {...formConfig} defaultValues={initialData} />
}
