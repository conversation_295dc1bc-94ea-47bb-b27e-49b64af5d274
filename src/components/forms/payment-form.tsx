"use client"

import { useMemo } from "react"

import {
  PaymentFormConfig,
  PaymentFormProps,
  paymentSchema,
} from "@/types/payment" // Assuming the Payment interface is defined in this path

import { Form } from "./common/custom-form"

export function PaymentForm({ initialData, onSubmit }: PaymentFormProps) {
  const formConfig: PaymentFormConfig = useMemo(
    () => ({
      fields: [
        {
          name: "user",
          label: "User ID",
          type: "text",
          placeholder: "User ID",
        },
        {
          name: "amount",
          label: "Amount",
          type: "number",
          placeholder: "Amount",
        },
        {
          name: "currency",
          label: "Currency",
          type: "text",
          placeholder: "Currency",
        },
        {
          name: "method",
          label: "Payment Method",
          type: "select",
          options: [
            { label: "Credit Card", value: "credit_card" },
            { label: "PayPal", value: "paypal" },
            { label: "Bank Transfer", value: "bank_transfer" },
          ],
        },
        {
          name: "status",
          label: "Status",
          type: "select",
          options: [
            { label: "Successful", value: "successful" },
            { label: "Failed", value: "failed" },
            { label: "Pending", value: "pending" },
          ],
        },
        {
          name: "transactionId",
          label: "Transaction ID",
          type: "text",
          placeholder: "Transaction ID",
        },
        {
          name: "description",
          label: "Description",
          type: "text",
          placeholder: "Description",
        },
        {
          name: "isRefunded",
          label: "Is Refunded",
          type: "checkbox",
          placeholder: "Is refunded",
          // type: "select",
          // options: [
          //   { label: "Yes", value: true },
          //   { label: "No", value: false },
          // ],
        },
        {
          name: "transactionDate",
          label: "Transaction Date",
          type: "date",
          placeholder: "Transaction date",
        },
      ],
      schema: paymentSchema,
      onSubmit: onSubmit,
    }),
    [onSubmit],
  )

  return <Form {...formConfig} defaultValues={initialData} />
}
