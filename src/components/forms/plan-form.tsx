"use client"

import { useMemo } from "react"

import { PlanFormConfig, PlanFormProps, planSchema } from "@/types/plan" // Assuming the Plan interface is defined in this path

import { Form } from "./common/custom-form"

export function PlanForm({ initialData, onSubmit }: PlanFormProps) {
  const formConfig: PlanFormConfig = useMemo(
    () => ({
      fields: [
        {
          name: "name",
          label: "Name",
          type: "text",
          placeholder: "Name",
        },
        {
          name: "description",
          label: "Description",
          type: "text",
          placeholder: "Description",
        },
        {
          name: "price",
          label: "Price",
          type: "number",
          placeholder: "Price",
        },
        {
          name: "currency",
          label: "Currency",
          type: "text",
          placeholder: "Currency",
        },
        {
          name: "duration",
          label: "Duration",
          type: "select",
          options: [
            { label: "Monthly", value: "monthly" },
            { label: "Yearly", value: "yearly" },
            { label: "Lifetime", value: "lifetime" },
          ],
        },
        {
          name: "isActive",
          label: "isActive",
          type: "checkbox",
          placeholder: "isActive",
        },
        {
          name: "maxTemplates",
          label: "Max Templates",
          type: "number",
          placeholder: "Max Templates",
        },
        {
          name: "maxUsers",
          label: "Max Users",
          type: "number",
          placeholder: "Max Users",
        },
        {
          name: "benefits",
          label: "Benefits",
          type: "text",
          placeholder: "Benefits (comma separated)",
        },
      ],
      schema: planSchema,

      onSubmit: onSubmit,
    }),
    [onSubmit],
  )

  return <Form {...formConfig} defaultValues={initialData} />
}
