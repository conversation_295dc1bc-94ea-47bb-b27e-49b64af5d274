"use client"

import { zod<PERSON><PERSON><PERSON><PERSON> } from "@hookform/resolvers/zod"
import { useCallback } from "react"
import { useForm } from "react-hook-form"
import { z } from "zod"

import { FormFieldComponent } from "@/components/forms/common/form-field"
import { Button } from "@/components/ui/button"
import { Form as FormProvider } from "@/components/ui/form"
import { FormProps } from "@/types/form"

export function Form<T extends z.ZodType>({
  fields,
  schema,
  onSubmit,
  defaultValues,
}: FormProps<T>) {
  const form = useForm<z.infer<T>>({
    resolver: zodResolver(schema),
    defaultValues: defaultValues as z.infer<T>,
  })

  const handleSubmit = useCallback(
    (values: z.infer<T>) => {
      console.log("Form values:", values)
      onSubmit(values)
    },
    [onSubmit],
  )

  return (
    <FormProvider {...form}>
      <form
        onSubmit={form.handleSubmit(handleSubmit)}
        className='grid grid-flow-row-dense gap-3 md:grid-cols-2 md:gap-6 xl:grid-cols-3'
      >
        {fields.map((field) => (
          <FormFieldComponent key={field.name} field={field} form={form} />
        ))}
        <div className='sticky bottom-0 col-span-full w-full rounded-t-md bg-background p-3'>
          <Button
            disabled={
              !form.formState.isValid ||
              form.formState.isSubmitting ||
              form.formState.isValidating
            }
            className='w-full'
            type='submit'
          >
            {/* {form.formState.isSubmitting ? "Submitting..." : "Submit"} */}
            {form.formState.isValidating
              ? "Validating..."
              : form.formState.isSubmitting
                ? "Submitting..."
                : "Submit"}
          </Button>
        </div>
      </form>
    </FormProvider>
  )
}
