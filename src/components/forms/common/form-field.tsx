/* eslint-disable @next/next/no-img-element */
import { format } from "date-fns"
import { CalendarIcon } from "lucide-react"
import React from "react"
import { useController } from "react-hook-form"
import { toast } from "sonner"

import { CustomArrayInput, CustomCheckbox, Selector } from "@/components/custom"
import { GalleryModal } from "@/components/gallery"
import { Button } from "@/components/ui/button"
import { Calendar } from "@/components/ui/calendar"
import {
  FormControl,
  FormField,
  FormItem,
  FormLabel,
  FormMessage,
} from "@/components/ui/form"
import { Input } from "@/components/ui/input"
import {
  Popover,
  PopoverContent,
  PopoverTrigger,
} from "@/components/ui/popover"
import { RadioGroup, RadioGroupItem } from "@/components/ui/radio-group"
import {
  Select,
  SelectContent,
  SelectItem,
  SelectTrigger,
  SelectValue,
} from "@/components/ui/select"
import { Textarea } from "@/components/ui/textarea"
import { UploadButtonS3 } from "@/components/ui/upload-button-s3"
import { cn } from "@/functions"
import { FormFieldProps } from "@/types/form"
import { createArrayFromString } from "@/utils/generic"
import { UploadButton } from "@/utils/uploadthing"

export const FormFieldComponent: React.FC<FormFieldProps> = ({
  field,
  form,
}) => {
  const { name, label, type, placeholder, options, isRequired } = field
  const { field: controllerField, fieldState } = useController({
    name,
    control: form.control,
  })

  // console.log("options: ", options);
  const renderFieldComponent = () => {
    switch (type) {
      case "select":
        return (
          <Select
            value={controllerField.value || ""}
            onValueChange={(value) =>
              controllerField.onChange(value === "default" ? null : value)
            }
          >
            <SelectTrigger>
              <SelectValue placeholder={placeholder} />
            </SelectTrigger>
            <SelectContent>
              {options?.map((option) => (
                <SelectItem key={option.value} value={option.value}>
                  {option.label}
                </SelectItem>
              ))}
            </SelectContent>
          </Select>
        )
      case "textarea":
        return (
          <Textarea
            className='w-full'
            placeholder={placeholder}
            {...controllerField}
          />
        )
      case "gallery":
        return (
          <div key={name}>
            <GalleryModal
              onSelect={(selectedImages) => {
                console.log("Selected images:", selectedImages)
                // controllerField.onChange(selectedImages);
                const prevImages = form.getValues(name)
                // my value should be strig not array as my selectedImages is string seprated by comma
                if (selectedImages) {
                  // form.setValue(name, selectedImages)
                  form.setValue(
                    name,
                    prevImages
                      ? // should be string not array
                        prevImages + "," + selectedImages
                      : selectedImages,
                  )
                }
              }}
              allowMultiple={true}
            />
          </div>
        )
      case "image":
        return (
          <UploadButton
            key={name}
            endpoint='imageUploader'
            onClientUploadComplete={(res) => {
              // Do something with the response
              // Do something with the response
              console.log("Files: ", res)
              // alert('Upload Completed')
              toast.success("Image uploaded successfully")
              const data: any = res
              console.log(data)
              if (data) {
                console.log("Data: ", data)
                // const prevImages = form.getValues(name);

                form.setValue(name, [data?.[0]?.url])
                form.setValue("imageKey", data?.[0]?.key)
                form.setValue("fileName", data?.[0]?.name)
                form.setValue("fileSize", data?.[0]?.size)
                form.setValue("fileType", data?.[0]?.type)

                // form.setValue(name, data?.[0]?.url);
              }
            }}
            onUploadError={(error: Error) => {
              // Do something with the error.
              alert(`ERROR! ${error.message}`)
            }}
          />
        )
      case "array":
        return (
          <CustomArrayInput
            value={controllerField.value || []}
            onChange={(value) => {
              controllerField.onChange(value)
            }}
            placeholder={placeholder}
          />
        )

      case "select-multiple":
        return (
          <Selector
            options={options || []}
            selected={controllerField.value || []} // Default to empty array
            onChange={(value) => {
              form.setValue(name, value)
            }}
            placeholder={placeholder || "Select items..."}
          />
        )
      case "select-single":
        return (
          <Selector
            options={options || []}
            selected={controllerField.value ? [controllerField.value] : []} // Single value as an array
            onChange={(value) => {
              if (Array.isArray(value)) {
                form.setValue(name, value[0] || null) // Use the first selected value
              }
            }}
            placeholder={placeholder || "Select an item..."}
          />
        )
      case "checkbox":
        return (
          <CustomCheckbox
            label={name}
            description={placeholder}
            checked={Boolean(controllerField.value)}
            onCheckedChange={(checked) => controllerField.onChange(checked)}
          />
          // <Checkbox
          //   checked={Boolean(controllerField.value)}
          //   onCheckedChange={(checked) => controllerField.onChange(checked)}
          // />
        )
      case "radio":
        return (
          <RadioGroup
            value={controllerField.value || ""}
            onValueChange={(value) => controllerField.onChange(value)}
          >
            {options?.map((option) => (
              <FormItem
                key={option.value}
                className='flex items-center space-x-3 space-y-0'
              >
                <FormControl>
                  <RadioGroupItem value={option.value} />
                </FormControl>
                <FormLabel className='font-normal'>{option.label}</FormLabel>
              </FormItem>
            ))}
          </RadioGroup>
        )
      case "date":
        return (
          <Popover>
            <PopoverTrigger asChild>
              <Button
                variant={"outline"}
                className={`w-full justify-start text-left font-normal ${
                  !controllerField.value && "text-muted-foreground"
                }`}
              >
                <CalendarIcon className='mr-2 h-4 w-4' />
                {controllerField.value ? (
                  format(new Date(controllerField.value), "PPP")
                ) : (
                  <span>Pick a date</span>
                )}
              </Button>
            </PopoverTrigger>
            <PopoverContent className='w-auto p-0' align='start'>
              <Calendar
                mode='single'
                selected={
                  controllerField.value
                    ? new Date(controllerField.value)
                    : undefined
                }
                onSelect={(date) =>
                  // controllerField.onChange(date?.toISOString() || null)
                  controllerField.onChange(date)
                }
                initialFocus
              />
            </PopoverContent>
          </Popover>
        )
      case "s3-image":
        return (
          <UploadButtonS3
            key={name}
            onClientUploadComplete={(res) => {
              console.log("Upload complete:", res)
              toast.success("Image uploaded successfully")

              form.setValue(name, res.imageUrl)
              form.setValue("title", res.title)
              form.setValue("fileName", res.fileName)
              form.setValue("imageKey", res.imageKey)
              form.setValue("fileName", res.fileName)
              form.setValue("fileSize", res.fileSize)
              form.setValue("fileType", res.fileType)
              form.setValue("description", "Test description")
              form.setValue("sourceDetails", res.sourceDetails)
              form.setValue("source", res.source)
            }}
            onUploadError={(error: Error) => {
              toast.error(`Upload failed: ${error.message}`)
            }}
          />
        )
      case "image-preview":
        return (
          <div
            key={name + "preview"}
            className='grid grid-cols-2 gap-4 md:grid-cols-4 lg:grid-cols-6 2xl:grid-cols-8'
          >
            {createArrayFromString(controllerField.value)?.map(
              (image, index) => (
                <div
                  key={index}
                  className='relative aspect-square overflow-hidden rounded-lg border border-card bg-card'
                >
                  <img
                    src={image}
                    alt={`Preview ${index + 1}`}
                    className='h-full w-full object-cover'
                    loading='lazy'
                  />
                </div>
              ),
            )}
          </div>
        )
      default:
        return (
          <Input
            type={type}
            placeholder={placeholder}
            value={controllerField.value || ""}
            onChange={(e) => {
              const { value } = e.target
              if (type === "number") {
                controllerField.onChange(value ? parseFloat(value) : null)
              } else {
                controllerField.onChange(value)
              }
            }}
          />
        )
    }
  }

  return (
    <FormField
      control={form.control}
      name={name}
      render={() => (
        <FormItem
          className={cn(
            type === "textarea" ||
              type === "image" ||
              type === "array" ||
              type === "image-preview"
              ? "col-span-full"
              : "",
          )}
        >
          <FormLabel>
            {label}
            {isRequired && <span className='text-red-500'>*</span>}
          </FormLabel>
          <FormControl>{renderFieldComponent()}</FormControl>
          <FormMessage>{fieldState.error?.message}</FormMessage>
        </FormItem>
      )}
    />
  )
}

FormFieldComponent.displayName = "FormFieldComponent"
