"use client"

import { useMemo } from "react"

import { CouponFormConfig, CouponFormProps, couponSchema } from "@/types/coupon" // Assuming the Coupon interface is defined in this path

import { Form } from "./common/custom-form"

export function CouponForm({ initialData, onSubmit }: CouponFormProps) {
  const formConfig: CouponFormConfig = useMemo(
    () => ({
      fields: [
        {
          name: "code",
          label: "Code",
          type: "text",
          placeholder: "Coupon code",
        },
        {
          name: "discountType",
          label: "Discount Type",
          type: "select",
          placeholder: "Select discount type",
          options: [
            { label: "Percentage", value: "percentage" },
            { label: "Fixed", value: "fixed" },
          ],
        },
        {
          name: "discountValue",
          label: "Discount Value",
          type: "number",
          placeholder: "Discount value",
        },
        {
          name: "maxRedemptions",
          label: "Max Redemptions",
          type: "number",
          placeholder: "Maximum redemptions",
        },
        {
          name: "validFrom",
          label: "Valid From",
          type: "date",
          placeholder: "Valid from date",
        },
        {
          name: "validUntil",
          label: "Valid Until",
          type: "date",
          placeholder: "Valid until date",
        },
        {
          name: "isActive",
          label: "Is Active",
          type: "checkbox",
        },
        {
          name: "expirationDate",
          label: "Expiration Date",
          type: "date",
          placeholder: "Expiration date",
        },
      ],
      schema: couponSchema, // Assuming you have a schema defined for coupon validation
      onSubmit: onSubmit,
    }),
    [onSubmit],
  )

  return <Form {...formConfig} defaultValues={initialData} />
}
