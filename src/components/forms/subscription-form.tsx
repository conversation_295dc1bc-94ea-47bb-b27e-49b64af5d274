"use client"

import { useMemo } from "react"

import {
  SubscriptionFormConfig,
  SubscriptionFormProps,
  subscriptionSchema,
} from "@/types/subscription"

import { Form } from "./common/custom-form"

export function SubscriptionForm({
  initialData,
  onSubmit,
}: SubscriptionFormProps) {
  const formConfig: SubscriptionFormConfig = useMemo(
    () => ({
      fields: [
        {
          name: "userId",
          label: "User ID",
          type: "text",
          placeholder: "User ID",
        },
        {
          name: "planId",
          label: "Plan ID",
          type: "text",
          placeholder: "Plan ID",
        },
        {
          name: "startDate",
          label: "Start Date",
          type: "date",
          placeholder: "Start Date",
        },
        {
          name: "endDate",
          label: "End Date",
          type: "date",
          placeholder: "End Date",
        },
        {
          name: "isActive",
          label: "Is Active",
          type: "checkbox",
          // options: [
          //   { label: "Yes", value: true },
          //   { label: "No", value: false },
          // ],
        },
        {
          name: "autoRenew",
          label: "Auto Renew",
          type: "checkbox",
        },
        {
          name: "paymentMethod",
          label: "Payment Method",
          type: "text",
          placeholder: "Payment Method",
        },
        {
          name: "paymentStatus",
          label: "Payment Status",
          type: "select",
          options: [
            { label: "Paid", value: "paid" },
            { label: "Failed", value: "failed" },
            { label: "Pending", value: "pending" },
          ],
        },
        {
          name: "createdAt",
          label: "Created At",
          type: "date",
          placeholder: "Created At",
        },
        {
          name: "updatedAt",
          label: "Updated At",
          type: "date",
          placeholder: "Updated At",
        },
      ],
      schema: subscriptionSchema,
      onSubmit: onSubmit,
    }),
    [onSubmit],
  )

  return <Form {...formConfig} defaultValues={initialData} />
}
