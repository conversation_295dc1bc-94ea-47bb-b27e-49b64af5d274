"use client"

import { useMemo } from "react"

import { BlogFormConfig, BlogFormProps, blogSchema } from "@/types/blog" // Assuming the Blog interface and schema are defined in this path

import { Form } from "./common/custom-form"

export function BlogForm({ initialData, onSubmit }: BlogFormProps) {
  const formConfig: BlogFormConfig = useMemo(
    () => ({
      fields: [
        {
          name: "title",
          label: "Title",
          type: "text",
          placeholder: "Blog title",
        },
        {
          name: "slug",
          label: "Slug",
          type: "text",
          placeholder: "Unique slug for the blog",
        },
        {
          name: "content",
          label: "Content",
          type: "textarea",
          placeholder: "Content of the blog",
        },
        {
          name: "author",
          label: "Author",
          type: "text",
          placeholder: "User ID of the author",
        },
        {
          name: "categories",
          label: "Categories",
          type: "array",
          placeholder: "Comma separated categories",
        },
        {
          name: "tags",
          label: "Tags",
          type: "array",
          placeholder: "Comma separated tags",
        },
        {
          name: "thumbnailUrl",
          label: "Thumbnail URL",
          type: "text",
          placeholder: "URL of the thumbnail image",
        },
        {
          name: "isPublished",
          label: "Is Published",
          type: "checkbox",
          placeholder: "",
        },
        {
          name: "publishDate",
          label: "Publish Date",
          type: "date",
          placeholder: "Publish date of the blog",
        },
        {
          name: "views",
          label: "Views",
          type: "number",
          placeholder: "Number of views",
        },
        {
          name: "likes",
          label: "Likes",
          type: "number",
          placeholder: "Number of likes",
        },
      ],
      schema: blogSchema, // Assuming you have a schema defined for blog validation
      onSubmit: onSubmit,
    }),
    [onSubmit],
  )

  return <Form {...formConfig} defaultValues={initialData} />
}
