/* eslint-disable @next/next/no-img-element */
import { Check, PlusIcon, SearchIcon } from "lucide-react"
import { useEffect, useState } from "react"

import { searchUnsplash } from "@/actions/gallery"
import { But<PERSON> } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { cn } from "@/functions"
import { useDebounce } from "@/hooks"

interface UnsplashSearchProps {
  onSelect: (imageUrl: string, unsplashData: any) => void
  selectedImages: string[]
  onImageSelect: (imageUrl: string) => void
  allowMultiple: boolean
}

export function UnsplashSearch({
  onSelect,
  selectedImages,
  onImageSelect,
  allowMultiple,
}: UnsplashSearchProps) {
  const [query, setQuery] = useState("")
  const [results, setResults] = useState<any[]>([])
  const [page, setPage] = useState(1)
  const [totalPages, setTotalPages] = useState(0)
  const [isLoading, setIsLoading] = useState(false)

  const debouncedQuery = useDebounce(query, 300)

  const fetchImages = async (searchQuery: string, pageNum: number = 1) => {
    if (!searchQuery.trim()) {
      setResults([])
      setTotalPages(0)
      return
    }

    setIsLoading(true)
    try {
      const data = await searchUnsplash(searchQuery, pageNum)
      setResults((prevResults) =>
        pageNum === 1 ? data.results : [...prevResults, ...data.results],
      )
      setTotalPages(data.total_pages)
      setPage(pageNum)
    } catch (error) {
      console.error("Error fetching Unsplash images:", error)
    } finally {
      setIsLoading(false)
    }
  }

  useEffect(() => {
    fetchImages(debouncedQuery, 1)
  }, [debouncedQuery])

  return (
    <div className='flex h-full flex-col'>
      <div className='sticky top-0 z-10 rounded-md bg-muted/50 p-3'>
        <div className='relative'>
          <SearchIcon className='absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground' />
          <Input
            placeholder='Search Unsplash...'
            className='rounded-md pl-9 shadow-sm focus:border-primary focus:ring-primary'
            value={query}
            onChange={(e) => setQuery(e.target.value)}
          />
        </div>
      </div>

      <div className='flex-1 overflow-y-auto py-4'>
        {isLoading && query && (
          <div className='grid grid-cols-2 gap-4 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5'>
            {Array.from({ length: 10 }).map((_, index) => (
              <div
                key={index}
                className='aspect-square animate-pulse rounded-lg bg-muted'
              />
            ))}
          </div>
        )}

        <div className='grid grid-cols-2 gap-4 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5'>
          {results.map((image) => (
            <div
              key={image.id}
              onClick={() => {
                onImageSelect(image.urls.full)
                onSelect(image.urls.full, image)
              }}
              className={cn(
                "group relative aspect-square transform cursor-pointer overflow-hidden rounded-lg border-2 transition-transform duration-200 hover:scale-105 hover:border-primary",
                selectedImages.includes(image.urls.full)
                  ? "border-primary ring-2 ring-primary ring-offset-2"
                  : "border-transparent",
              )}
            >
              <img
                src={image.urls.small}
                alt={image.alt_description || "Unsplash Image"}
                className='h-full w-full object-cover transition-transform group-hover:scale-105'
              />
              <div className='absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 transition-opacity group-hover:opacity-100' />
              {image.alt_description && (
                <div className='absolute bottom-0 left-0 right-0 bg-black/50 p-3 backdrop-blur-sm'>
                  <p className='line-clamp-1 text-sm text-white'>
                    {image.alt_description}
                  </p>
                </div>
              )}
              {selectedImages.includes(image.urls.full) && (
                <div className='absolute right-2 top-2'>
                  <div className='rounded-full bg-primary p-1'>
                    <Check className='h-4 w-4 text-primary-foreground' />
                  </div>
                </div>
              )}
            </div>
          ))}
        </div>

        {page < totalPages && !isLoading && results.length > 0 && (
          <div className='mt-4 flex justify-center'>
            <Button
              onClick={() => fetchImages(query, page + 1)}
              variant='outline'
              className='gap-2 rounded-md'
            >
              <PlusIcon className='h-4 w-4' />
              Load More
            </Button>
          </div>
        )}
      </div>
    </div>
  )
}
