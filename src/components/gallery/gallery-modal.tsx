/* eslint-disable import/no-cycle */
/* eslint-disable @next/next/no-img-element */
import { useQuery } from "@tanstack/react-query"
import {
  AlertCircle,
  Check,
  FolderOpen,
  Image as ImageIcon,
  Search,
  UploadCloud,
} from "lucide-react"
import React, { useState } from "react"

import {
  createGalleryItem,
  fetchGalleryItemsWithPagination,
} from "@/actions/gallery"
import { Button } from "@/components/ui/button"
import {
  Dialog,
  DialogContent,
  DialogHeader,
  DialogTitle,
} from "@/components/ui/dialog"
import { Input } from "@/components/ui/input"
import { Tabs, TabsContent, TabsList, TabsTrigger } from "@/components/ui/tabs"
import { cn } from "@/functions"
import { useDebounce } from "@/hooks"
import { Gallery } from "@/types/gallery"

import { GalleryForm } from "./image-upload-form"
import { UnsplashSearch } from "./unsplash-searh"

interface GalleryModalProps {
  onSelect: (imageUrls: string) => void // Allow passing multiple image URLs
  allowMultiple: boolean // Control whether multiple images can be selected
}

export function GalleryModal({ onSelect, allowMultiple }: GalleryModalProps) {
  const [search, setSearch] = useState("")
  const [isOpen, setIsOpen] = useState(false)
  const [page, setPage] = useState(1)
  const [selectedImages, setSelectedImages] = useState<string[]>([])

  const limit = 10 // Number of items per page

  // Use debounced search input
  const debouncedSearch = useDebounce(search, 300)

  const {
    data: galleryResult,
    error,
    isLoading,
    isFetching,
    refetch,
  } = useQuery({
    queryKey: ["galleryItems", page, debouncedSearch],
    queryFn: () =>
      fetchGalleryItemsWithPagination(page, limit, debouncedSearch),
    enabled: true, // Modal doesn't control the open state anymore
  })

  const { data = [], currentPage, totalPages } = galleryResult || {}

  const hasMore = currentPage < totalPages

  const handleLoadMore = () => {
    if (hasMore) setPage((prev) => prev + 1)
  }

  const handleUnsplashSelect = async (imageUrl: string, unsplashData: any) => {
    try {
      await createGalleryItem({
        imageUrl,
        title: unsplashData.alt_description || "Unsplash Image",
        source: "Unsplash",
        sourceDetails: JSON.stringify(unsplashData),
      })
      setSelectedImages((prev) => [...prev, imageUrl])
      refetch()
    } catch (error) {
      console.error("Error creating gallery item from Unsplash:", error)
    }
  }

  const handleUploadComplete = (newItem: Gallery) => {
    setSelectedImages((prev) => [...prev, newItem.imageUrl])
    refetch()
  }

  const handleImageSelect = (imageUrl: string) => {
    setSelectedImages((prev) => {
      if (allowMultiple) {
        if (prev.includes(imageUrl)) {
          return prev.filter((img) => img !== imageUrl)
        } else {
          return [...prev, imageUrl]
        }
      } else {
        return [imageUrl]
      }
    })
  }

  type HandleConfirmSelectionEvent = React.MouseEvent<HTMLButtonElement>

  const handleConfirmSelection = (e: HandleConfirmSelectionEvent) => {
    e.preventDefault()
    if (allowMultiple) {
      onSelect(selectedImages.join(","))
    } else {
      onSelect(selectedImages[0])
    }
    onClose()
  }

  const onClose = () => {
    setIsOpen(false)
    setSelectedImages([])
  }

  return (
    <>
      <Button
        onClick={() => setIsOpen(true)}
        variant='outline'
        type='button'
        className='flex items-center gap-2'
      >
        <ImageIcon className='h-4 w-4' />
        {allowMultiple ? "Select Images" : "Select Image"}
      </Button>

      <Dialog open={isOpen} onOpenChange={onClose}>
        <DialogContent className='flex h-[85vh] w-[90vw] max-w-[1200px] flex-col gap-4 rounded-lg pb-8'>
          <DialogHeader className='px-4'>
            <DialogTitle className='text-2xl font-semibold'>
              Media Library
            </DialogTitle>
            <p className='text-sm text-muted-foreground'>
              Select {allowMultiple ? "images" : "an image"} from your gallery,
              search Unsplash, or upload new content
            </p>
          </DialogHeader>

          <div className='flex flex-1 flex-col overflow-hidden'>
            <Tabs defaultValue='gallery' className='flex h-full flex-col'>
              <TabsList className='sticky top-0 z-10 w-full justify-start gap-2 border-b bg-background/95 px-4 pb-2 pt-2 backdrop-blur-sm'>
                <TabsTrigger
                  value='gallery'
                  className='flex items-center gap-2 rounded-md px-4 py-2.5 text-sm font-medium transition-all data-[state=active]:bg-primary data-[state=active]:text-primary-foreground'
                >
                  <FolderOpen className='h-4 w-4' />
                  Gallery
                </TabsTrigger>
                <TabsTrigger
                  value='unsplash'
                  className='flex items-center gap-2 rounded-md px-4 py-2.5 text-sm font-medium transition-all data-[state=active]:bg-primary data-[state=active]:text-primary-foreground'
                >
                  <Search className='h-4 w-4' />
                  Unsplash
                </TabsTrigger>
                <TabsTrigger
                  value='upload'
                  className='flex items-center gap-2 rounded-md px-4 py-2.5 text-sm font-medium transition-all data-[state=active]:bg-primary data-[state=active]:text-primary-foreground'
                >
                  <UploadCloud className='h-4 w-4' />
                  Upload
                </TabsTrigger>
              </TabsList>

              <div className='flex-1 overflow-hidden pb-10'>
                <TabsContent
                  value='gallery'
                  className='h-full overflow-auto px-4 py-2 focus-visible:outline-none'
                >
                  <div className='flex h-full flex-col gap-4'>
                    <div className='sticky top-0 z-10 bg-background/95 pb-2 pt-2 backdrop-blur-sm'>
                      <div className='relative'>
                        <Search className='absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground' />
                        <Input
                          placeholder='Search in gallery...'
                          value={search}
                          onChange={(e) => setSearch(e.target.value)}
                          className='h-10 rounded-md border-muted bg-background pl-9 pr-4 ring-offset-background placeholder:text-muted-foreground focus-visible:ring-2 focus-visible:ring-ring focus-visible:ring-offset-2'
                        />
                      </div>
                    </div>

                    <div className='flex-1 overflow-y-auto'>
                      <div className='grid grid-cols-2 gap-4 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5'>
                        {isLoading &&
                          Array(10)
                            .fill(0)
                            .map((_, i) => (
                              <div
                                key={i}
                                className='aspect-square animate-pulse rounded-lg bg-muted/60'
                              />
                            ))}
                        {error && (
                          <div className='col-span-full flex items-center justify-center rounded-lg border border-destructive/50 bg-destructive/10 p-8 text-center text-destructive'>
                            <AlertCircle className='mr-2 h-4 w-4' />
                            Error loading images
                          </div>
                        )}
                        {data?.map((item: Gallery) => (
                          <div
                            key={item._id}
                            onClick={() => handleImageSelect(item.imageUrl)}
                            className={cn(
                              "hover:scale-102 group relative aspect-square cursor-pointer overflow-hidden rounded-lg border-2 bg-muted/10 transition-all duration-200 hover:border-primary/80",
                              selectedImages.includes(item.imageUrl)
                                ? "border-primary ring-2 ring-primary ring-offset-2"
                                : "border-transparent",
                            )}
                          >
                            <img
                              src={item.imageUrl}
                              alt={item.title || ""}
                              className='h-full w-full object-cover transition-transform duration-200 group-hover:scale-105'
                              loading='lazy'
                            />
                            <div className='absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent opacity-0 transition-opacity duration-200 group-hover:opacity-100' />
                            {item.title && (
                              <div className='absolute bottom-0 left-0 right-0 translate-y-full bg-black/50 p-3 backdrop-blur-sm transition-transform duration-200 group-hover:translate-y-0'>
                                <p className='line-clamp-1 text-sm font-medium text-white'>
                                  {item.title}
                                </p>
                              </div>
                            )}
                            {selectedImages.includes(item.imageUrl) && (
                              <div className='absolute right-2 top-2'>
                                <div className='rounded-full bg-primary p-1.5 shadow-lg'>
                                  <Check className='h-3.5 w-3.5 text-primary-foreground' />
                                </div>
                              </div>
                            )}
                          </div>
                        ))}
                      </div>
                    </div>
                  </div>
                </TabsContent>

                <TabsContent
                  value='unsplash'
                  className='h-full overflow-auto px-4 py-2 focus-visible:outline-none'
                >
                  <UnsplashSearch
                    onSelect={handleUnsplashSelect}
                    selectedImages={selectedImages}
                    onImageSelect={handleImageSelect}
                    allowMultiple={allowMultiple}
                  />
                </TabsContent>

                <TabsContent
                  value='upload'
                  className='h-full overflow-auto px-4 py-2 focus-visible:outline-none'
                >
                  <GalleryForm onSubmit={handleUploadComplete} />
                </TabsContent>
              </div>
            </Tabs>
          </div>

          <div className='fixed bottom-0 flex w-full items-center justify-between gap-4 rounded-b-lg border-t bg-background px-4 py-3'>
            <p className='text-sm text-muted-foreground'>
              {selectedImages.length}{" "}
              {selectedImages.length === 1 ? "image" : "images"} selected
            </p>
            <div className='flex gap-2'>
              <Button
                variant='outline'
                onClick={onClose}
                className='rounded-md'
              >
                Cancel
              </Button>
              <Button
                onClick={handleConfirmSelection}
                disabled={selectedImages.length === 0}
                className='rounded-md'
              >
                {allowMultiple ? "Add Images" : "Add Image"}
              </Button>
            </div>
          </div>
        </DialogContent>
      </Dialog>
    </>
  )
}
