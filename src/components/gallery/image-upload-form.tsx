"use client"

import { useMemo } from "react"

import {
  GalleryFormConfig,
  GalleryFormProps,
  gallerySchema,
} from "@/types/gallery"

import { Form } from "../forms/common/custom-form"

// Zod schema for Gallery

export function GalleryForm({ initialData, onSubmit }: GalleryFormProps) {
  const formConfig: GalleryFormConfig = useMemo(
    () => ({
      fields: [
        {
          name: "imageUrl",
          label: "Upload Image",
          type: "s3-image",
        },

        {
          name: "imageUrl",
          label: "Image URL",
          type: "text",
          placeholder: "URL of the image",
        },

        {
          name: "imageKey",
          label: "Image Key",
          type: "text",
          placeholder: "Key of the image",
        },
        {
          name: "title",
          label: "Title",
          type: "text",
          placeholder: "Title of the image",
        },
        {
          name: "description",
          label: "Description",
          type: "textarea",
          placeholder: "Description of the image",
        },
        {
          name: "tags",
          label: "Tags",
          type: "array",
          placeholder: "Comma separated tags",
        },

        {
          name: "source",
          label: "Source",
          type: "select",
          // options: ["S3", "UploadThing", "Unsplash", "External"],
          options: [
            { value: "S3", label: "S3" },
            { value: "UploadThing", label: "UploadThing" },
            { value: "Unsplash", label: "Unsplash" },
            { value: "External", label: "External" },
          ],
        },

        {
          name: "fileName",
          label: "File Name",
          type: "text",
          placeholder: "Name of the file",
        },
        {
          name: "fileSize",
          label: "File Size",
          type: "number",
          placeholder: "Size of the file",
        },
        {
          name: "fileType",
          label: "File Type",
          type: "text",
          placeholder: "Type of the file",
        },
        {
          name: "category",
          label: "Category",
          type: "text",
          placeholder: "Category of the image",
        },

        {
          name: "sourceDetails",
          label: "Source Details",
          type: "text",
          placeholder: "Details of the source",
        },
        {
          name: "uploadedBy",
          label: "Uploaded By",
          type: "text",
          placeholder: "User ID of the uploader",
        },

        {
          name: "isPublic",
          label: "Is Public",
          type: "checkbox",
        },
        {
          name: "isDeleted",
          label: "Is Deleted",
          type: "checkbox",
        },
        {
          name: "isFeatured",
          label: "Is Featured",
          type: "checkbox",
        },
      ],
      schema: gallerySchema,
      onSubmit: onSubmit,
    }),
    [onSubmit],
  )

  return <Form {...formConfig} defaultValues={initialData} />
}
