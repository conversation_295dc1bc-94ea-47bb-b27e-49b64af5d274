"use client"
import { ColumnDef } from "@tanstack/react-table"

import { Checkbox } from "@/components/ui/checkbox"
import { Plan } from "@/types/plan" // Make sure you have an appropriate Plan type defined.

import { CellAction } from "./cell-action"

export const columns: ColumnDef<Plan>[] = [
  {
    id: "_id",
    header: ({ table }) => (
      <Checkbox
        checked={table.getIsAllPageRowsSelected()}
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label='Select all'
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label='Select row'
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: "_id",
    header: "Plan ID",
  },
  {
    accessorKey: "name",
    header: "Name",
  },
  {
    accessorKey: "description",
    header: "Description",
    cell: ({ row }) => {
      const plan = row.original as Plan
      return plan.description ?? "N/A"
    },
  },
  {
    accessorKey: "price",
    header: "Price",
    cell: ({ row }) => {
      const plan = row.original as Plan
      return `${plan.currency} ${plan.price.toFixed(2)}`
    },
  },
  {
    accessorKey: "duration",
    header: "Duration",
  },
  {
    accessorKey: "isActive",
    header: "Active",
    cell: ({ row }) => {
      const plan = row.original as Plan
      return plan.isActive ? "Yes" : "No"
    },
  },
  {
    accessorKey: "maxTemplates",
    header: "Max Templates",
    cell: ({ row }) => {
      const plan = row.original as Plan
      return plan.maxTemplates ?? "N/A"
    },
  },
  {
    accessorKey: "maxUsers",
    header: "Max Users",
    cell: ({ row }) => {
      const plan = row.original as Plan
      return plan.maxUsers ?? "N/A"
    },
  },
  {
    accessorKey: "benefits",
    header: "Benefits",
    cell: ({ row }) => {
      const plan = row.original as Plan
      // return plan.benefits?.join(", ") ?? "N/A";
      return plan.benefits ?? "N/A"
    },
  },
  {
    accessorKey: "createdBy",
    header: "Created By",
  },
  {
    id: "actions",
    cell: ({ row }) => <CellAction data={row.original as Plan} />,
  },
]
