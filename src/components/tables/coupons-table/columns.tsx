"use client"
import { ColumnDef } from "@tanstack/react-table"

import { Checkbox } from "@/components/ui/checkbox"
import { Coupon } from "@/types/coupon"

import { CellAction } from "./cell-action"

export const columns: ColumnDef<Coupon>[] = [
  {
    id: "_id",
    header: ({ table }) => (
      <Checkbox
        checked={table.getIsAllPageRowsSelected()}
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label='Select all'
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label='Select row'
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: "_id",
    header: "Id",
  },
  {
    accessorKey: "code",
    header: "Code",
  },
  {
    accessorKey: "discountType",
    header: "Discount Type",
  },
  {
    accessorKey: "discountValue",
    header: "Discount Value",
    cell: ({ row }) => {
      const coupon = row.original as Coupon
      return coupon.discountType === "percentage"
        ? `${coupon.discountValue}%`
        : `$${coupon.discountValue}`
    },
  },
  {
    accessorKey: "maxRedemptions",
    header: "Max Redemptions",
    cell: ({ row }) => {
      const coupon = row.original as Coupon
      return coupon.maxRedemptions ?? "Unlimited"
    },
  },
  {
    accessorKey: "currentRedemptions",
    header: "Current Redemptions",
  },
  {
    accessorKey: "validFrom",
    header: "Valid From",
    cell: ({ row }) => {
      const coupon = row.original as Coupon
      return new Date(coupon.validFrom).toLocaleDateString()
    },
  },
  {
    accessorKey: "validUntil",
    header: "Valid Until",
    cell: ({ row }) => {
      const coupon = row.original as Coupon
      return new Date(coupon.validUntil).toLocaleDateString()
    },
  },
  {
    accessorKey: "isActive",
    header: "Active",
    cell: ({ row }) => {
      const coupon = row.original as Coupon
      return coupon.isActive ? "Yes" : "No"
    },
  },
  {
    id: "actions",
    cell: ({ row }) => <CellAction data={row.original as Coupon} />,
  },
]
