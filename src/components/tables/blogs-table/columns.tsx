"use client"
import { ColumnDef } from "@tanstack/react-table"

import { Checkbox } from "@/components/ui/checkbox"
import { Blog } from "@/types/blog"

import { CellAction } from "./cell-action"

export const columns: ColumnDef<Blog>[] = [
  {
    id: "_id",
    header: ({ table }) => (
      <Checkbox
        checked={table.getIsAllPageRowsSelected()}
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label='Select all'
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label='Select row'
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: "_id",
    header: "Id",
  },
  {
    accessorKey: "title",
    header: "Title",
  },
  {
    accessorKey: "slug",
    header: "Slug",
  },
  {
    accessorKey: "author",
    header: "Author",
  },
  {
    accessorKey: "categories",
    header: "Categories",
    cell: ({ row }) => {
      const blog = row.original as Blog
      return blog.categories?.join(", ") ?? "None"
    },
  },
  {
    accessorKey: "tags",
    header: "Tags",
    cell: ({ row }) => {
      const blog = row.original as Blog
      return blog.tags?.join(", ") ?? "None"
    },
  },
  {
    accessorKey: "thumbnailUrl",
    header: "Thumbnail",
    cell: ({ row }) => {
      const blog = row.original as Blog
      return blog.thumbnailUrl ? (
        <img
          src={blog.thumbnailUrl}
          alt='Thumbnail'
          style={{ width: "50px", height: "50px" }}
        />
      ) : (
        "No Image"
      )
    },
  },
  {
    accessorKey: "isPublished",
    header: "Published",
    cell: ({ row }) => {
      const blog = row.original as Blog
      return blog.isPublished ? "Yes" : "No"
    },
  },
  {
    accessorKey: "publishDate",
    header: "Publish Date",
    cell: ({ row }) => {
      const blog = row.original as Blog
      return blog.publishDate
        ? new Date(blog.publishDate).toLocaleDateString()
        : "Not Published"
    },
  },
  {
    accessorKey: "views",
    header: "Views",
  },
  {
    accessorKey: "likes",
    header: "Likes",
  },
  {
    id: "actions",
    cell: ({ row }) => <CellAction data={row.original as Blog} />,
  },
]
