"use client"
import { ColumnDef } from "@tanstack/react-table"
import { formatDate } from "date-fns"

import { Checkbox } from "@/components/ui/checkbox"
import { User } from "@/types/user" // Make sure you have an appropriate User type defined.

import { CellAction } from "./cell-action"

export const columns: ColumnDef<User>[] = [
  {
    id: "_id",
    header: ({ table }) => (
      <div className='flex items-center justify-start gap-2'>
        <Checkbox
          checked={table.getIsAllPageRowsSelected()}
          onCheckedChange={(value) => {
            console.log(value)
            table.toggleAllPageRowsSelected(!!value)
          }}
          aria-label='Select all'
        />
        <span>ID</span>
      </div>
    ),
    cell: ({ row }) => (
      <div className='flex items-center justify-start gap-2'>
        <Checkbox
          checked={row.getIsSelected()}
          onCheckedChange={(value) => row.toggleSelected(!!value)}
          aria-label='Select row'
        />
        <span>{row.getValue("_id")}</span>
      </div>
    ),
    enableSorting: false,
    enableHiding: false,
  },

  {
    accessorKey: "username",
    header: "Username",
  },
  {
    accessorKey: "email",
    header: "Email",
  },

  {
    accessorKey: "role",
    header: "Role",
  },
  {
    accessorKey: "createdAt",
    header: "Created At",
    cell: ({ row }) => {
      const user = row.original as User
      // return new Date(user.createdAt).toLocaleDateString();
      return user?.createdAt
        ? formatDate(user?.createdAt, "yyyy-MM-dd")
        : user?.createdAt
    },
  },
  {
    accessorKey: "name",
    header: "Name",
  },

  {
    accessorKey: "phone",
    header: "Phone",
  },
  {
    accessorKey: "address",
    header: "Address",
  },

  {
    accessorKey: "lastLogin",
    header: "Last Login",
    cell: ({ row }) => {
      const user = row.original as User
      // return new Date(user.lastLogin).toLocaleDateString();
      return user.lastLogin
    },
  },
  {
    accessorKey: "status",
    header: "Status",
  },
  {
    accessorKey: "actions",
    cell: ({ row }) => <CellAction data={row.original as User} />,
  },
]
