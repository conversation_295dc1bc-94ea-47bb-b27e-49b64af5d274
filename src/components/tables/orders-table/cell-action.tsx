"use client"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Trash } from "lucide-react"
import { useRouter } from "next/navigation"
import { useState } from "react"
import { toast } from "sonner"

import { deleteOrder } from "@/actions/orders"
import { AlertModal } from "@/components/modal/alert-modal"
import { Button } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Category } from "@/types/category"
import { Order } from "@/types/order"
// import { User } from '@/constants/data';

interface CellActionProps {
  data: Order
}

export const CellAction: React.FC<CellActionProps> = ({ data }) => {
  const [loading, setLoading] = useState(false)
  const [open, setOpen] = useState(false)
  const router = useRouter()

  const onConfirm = async () => {
    console.log("Delete", data)
    // toast.loading("deleting category...");
    if (!data._id) return

    const toastId = toast("Deleting Order...")
    try {
      setLoading(true)
      const result = await deleteOrder(data._id)
      if (result?.success)
        toast.success("Order deleted successfully", { id: toastId })
      else {
        toast.error(result?.message || "Error deleting Order", {
          id: toastId,
        })
      }
    } catch (error) {
      toast.error("Error deleting Order", { id: toastId })
      console.log(error)
    } finally {
      toast.dismiss(toastId)
      setLoading(false)
      setOpen(false)
    }
  }

  return (
    <>
      <AlertModal
        isOpen={open}
        onClose={() => setOpen(false)}
        onConfirm={onConfirm}
        loading={loading}
      />
      <DropdownMenu modal={false}>
        <DropdownMenuTrigger asChild>
          <Button variant='ghost' className='h-8 w-8 p-0'>
            <span className='sr-only'>Open menu</span>
            <MoreHorizontal className='h-4 w-4' />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align='end'>
          <DropdownMenuLabel>Actions</DropdownMenuLabel>

          <DropdownMenuItem
            onClick={() => router.push(`/dashboard/orders/${data._id}`)}
          >
            <Edit className='mr-2 h-4 w-4' /> Update
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => setOpen(true)}>
            <Trash className='mr-2 h-4 w-4' /> Delete
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </>
  )
}
