"use client"
import { ColumnDef } from "@tanstack/react-table"

import { Checkbox } from "@/components/ui/checkbox"
import { Order } from "@/types/order" // Make sure you have an appropriate Order type defined.

import { CellAction } from "./cell-action"

export const columns: ColumnDef<Order>[] = [
  {
    id: "_id",
    header: ({ table }) => (
      <Checkbox
        checked={table.getIsAllPageRowsSelected()}
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label='Select all'
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label='Select row'
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: "_id",
    header: "Order ID",
  },
  {
    accessorKey: "user",
    header: "User",
    // cell: ({ row }) => {
    //   const order = row.original as Order;
    //   return order.user?.email ?? "N/A"; // Assuming the user object contains an email field.
    // },
    cell: ({ row }) => {
      const order = row.original as Order
      return order.user ?? "N/A" // Assuming the user object contains an email field.
    },
  },
  {
    accessorKey: "products",
    header: "Products",
    cell: ({ row }) => {
      const order = row.original as Order
      return order.products.map((t) => t).join(", ") // Assuming each template has a `templateName` field.
    },
  },
  {
    accessorKey: "totalAmount",
    header: "Total Amount",
    cell: ({ row }) => {
      const order = row.original as Order
      return `$${order.totalAmount.toFixed(2)}`
    },
  },
  {
    accessorKey: "payment",
    header: "Payment ID",
    cell: ({ row }) => {
      const order = row.original as Order
      return order.payment ?? "N/A" // Assuming payment object contains an `_id`.
    },
  },
  {
    accessorKey: "status",
    header: "Status",
    cell: ({ row }) => {
      const order = row.original as Order
      return order.status.charAt(0).toUpperCase() + order.status.slice(1) // Capitalize status.
    },
  },
  {
    accessorKey: "deliveryEmail",
    header: "Delivery Email",
  },
  {
    accessorKey: "shippingAddress",
    header: "Shipping Address",
    cell: ({ row }) => {
      const order = row.original as Order
      const address = order.shippingAddress
      return address
        ? `${address.street}, ${address.city}, ${address.state}, ${address.postalCode}, ${address.country}`
        : "N/A"
    },
  },
  {
    accessorKey: "createdAt",
    header: "Order Date",
    cell: ({ row }) => {
      const order = row.original as Order
      return order?.created_at
        ? new Date(order?.created_at).toLocaleDateString()
        : "N/A"
    },
  },
  {
    id: "actions",
    cell: ({ row }) => <CellAction data={row.original as Order} />,
  },
]
