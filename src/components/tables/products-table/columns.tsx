"use client"
import { ColumnDef } from "@tanstack/react-table"

import TooltipList from "@/components/custom/tooltip"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Checkbox } from "@/components/ui/checkbox"
import {
  Toolt<PERSON>,
  TooltipContent,
  TooltipTrigger,
} from "@/components/ui/tooltip"
import { Product } from "@/types/product" // Make sure you have an appropriate Product type defined.
import { createArrayFromString } from "@/utils/generic"

import { CellAction } from "./cell-action"

export const columns: ColumnDef<Product>[] = [
  {
    id: "_id",
    header: ({ table }) => (
      <Checkbox
        checked={table.getIsAllPageRowsSelected()}
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label='Select all'
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label='Select row'
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: "name",
    header: "Name",
  },
  {
    accessorKey: "slug",
    header: "Slug",
  },
  {
    accessorKey: "description",
    header: "Description",
    cell: ({ row }) => {
      const product = row.original as Product
      return <TooltipList item={product?.description ?? ""} />

      // return (
      //   <span className="line-clamp-1">{product.description ?? "N/A"}</span>
      // );
    },
  },
  {
    accessorKey: "type",
    header: "Type",
  },
  {
    accessorKey: "category",
    header: "Category",
    cell: ({ row }) => {
      const product = row.original as Product
      return product.category ?? "N/A"
    },
  },
  // {
  //   accessorKey: "categories",
  //   header: "Categories",
  //   cell: ({ row }) => {
  //     const product = row.original as Product;
  //     return product.categories?.join(", ") ?? "N/A";
  //   },
  // },
  // {
  //   accessorKey: "superCategory",
  //   header: "Super Category",
  //   cell: ({ row }) => {
  //     const product = row.original as Product;
  //     return product.superCategory ?? "N/A";
  //   },
  // },
  {
    accessorKey: "tags",
    header: "Tags",
    cell: ({ row }) => {
      const product = row.original as Product
      return <TooltipList items={product?.tags ?? []} />
    },
  },
  {
    accessorKey: "subTypes",
    header: "Sub Types",
    cell: ({ row }) => {
      const product = row.original as Product
      // return product.subTypes?.join(", ") ?? "N/A";
      return <TooltipList items={product?.subTypes ?? []} />
    },
  },
  {
    accessorKey: "techStack",
    header: "Tech Stack",
    cell: ({ row }) => {
      const product = row.original as Product
      // return product.techStack?.join(", ") ?? "N/A";
      return <TooltipList items={product?.techStack ?? []} />
    },
  },
  {
    accessorKey: "price",
    header: "Price",
    cell: ({ row }) => {
      const product = row.original as Product
      return product.price !== undefined
        ? `$${product.price.toFixed(2)}`
        : "N/A"
    },
  },
  {
    accessorKey: "isPaid",
    header: "Paid",
    cell: ({ row }) => {
      const product = row.original as Product
      return product.isPaid ? "Yes" : "No"
    },
  },
  {
    accessorKey: "downloads",
    header: "Downloads",
    cell: ({ row }) => {
      const product = row.original as Product
      return product.downloads ?? "N/A"
    },
  },
  {
    accessorKey: "previewUrl",
    header: "Preview URL",
    cell: ({ row }) => {
      const product = row.original as Product
      return product.previewUrl ? (
        <a href={product.previewUrl} target='_blank' rel='noopener noreferrer'>
          Preview
        </a>
      ) : (
        "N/A"
      )
    },
  },
  {
    accessorKey: "paymentLink",
    header: "Payment Link",
    cell: ({ row }) => {
      const product = row.original as Product
      return product?.paymentLink ? (
        <a href={product.paymentLink} target='_blank' rel='noopener noreferrer'>
          Payment
        </a>
      ) : (
        "N/A"
      )
    },
  },
  // {
  //   accessorKey: "authorId",
  //   header: "Author ID",
  // },
  {
    accessorKey: "rating",
    header: "Rating",
    cell: ({ row }) => {
      const product = row.original as Product
      return product?.rating?.toFixed(1)
    },
  },
  {
    accessorKey: "keyFeatures",
    header: "Key Features",
    cell: ({ row }) => {
      const product = row.original as Product
      // return product.keyFeatures?.join(", ") ?? "N/A";
      return (
        // <span className="line-clamp-1"> {product.keyFeatures ?? "N/A"} </span>
        <TooltipList
          items={createArrayFromString(product?.keyFeatures) ?? []}
        />
      )
    },
  },
  {
    accessorKey: "highlights",
    header: "Highlights",
    cell: ({ row }) => {
      const product = row.original as Product
      // return product.highlights?.join(", ") ?? "N/A";
      return (
        <TooltipList items={createArrayFromString(product?.highlights) ?? []} />
      )
    },
  },
  {
    accessorKey: "status",
    header: "Status",
  },
  {
    accessorKey: "isFeatured",
    header: "Featured",
    cell: ({ row }) => {
      const product = row.original as Product
      return product.isFeatured ? "Yes" : "No"
    },
  },
  {
    accessorKey: "discount",
    header: "Discount",
    cell: ({ row }) => {
      const product = row.original as Product
      return `${product.discount}%`
    },
  },
  {
    id: "actions",
    cell: ({ row }) => <CellAction data={row.original as Product} />,
  },
]
