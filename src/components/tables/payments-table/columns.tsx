"use client"
import { ColumnDef } from "@tanstack/react-table"

import { Checkbox } from "@/components/ui/checkbox"
import { Payment } from "@/types/payment"

import { CellAction } from "./cell-action"

export const columns: ColumnDef<Payment>[] = [
  {
    id: "_id",
    header: ({ table }) => (
      <Checkbox
        checked={table.getIsAllPageRowsSelected()}
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label='Select all'
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label='Select row'
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: "_id",
    header: "Payment ID",
  },
  {
    accessorKey: "user",
    header: "User ID",
  },
  {
    accessorKey: "amount",
    header: "Amount",
    cell: ({ row }) => {
      const payment = row.original as Payment
      return `$${payment.amount.toFixed(2)}`
    },
  },
  {
    accessorKey: "currency",
    header: "Currency",
  },
  {
    accessorKey: "status",
    header: "Status",
    cell: ({ row }) => {
      const payment = row.original as Payment
      return payment.status.charAt(0).toUpperCase() + payment.status.slice(1) // Capitalize status.
    },
  },
  {
    accessorKey: "method",
    header: "Payment Method",
  },
  {
    accessorKey: "transactionId",
    header: "Transaction ID",
  },
  {
    accessorKey: "description",
    header: "Description",
  },
  {
    accessorKey: "isRefunded",
    header: "Refunded",
    cell: ({ row }) => {
      const payment = row.original as Payment
      return payment.isRefunded ? "Yes" : "No"
    },
  },
  {
    accessorKey: "createdAt",
    header: "Created At",
    cell: ({ row }) => {
      const payment = row.original as Payment
      return payment.createdAt
        ? new Date(payment.createdAt).toLocaleDateString()
        : "N/A"
    },
  },
  {
    accessorKey: "updatedAt",
    header: "Updated At",
    cell: ({ row }) => {
      const payment = row.original as Payment
      return payment.updatedAt
        ? new Date(payment.updatedAt).toLocaleDateString()
        : "N/A"
    },
  },
  {
    id: "actions",
    cell: ({ row }) => <CellAction data={row.original as Payment} />,
  },
]
