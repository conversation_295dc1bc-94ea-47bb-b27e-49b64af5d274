"use client"
import { ColumnDef } from "@tanstack/react-table"

import { Checkbox } from "@/components/ui/checkbox"
import { Subscription } from "@/types/subscription" // Make sure you have an appropriate Subscription type defined.

import { CellAction } from "./cell-action"

export const columns: ColumnDef<Subscription>[] = [
  {
    id: "_id",
    header: ({ table }) => (
      <Checkbox
        checked={table.getIsAllPageRowsSelected()}
        onCheckedChange={(value) => table.toggleAllPageRowsSelected(!!value)}
        aria-label='Select all'
      />
    ),
    cell: ({ row }) => (
      <Checkbox
        checked={row.getIsSelected()}
        onCheckedChange={(value) => row.toggleSelected(!!value)}
        aria-label='Select row'
      />
    ),
    enableSorting: false,
    enableHiding: false,
  },
  {
    accessorKey: "userId",
    header: "User ID",
  },
  {
    accessorKey: "planId",
    header: "Plan ID",
  },
  {
    accessorKey: "startDate",
    header: "Start Date",
    cell: ({ row }) => {
      const subscription = row.original as Subscription
      return new Date(subscription.startDate).toLocaleDateString()
    },
  },
  {
    accessorKey: "endDate",
    header: "End Date",
    cell: ({ row }) => {
      const subscription = row.original as Subscription
      return subscription?.endDate
        ? new Date(subscription?.endDate).toLocaleDateString()
        : "N/A"
    },
  },
  {
    accessorKey: "isActive",
    header: "Active",
    cell: ({ row }) => {
      const subscription = row.original as Subscription
      return subscription.isActive ? "Yes" : "No"
    },
  },
  {
    accessorKey: "isCurrent",
    header: "Current",
    cell: ({ row }) => {
      const subscription = row.original as Subscription
      return subscription.isCurrent ? "Yes" : "No"
    },
  },
  {
    accessorKey: "autoRenew",
    header: "Auto Renew",
    cell: ({ row }) => {
      const subscription = row.original as Subscription
      return subscription.autoRenew ? "Yes" : "No"
    },
  },
  {
    accessorKey: "paymentMethod",
    header: "Payment Method",
  },
  {
    accessorKey: "paymentStatus",
    header: "Payment Status",
  },
  {
    id: "actions",
    cell: ({ row }) => <CellAction data={row.original as Subscription} />,
  },
]
