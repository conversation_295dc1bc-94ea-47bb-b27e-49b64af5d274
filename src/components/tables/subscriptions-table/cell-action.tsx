"use client"
import { <PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, Trash } from "lucide-react"
import { useRout<PERSON> } from "next/navigation"
import { useState } from "react"
import { toast } from "sonner"

import { deleteSubscription } from "@/actions/subscriptions"
import { AlertModal } from "@/components/modal/alert-modal"
import { Button } from "@/components/ui/button"
import {
  DropdownMenu,
  DropdownMenuContent,
  DropdownMenuItem,
  DropdownMenuLabel,
  DropdownMenuTrigger,
} from "@/components/ui/dropdown-menu"
import { Subscription } from "@/types/subscription"

interface CellActionProps {
  data: Subscription
}

export const CellAction: React.FC<CellActionProps> = ({ data }) => {
  const [loading, setLoading] = useState(false)
  const [open, setOpen] = useState(false)
  const router = useRouter()

  const onConfirm = async () => {
    console.log("Delete", data)
    // toast.loading("deleting category...");
    if (!data._id) return

    const toastId = toast("Deleting Subscription...")
    try {
      setLoading(true)
      const result = await deleteSubscription(data._id)
      if (result?.success) toast.success("Subscription deleted successfully")
      else {
        toast.error(result?.message || "Error deleting Subscription")
      }
    } catch (error) {
      toast.error("Error deleting Subscription", { id: toastId })
      console.log(error)
    } finally {
      toast.dismiss(toastId)
      setLoading(false)
      setOpen(false)
    }
  }

  return (
    <>
      <AlertModal
        isOpen={open}
        onClose={() => setOpen(false)}
        onConfirm={onConfirm}
        loading={loading}
      />
      <DropdownMenu modal={false}>
        <DropdownMenuTrigger asChild>
          <Button variant='ghost' className='h-8 w-8 p-0'>
            <span className='sr-only'>Open menu</span>
            <MoreHorizontal className='h-4 w-4' />
          </Button>
        </DropdownMenuTrigger>
        <DropdownMenuContent align='end'>
          <DropdownMenuLabel>Actions</DropdownMenuLabel>

          <DropdownMenuItem
            onClick={() => router.push(`/dashboard/subscriptions/${data._id}`)}
          >
            <Edit className='mr-2 h-4 w-4' /> Update
          </DropdownMenuItem>
          <DropdownMenuItem onClick={() => setOpen(true)}>
            <Trash className='mr-2 h-4 w-4' /> Delete
          </DropdownMenuItem>
        </DropdownMenuContent>
      </DropdownMenu>
    </>
  )
}
