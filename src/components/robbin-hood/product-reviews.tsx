import { Star } from "lucide-react"

import Heading from "@/components/global/heading"
import { Avatar, AvatarFallback, AvatarImage } from "@/components/ui/avatar"
import { Card } from "@/components/ui/card"

const reviews = [
  {
    id: 1,
    name: "<PERSON>",
    avatar: "/placeholder.svg?height=40&width=40",
    rating: 5,
    comment:
      "This template is amazing! It saved me so much time and effort in setting up my SEO agency website.",
  },
  {
    id: 2,
    name: "<PERSON>",
    avatar: "/placeholder.svg?height=40&width=40",
    rating: 4,
    comment:
      "Great template with lots of features. The only reason I'm not giving 5 stars is because I had some trouble with customization.",
  },
  {
    id: 3,
    name: "<PERSON>",
    avatar: "/placeholder.svg?height=40&width=40",
    rating: 5,
    comment:
      "Absolutely worth every penny. The code is clean, well-documented, and easy to extend.",
  },
]

export function ProductReviews() {
  return (
    <div className='space-y-6'>
      {/* <h2 className="text-2xl font-semibold">Customer Reviews</h2> */}
      <Heading className='text-start'>Customer Reviews</Heading>

      <div className='grid gap-6 md:grid-cols-2'>
        {reviews.map((review) => (
          <Card key={review.id} className='border-card bg-card'>
            <div className='space-y-4 p-6'>
              <div className='flex items-center gap-4'>
                <Avatar>
                  <AvatarImage src={review.avatar} alt={review.name} />
                  <AvatarFallback>{review.name.charAt(0)}</AvatarFallback>
                </Avatar>
                <div>
                  <h3 className='font-semibold'>{review.name}</h3>
                  <div className='flex'>
                    {[...Array(5)].map((_, i) => (
                      <Star
                        key={i}
                        className={`h-4 w-4 ${
                          i < review.rating
                            ? "fill-yellow-400 text-yellow-400"
                            : "text-gray-400"
                        }`}
                      />
                    ))}
                  </div>
                </div>
              </div>
              <p className='text-gray-400'>{review.comment}</p>
            </div>
          </Card>
        ))}
      </div>
    </div>
  )
}
