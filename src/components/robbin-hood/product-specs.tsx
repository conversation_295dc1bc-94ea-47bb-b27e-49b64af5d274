"use client"

import { motion } from "framer-motion"
import { Calendar, Download, Package, Star, Users } from "lucide-react"

import Heading from "@/components/global/heading"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"

interface ProductSpecsProps {
  product: {
    version?: string
    lastUpdated: string
    downloads: number
    likes: number
    comments?: number
    fileSize?: string
    license?: string
  }
}

export function ProductSpecs({ product }: ProductSpecsProps) {
  const specs = [
    {
      icon: Package,
      label: "Version",
      value: product.version || "1.0.0",
    },
    {
      icon: Calendar,
      label: "Last Updated",
      value: new Date(product.lastUpdated).toLocaleDateString(),
    },
    {
      icon: Download,
      label: "Downloads",
      value: product.downloads.toLocaleString(),
    },
    {
      icon: Star,
      label: "Likes",
      value: product.likes.toString(),
    },
    {
      icon: Users,
      label: "Comments",
      value: (product.comments || 0).toString(),
    },
  ]

  return (
    <div className='space-y-6'>
      <Heading className='text-start'>Product Specifications</Heading>
      <Card className='border-gray-800 bg-gray-900/50'>
        <CardContent className='p-6'>
          <div className='grid gap-4 md:grid-cols-2 lg:grid-cols-3'>
            {specs.map((spec, index) => (
              <motion.div
                key={spec.label}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.1 }}
                className='flex items-center gap-3'
              >
                <div className='flex h-10 w-10 items-center justify-center rounded-lg bg-primary/10'>
                  <spec.icon className='h-5 w-5 text-primary' />
                </div>
                <div>
                  <p className='text-sm text-gray-400'>{spec.label}</p>
                  <p className='font-semibold text-white'>{spec.value}</p>
                </div>
              </motion.div>
            ))}
          </div>

          <div className='mt-6 border-t border-gray-800 pt-6'>
            <div className='flex flex-wrap gap-2'>
              <Badge
                variant='secondary'
                className='bg-green-900/20 text-green-400'
              >
                Commercial License
              </Badge>
              <Badge
                variant='secondary'
                className='bg-blue-900/20 text-blue-400'
              >
                Lifetime Updates
              </Badge>
              <Badge
                variant='secondary'
                className='bg-purple-900/20 text-purple-400'
              >
                Premium Support
              </Badge>
            </div>
          </div>
        </CardContent>
      </Card>
    </div>
  )
}
