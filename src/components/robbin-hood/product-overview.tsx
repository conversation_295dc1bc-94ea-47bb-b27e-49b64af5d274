"use client"

import { motion } from "framer-motion"
import { Info } from "lucide-react"

import Heading from "@/components/global/heading"
import { Card, CardContent } from "@/components/ui/card"

export function ProductOverview({ product }: { product: any }) {
  return (
    <div className='space-y-6'>
      <div className='flex items-center gap-2'>
        <Info className='h-6 w-6 text-primary' />
        <Heading className='text-start'>Product Overview</Heading>
      </div>

      <Card className='border-gray-800 bg-gray-900/50'>
        <CardContent className='p-6'>
          <motion.div
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            className='space-y-4'
          >
            <div className='grid gap-4 md:grid-cols-2'>
              <div>
                <h3 className='mb-2 text-lg font-semibold text-white'>
                  Description
                </h3>
                <p className='leading-relaxed text-gray-300'>
                  {product.description ||
                    "A comprehensive product designed for modern development needs."}
                </p>
              </div>
              <div>
                <h3 className='mb-2 text-lg font-semibold text-white'>
                  Creator
                </h3>
                <p className='text-gray-300'>{product.creator}</p>

                <h3 className='mb-2 mt-4 text-lg font-semibold text-white'>
                  Version
                </h3>
                <p className='text-gray-300'>{product.version || "1.0.0"}</p>
              </div>
            </div>
          </motion.div>
        </CardContent>
      </Card>
    </div>
  )
}
