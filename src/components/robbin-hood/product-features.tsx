"use client"

import { motion } from "framer-motion"
import {
  CheckCircle2,
  Code,
  Globe,
  Palette,
  Rocket,
  Settings,
  Shield,
  Smartphone,
  Star,
  Zap,
} from "lucide-react"

import Heading from "@/components/global/heading"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"
import { createArrayFromString } from "@/utils/generic"

// Icon mapping for different feature types
const getFeatureIcon = (feature: string, index: number) => {
  const lowerFeature = feature.toLowerCase()

  if (lowerFeature.includes("responsive") || lowerFeature.includes("mobile")) {
    return <Smartphone className='h-4 w-4 text-primary' />
  }
  if (lowerFeature.includes("security") || lowerFeature.includes("secure")) {
    return <Shield className='h-4 w-4 text-primary' />
  }
  if (lowerFeature.includes("performance") || lowerFeature.includes("fast")) {
    return <Rocket className='h-4 w-4 text-primary' />
  }
  if (lowerFeature.includes("design") || lowerFeature.includes("ui")) {
    return <Palette className='h-4 w-4 text-primary' />
  }
  if (lowerFeature.includes("code") || lowerFeature.includes("typescript")) {
    return <Code className='h-4 w-4 text-primary' />
  }
  if (lowerFeature.includes("seo") || lowerFeature.includes("optimization")) {
    return <Globe className='h-4 w-4 text-primary' />
  }

  // Default icons based on index
  const defaultIcons = [Settings, Zap, CheckCircle2, Star, Code, Palette]
  const IconComponent = defaultIcons[index % defaultIcons.length]
  return <IconComponent className='h-4 w-4 text-primary' />
}

export function ProductFeatures({ features }: { features: string }) {
  const featuresList = createArrayFromString(features)

  if (!featuresList || featuresList.length === 0) {
    return (
      <div className='space-y-6'>
        <div className='flex items-center gap-2'>
          <Zap className='h-6 w-6 text-primary' />
          <Heading className='text-start'>Core Features</Heading>
        </div>
        <Card className='border-gray-800 bg-gray-900/50'>
          <CardContent className='p-6 text-center text-gray-400'>
            No features available
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className='space-y-6'>
      <div className='flex items-center justify-between'>
        <div className='flex items-center gap-2'>
          <Zap className='h-6 w-6 text-primary' />
          <Heading className='text-start'>Core Features</Heading>
        </div>
        <Badge variant='secondary' className='text-xs'>
          {featuresList.length} Features
        </Badge>
      </div>

      <div className='space-y-3'>
        {featuresList.map((feature, index) => (
          <motion.div
            key={index}
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: index * 0.1 }}
          >
            <Card className='group border-gray-800 bg-gray-900/50 transition-all duration-300 hover:border-gray-700 hover:bg-gray-900/70'>
              <CardContent className='p-4'>
                <div className='flex items-start gap-3'>
                  <div className='flex h-8 w-8 shrink-0 items-center justify-center rounded-lg bg-primary/10 transition-colors group-hover:bg-primary/20'>
                    {getFeatureIcon(feature, index)}
                  </div>
                  <div className='min-w-0 flex-1'>
                    <p className='font-medium leading-relaxed text-white transition-colors group-hover:text-primary'>
                      {feature}
                    </p>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </div>

      {/* Feature Categories Summary */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: featuresList.length * 0.1 + 0.2 }}
        className='mt-8'
      >
        <Card className='border-gray-800 bg-gradient-to-r from-primary/5 to-secondary/5'>
          <CardContent className='p-6'>
            <div className='mb-4 flex items-center gap-2'>
              <Star className='h-5 w-5 text-primary' />
              <h3 className='text-lg font-semibold text-white'>
                Why Choose This Template?
              </h3>
            </div>
            <div className='grid grid-cols-1 gap-4 text-sm md:grid-cols-2'>
              <div className='flex items-center gap-2 text-gray-300'>
                <CheckCircle2 className='h-4 w-4 text-green-400' />
                <span>Production-ready code</span>
              </div>
              <div className='flex items-center gap-2 text-gray-300'>
                <CheckCircle2 className='h-4 w-4 text-green-400' />
                <span>Modern tech stack</span>
              </div>
              <div className='flex items-center gap-2 text-gray-300'>
                <CheckCircle2 className='h-4 w-4 text-green-400' />
                <span>Responsive design</span>
              </div>
              <div className='flex items-center gap-2 text-gray-300'>
                <CheckCircle2 className='h-4 w-4 text-green-400' />
                <span>Easy customization</span>
              </div>
            </div>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  )
}
