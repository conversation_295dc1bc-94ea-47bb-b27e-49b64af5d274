"use client"

import { motion, useScroll, useTransform } from "framer-motion"
import { BadgeCheck, DownloadIcon, EyeIcon, Heart } from "lucide-react"
import Image from "next/image"
import Link from "next/link"

import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { createArrayFromString } from "@/utils/generic"

export function ProductHeader({ product }: { product: any }) {
  const { scrollY } = useScroll()
  const opacity = useTransform(scrollY, [0, 100], [1, 0.8]) //
  const scale = useTransform(scrollY, [0, 100], [1, 0.95])

  return (
    <motion.div style={{ opacity, scale }} className='space-y-6'>
      {/* name desc */}
      <div className='max-w-4xl space-y-4'>
        <h1 className='bg-gradient-to-tr from-zinc-400/50 via-white to-white/60 bg-clip-text font-heading text-3xl font-bold !leading-snug text-transparent md:text-4xl lg:text-5xl'>
          {product.fullName}
        </h1>
        {/* <p className="text-muted-foreground">{product.description}</p> */}
        <div className='flex items-center justify-start gap-2'>
          {/* <h3 className="text-sm font-semibold text-muted-foreground">Tech Stack</h3> */}
          <Button size={"sm"} variant='white' className='text-sm'>
            Tech Stack :
          </Button>
          <div className='flex flex-wrap gap-2'>
            {product.techStack?.map((tech: string) => (
              <Badge key={tech} variant='secondary' className='bg-gray-800'>
                {tech}
              </Badge>
            ))}
          </div>
        </div>
      </div>

      {/*  */}
      <motion.div className='sticky top-72 flex flex-wrap items-center justify-between gap-4 border-y border-gray-800 py-4'>
        <div className='flex items-center gap-4'>
          <Image
            // src="/placeholder.svg?height=40&width=40"
            src={createArrayFromString(product.images)[0]}
            alt='Creator avatar'
            width={40}
            height={40}
            className='rounded-full'
          />
          <div>
            <div className='flex items-center gap-2'>
              <h2 className='text-xl font-semibold'>{product.name}</h2>
              <BadgeCheck className='h-5 w-5 text-green-400' />
            </div>
            <div className='text-sm text-gray-400'>
              <Link href='/creator' className='hover:text-white'>
                {product.creator}
              </Link>
            </div>
          </div>
        </div>
        <div className='flex items-center gap-4'>
          <div className='flex items-center gap-6'>
            <button className='flex items-center gap-2 text-gray-400 hover:text-white'>
              <Heart className='h-5 w-5' />
              <span>{product.likes}</span>
            </button>
            <button className='flex items-center gap-2 text-gray-400 hover:text-white'>
              <DownloadIcon className='h-5 w-5' />
              <span>{product.downloads}</span>
            </button>
          </div>
          <Button variant='outline'>
            <EyeIcon className='mr-1 h-4 w-4' />
            Preview
          </Button>
          <Button>
            <BadgeCheck className='mr-1 h-4 w-4' />
            Purchase ${product.price}
          </Button>
        </div>
      </motion.div>
    </motion.div>
  )
}
