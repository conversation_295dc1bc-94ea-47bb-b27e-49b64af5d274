"use client"

import { AnimatePresence, motion } from "framer-motion"
import {
  ChevronLeft,
  ChevronRight,
  Download,
  ExternalLink,
  Eye,
  Maximize2,
  Pause,
  Play,
  X,
  ZoomIn,
  ZoomOut,
} from "lucide-react"
import Image from "next/image"
import { useCallback, useEffect, useState } from "react"

import Heading from "@/components/global/heading"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import { cn } from "@/lib/utils"
import { createArrayFromString } from "@/utils/generic"

export function ProductPreview({ images }: { images: string }) {
  const [selectedImage, setSelectedImage] = useState(0)
  const [isFullscreen, setIsFullscreen] = useState(false)
  const [isAutoPlay, setIsAutoPlay] = useState(false)
  const [zoom, setZoom] = useState(1)
  const imagesList = createArrayFromString(images)

  const navigateImage = useCallback(
    (direction: "prev" | "next") => {
      if (!imagesList) return

      setSelectedImage((prev) => {
        if (direction === "next") {
          return (prev + 1) % imagesList.length
        } else {
          return prev === 0 ? imagesList.length - 1 : prev - 1
        }
      })
    },
    [imagesList],
  )

  // Auto-play functionality
  useEffect(() => {
    if (!isAutoPlay || !imagesList || imagesList.length <= 1) return

    const interval = setInterval(() => {
      setSelectedImage((prev) => (prev + 1) % imagesList.length)
    }, 3000)

    return () => clearInterval(interval)
  }, [isAutoPlay, imagesList])

  // Keyboard navigation
  useEffect(() => {
    const handleKeyPress = (e: KeyboardEvent) => {
      if (!isFullscreen) return

      switch (e.key) {
        case "ArrowLeft":
          navigateImage("prev")
          break
        case "ArrowRight":
          navigateImage("next")
          break
        case "Escape":
          setIsFullscreen(false)
          break
        case " ":
          e.preventDefault()
          setIsAutoPlay(!isAutoPlay)
          break
      }
    }

    window.addEventListener("keydown", handleKeyPress)
    return () => window.removeEventListener("keydown", handleKeyPress)
  }, [isFullscreen, isAutoPlay, navigateImage])

  const toggleFullscreen = () => {
    setIsFullscreen(!isFullscreen)
    setZoom(1)
  }

  const handleZoom = (type: "in" | "out") => {
    setZoom((prev) => {
      if (type === "in") return Math.min(prev + 0.25, 3)
      return Math.max(prev - 0.25, 0.5)
    })
  }

  if (!imagesList || imagesList.length === 0) {
    return (
      <div className='space-y-8'>
        <div className='flex items-center gap-2'>
          <Eye className='h-6 w-6 text-primary' />
          <Heading className='text-start'>Live Preview</Heading>
        </div>
        <Card className='border-gray-800 bg-gray-900/50'>
          <CardContent className='p-6'>
            <div className='flex h-64 items-center justify-center rounded-lg bg-gray-800 text-gray-400'>
              No preview images available
            </div>
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <>
      <div className='space-y-8'>
        <div className='flex items-center justify-between'>
          <div className='flex items-center gap-2'>
            <Eye className='h-6 w-6 text-primary' />
            <Heading className='text-start'>Live Preview</Heading>
          </div>
          <div className='flex items-center gap-2'>
            <Badge variant='secondary' className='text-xs'>
              {selectedImage + 1} / {imagesList.length}
            </Badge>
            {imagesList.length > 1 && (
              <Button
                size='sm'
                variant='outline'
                onClick={() => setIsAutoPlay(!isAutoPlay)}
                className='h-8'
              >
                {isAutoPlay ? (
                  <Pause className='h-4 w-4' />
                ) : (
                  <Play className='h-4 w-4' />
                )}
              </Button>
            )}
          </div>
        </div>

        {/* Main Preview */}
        <Card className='group overflow-hidden border-gray-800 bg-gray-900/50'>
          <CardContent className='p-0'>
            <div className='relative aspect-video bg-gray-800'>
              <AnimatePresence mode='wait'>
                <motion.div
                  key={selectedImage}
                  initial={{ opacity: 0, scale: 0.95 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 1.05 }}
                  transition={{ duration: 0.3 }}
                  className='absolute inset-0'
                >
                  <Image
                    src={imagesList[selectedImage]}
                    alt={`Preview ${selectedImage + 1}`}
                    fill
                    className='object-cover'
                    priority={selectedImage === 0}
                  />
                </motion.div>
              </AnimatePresence>

              {/* Overlay controls */}
              <div className='absolute inset-0 bg-gradient-to-t from-black/40 via-transparent to-black/20 opacity-0 transition-opacity duration-300 group-hover:opacity-100' />

              {/* Navigation arrows */}
              {imagesList.length > 1 && (
                <>
                  <Button
                    size='sm'
                    variant='secondary'
                    className='absolute left-4 top-1/2 -translate-y-1/2 opacity-0 backdrop-blur-sm transition-opacity duration-300 group-hover:opacity-100'
                    onClick={() => navigateImage("prev")}
                  >
                    <ChevronLeft className='h-4 w-4' />
                  </Button>
                  <Button
                    size='sm'
                    variant='secondary'
                    className='absolute right-4 top-1/2 -translate-y-1/2 opacity-0 backdrop-blur-sm transition-opacity duration-300 group-hover:opacity-100'
                    onClick={() => navigateImage("next")}
                  >
                    <ChevronRight className='h-4 w-4' />
                  </Button>
                </>
              )}

              {/* Top right controls */}
              <div className='absolute right-4 top-4 flex gap-2 opacity-0 transition-opacity duration-300 group-hover:opacity-100'>
                <Button
                  size='sm'
                  variant='secondary'
                  className='backdrop-blur-sm'
                  onClick={toggleFullscreen}
                >
                  <Maximize2 className='h-4 w-4' />
                </Button>
                <Button
                  size='sm'
                  variant='secondary'
                  className='backdrop-blur-sm'
                >
                  <ExternalLink className='h-4 w-4' />
                </Button>
              </div>

              {/* Progress indicator */}
              {isAutoPlay && imagesList.length > 1 && (
                <div className='absolute bottom-4 left-4 right-4'>
                  <div className='h-1 overflow-hidden rounded-full bg-black/30'>
                    <motion.div
                      className='h-full bg-primary'
                      initial={{ width: "0%" }}
                      animate={{ width: "100%" }}
                      transition={{ duration: 3, ease: "linear" }}
                      key={selectedImage}
                    />
                  </div>
                </div>
              )}
            </div>
          </CardContent>
        </Card>

        {/* Enhanced Thumbnail Gallery */}
        {imagesList.length > 1 && (
          <div className='space-y-6'>
            <div className='flex items-center justify-between'>
              <h3 className='text-lg font-semibold text-white'>Gallery</h3>
              <div className='flex gap-2'>
                <Button size='sm' variant='outline' className='h-8'>
                  <Download className='mr-2 h-4 w-4' />
                  Download All
                </Button>
              </div>
            </div>

            <motion.div
              className='grid grid-cols-2 gap-4 md:grid-cols-3 lg:grid-cols-4 xl:grid-cols-5'
              initial='hidden'
              animate='visible'
              variants={{
                hidden: { opacity: 0 },
                visible: {
                  opacity: 1,
                  transition: {
                    staggerChildren: 0.05,
                  },
                },
              }}
            >
              {imagesList.map((image, index) => (
                <motion.div
                  key={index}
                  variants={{
                    hidden: { opacity: 0, y: 20 },
                    visible: { opacity: 1, y: 0 },
                  }}
                  className='group cursor-pointer'
                  onClick={() => setSelectedImage(index)}
                >
                  <div
                    className={cn(
                      "relative aspect-video overflow-hidden rounded-lg border-2 transition-all duration-300",
                      selectedImage === index
                        ? "border-primary shadow-lg shadow-primary/20 ring-2 ring-primary/20"
                        : "border-gray-700 hover:border-gray-600",
                    )}
                  >
                    <Image
                      src={image}
                      alt={`Thumbnail ${index + 1}`}
                      fill
                      className='object-cover transition-all duration-300 group-hover:scale-110'
                    />
                    <div className='absolute inset-0 bg-black/0 transition-colors group-hover:bg-black/10' />

                    {/* Thumbnail overlay */}
                    <div className='absolute inset-0 flex items-center justify-center opacity-0 transition-opacity group-hover:opacity-100'>
                      <div className='rounded-full bg-black/50 p-2 backdrop-blur-sm'>
                        <Eye className='h-4 w-4 text-white' />
                      </div>
                    </div>

                    {/* Selected indicator */}
                    {selectedImage === index && (
                      <div className='absolute right-2 top-2'>
                        <div className='rounded-full bg-primary p-1'>
                          <div className='h-2 w-2 rounded-full bg-white' />
                        </div>
                      </div>
                    )}
                  </div>
                </motion.div>
              ))}
            </motion.div>
          </div>
        )}
      </div>

      {/* Fullscreen Modal */}
      <AnimatePresence>
        {isFullscreen && (
          <motion.div
            initial={{ opacity: 0 }}
            animate={{ opacity: 1 }}
            exit={{ opacity: 0 }}
            className='fixed inset-0 z-50 bg-black/95 backdrop-blur-sm'
            onClick={toggleFullscreen}
          >
            <div className='absolute inset-4 flex items-center justify-center'>
              <motion.div
                initial={{ scale: 0.8 }}
                animate={{ scale: 1 }}
                exit={{ scale: 0.8 }}
                className='relative max-h-full max-w-full'
                onClick={(e) => e.stopPropagation()}
                style={{ transform: `scale(${zoom})` }}
              >
                <Image
                  src={imagesList[selectedImage]}
                  alt={`Fullscreen Preview ${selectedImage + 1}`}
                  width={1200}
                  height={800}
                  className='max-h-full max-w-full object-contain'
                />
              </motion.div>
            </div>

            {/* Fullscreen controls */}
            <div className='absolute left-4 top-4 flex gap-2'>
              <Button
                size='sm'
                variant='secondary'
                onClick={toggleFullscreen}
                className='backdrop-blur-sm'
              >
                <X className='h-4 w-4' />
              </Button>
              <Button
                size='sm'
                variant='secondary'
                onClick={() => handleZoom("out")}
                className='backdrop-blur-sm'
              >
                <ZoomOut className='h-4 w-4' />
              </Button>
              <Button
                size='sm'
                variant='secondary'
                onClick={() => handleZoom("in")}
                className='backdrop-blur-sm'
              >
                <ZoomIn className='h-4 w-4' />
              </Button>
            </div>

            {/* Navigation in fullscreen */}
            {imagesList.length > 1 && (
              <>
                <Button
                  size='lg'
                  variant='secondary'
                  className='absolute left-8 top-1/2 -translate-y-1/2 backdrop-blur-sm'
                  onClick={(e) => {
                    e.stopPropagation()
                    navigateImage("prev")
                  }}
                >
                  <ChevronLeft className='h-6 w-6' />
                </Button>
                <Button
                  size='lg'
                  variant='secondary'
                  className='absolute right-8 top-1/2 -translate-y-1/2 backdrop-blur-sm'
                  onClick={(e) => {
                    e.stopPropagation()
                    navigateImage("next")
                  }}
                >
                  <ChevronRight className='h-6 w-6' />
                </Button>
              </>
            )}

            {/* Fullscreen info */}
            <div className='absolute bottom-4 left-1/2 -translate-x-1/2'>
              <Badge variant='secondary' className='backdrop-blur-sm'>
                {selectedImage + 1} / {imagesList.length} • Press ESC to close •
                Space to toggle autoplay
              </Badge>
            </div>
          </motion.div>
        )}
      </AnimatePresence>
    </>
  )
}
