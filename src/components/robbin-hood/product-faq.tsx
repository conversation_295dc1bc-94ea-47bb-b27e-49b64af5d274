import Heading from "@/components/global/heading"
import {
  Accordion,
  AccordionContent,
  AccordionItem,
  AccordionTrigger,
} from "@/components/ui/accordion"

const faqs = [
  {
    question: "What's included in the template?",
    answer:
      "The template includes the full source code, all necessary assets, and comprehensive documentation to help you set up and customize the template for your needs.",
  },
  {
    question: "Do I need to know Next.js to use this template?",
    answer:
      "While having experience with Next.js is beneficial, it's not strictly necessary. The template comes with clear documentation and is built using best practices, making it accessible even for those new to Next.js.",
  },
  {
    question: "Can I use this template for client projects?",
    answer:
      "Yes, you can use this template for both personal and client projects. However, you cannot resell the template as-is.",
  },
  {
    question: "Is support included with the purchase?",
    answer:
      "Yes, we offer 12 months of support with your purchase. This includes assistance with bug fixes and general questions about the template.",
  },
]

export function ProductFAQ() {
  return (
    <div className='space-y-6'>
      {/* <h2 className="text-2xl font-semibold">Frequently Asked Questions</h2> */}
      <Heading className='text-start'>Frequently Asked Questions</Heading>

      <Accordion type='single' collapsible className='w-full'>
        {faqs.map((faq, index) => (
          <AccordionItem key={index} value={`item-${index}`}>
            <AccordionTrigger>{faq.question}</AccordionTrigger>
            <AccordionContent>{faq.answer}</AccordionContent>
          </AccordionItem>
        ))}
      </Accordion>
    </div>
  )
}
