"use client"

import { motion } from "framer-motion"
import { BarChart3, Eye, Heart, MessageCircle, TrendingUp } from "lucide-react"

import Heading from "@/components/global/heading"
import { Card, CardContent } from "@/components/ui/card"

interface ProductStatsProps {
  product: {
    downloads: number
    likes: number
    views?: number
    comments?: number
  }
}

export function ProductStats({ product }: ProductStatsProps) {
  const stats = [
    {
      icon: BarChart3,
      label: "Total Downloads",
      value: product.downloads,
      change: "+12%",
      changeType: "positive" as const,
    },
    {
      icon: Heart,
      label: "Likes",
      value: product.likes,
      change: "+8%",
      changeType: "positive" as const,
    },
    {
      icon: Eye,
      label: "Views",
      value: product.views || product.downloads * 8,
      change: "+23%",
      changeType: "positive" as const,
    },
    {
      icon: MessageCircle,
      label: "Comments",
      value: product.comments || 0,
      change: "+5%",
      changeType: "positive" as const,
    },
  ]

  const formatNumber = (num: number) => {
    if (num >= 1000000) {
      return (num / 1000000).toFixed(1) + "M"
    }
    if (num >= 1000) {
      return (num / 1000).toFixed(1) + "K"
    }
    return num.toString()
  }

  return (
    <div className='space-y-6'>
      <div className='flex items-center gap-2'>
        <TrendingUp className='h-6 w-6 text-primary' />
        <Heading className='text-start'>Performance Analytics</Heading>
      </div>

      <div className='grid gap-4 md:grid-cols-2 lg:grid-cols-4'>
        {stats.map((stat, index) => (
          <motion.div
            key={stat.label}
            initial={{ opacity: 0, scale: 0.9 }}
            animate={{ opacity: 1, scale: 1 }}
            transition={{ delay: index * 0.1 }}
          >
            <Card className='border-gray-800 bg-gray-900/50 transition-colors hover:bg-gray-900/70'>
              <CardContent className='p-6'>
                <div className='flex items-center justify-between'>
                  <div className='flex items-center gap-2'>
                    <div className='flex h-8 w-8 items-center justify-center rounded-lg bg-primary/10'>
                      <stat.icon className='h-4 w-4 text-primary' />
                    </div>
                    <div>
                      <p className='text-xs text-gray-400'>{stat.label}</p>
                      <p className='text-lg font-bold text-white'>
                        {formatNumber(stat.value)}
                      </p>
                    </div>
                  </div>
                  <div className='text-right'>
                    <span
                      className={`text-xs font-medium ${
                        stat.changeType === "positive"
                          ? "text-green-400"
                          : "text-red-400"
                      }`}
                    >
                      {stat.change}
                    </span>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </div>
    </div>
  )
}
