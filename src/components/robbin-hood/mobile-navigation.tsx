"use client"

import { motion } from "framer-motion"
import { Menu } from "lucide-react"
import { useState } from "react"

import { But<PERSON> } from "@/components/ui/button"
import {
  <PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
  <PERSON><PERSON><PERSON><PERSON><PERSON>,
} from "@/components/ui/sheet"

const sections = [
  { id: "overview", label: "Overview" },
  { id: "stats", label: "Stats" },
  { id: "preview", label: "Preview" },
  { id: "features", label: "Features" },
  { id: "specs", label: "Specifications" },
  { id: "reviews", label: "Reviews" },
  { id: "faq", label: "FAQ" },
  { id: "pricing", label: "Pricing" },
  { id: "related", label: "Related" },
]

export function MobileNavigation() {
  const [isOpen, setIsOpen] = useState(false)

  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId)
    if (element) {
      element.scrollIntoView({ behavior: "smooth" })
      setIsOpen(false)
    }
  }

  return (
    <div className='fixed bottom-6 right-6 z-50 xl:hidden'>
      <Sheet open={isOpen} onOpenChange={setIsOpen}>
        <SheetTrigger asChild>
          <motion.div
            initial={{ scale: 0 }}
            animate={{ scale: 1 }}
            whileHover={{ scale: 1.1 }}
            whileTap={{ scale: 0.9 }}
          >
            <Button
              size='lg'
              className='h-14 w-14 rounded-full shadow-2xl shadow-primary/20'
            >
              <Menu className='h-6 w-6' />
            </Button>
          </motion.div>
        </SheetTrigger>
        <SheetContent side='bottom' className='border-gray-800 bg-gray-900'>
          <SheetHeader>
            <SheetTitle className='text-white'>Quick Navigation</SheetTitle>
          </SheetHeader>
          <div className='mt-6 grid grid-cols-3 gap-3'>
            {sections.map((section, index) => (
              <motion.button
                key={section.id}
                initial={{ opacity: 0, y: 20 }}
                animate={{ opacity: 1, y: 0 }}
                transition={{ delay: index * 0.05 }}
                onClick={() => scrollToSection(section.id)}
                className='rounded-lg bg-gray-800 px-4 py-3 text-sm text-white transition-colors hover:bg-gray-700'
              >
                {section.label}
              </motion.button>
            ))}
          </div>
        </SheetContent>
      </Sheet>
    </div>
  )
}
