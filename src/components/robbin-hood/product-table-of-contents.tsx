"use client"

import { motion } from "framer-motion"
import { <PERSON><PERSON>pen, ChevronRight } from "lucide-react"
import { useEffect, useState } from "react"

import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"

const sections = [
  { id: "overview", label: "Overview", offset: 0 },
  { id: "stats", label: "Performance Stats", offset: 200 },
  { id: "preview", label: "Live Preview", offset: 400 },
  { id: "features", label: "Features & Highlights", offset: 600 },
  { id: "specs", label: "Specifications", offset: 800 },
  { id: "reviews", label: "Reviews", offset: 1000 },
  { id: "faq", label: "FAQ", offset: 1200 },
  { id: "pricing", label: "Pricing", offset: 1400 },
  { id: "related", label: "Related Products", offset: 1600 },
]

export function ProductTableOfContents() {
  const [activeSection, setActiveSection] = useState("overview")

  useEffect(() => {
    const handleScroll = () => {
      const scrollPosition = window.scrollY + 100

      for (let i = sections.length - 1; i >= 0; i--) {
        if (scrollPosition >= sections[i].offset) {
          setActiveSection(sections[i].id)
          break
        }
      }
    }

    window.addEventListener("scroll", handleScroll)
    return () => window.removeEventListener("scroll", handleScroll)
  }, [])

  const scrollToSection = (offset: number) => {
    window.scrollTo({
      top: offset,
      behavior: "smooth",
    })
  }

  return (
    <Card className='sticky top-8 border-gray-800 bg-gray-900/50'>
      <CardHeader className='pb-3'>
        <CardTitle className='flex items-center gap-2 text-lg'>
          <BookOpen className='h-5 w-5 text-primary' />
          Table of Contents
        </CardTitle>
      </CardHeader>
      <CardContent className='space-y-1'>
        {sections.map((section, index) => (
          <motion.button
            key={section.id}
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: index * 0.05 }}
            onClick={() => scrollToSection(section.offset)}
            className={`group flex w-full items-center justify-between rounded-lg px-3 py-2 text-left text-sm transition-all duration-200 ${
              activeSection === section.id
                ? "bg-primary/10 text-primary"
                : "text-gray-400 hover:bg-gray-800/50 hover:text-white"
            }`}
          >
            <span>{section.label}</span>
            <ChevronRight
              className={`h-3 w-3 transition-transform duration-200 ${
                activeSection === section.id
                  ? "translate-x-1 text-primary"
                  : "text-gray-600 group-hover:translate-x-1 group-hover:text-gray-400"
              }`}
            />
          </motion.button>
        ))}
      </CardContent>
    </Card>
  )
}
