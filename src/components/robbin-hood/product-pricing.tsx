import Heading from "@/components/global/heading"
import { Button } from "@/components/ui/button"
import { Card } from "@/components/ui/card"

export function ProductPricing({ price }: { price: number }) {
  return (
    <div className='space-y-6'>
      {/* <h2 className="text-2xl font-semibold">Pricing</h2> */}
      <Heading className='text-start'>Pricing</Heading>

      <Card className='border-card bg-card'>
        <div className='space-y-6 p-6'>
          <div className='flex items-baseline justify-center gap-2'>
            <span className='text-5xl font-extrabold'>${price}</span>
            <span className='text-gray-400'>one-time payment</span>
          </div>
          <ul className='space-y-2 text-gray-400'>
            <li className='flex items-center gap-2'>
              <svg
                className='h-5 w-5 text-green-500'
                fill='none'
                stroke='currentColor'
                viewBox='0 0 24 24'
                xmlns='http://www.w3.org/2000/svg'
              >
                <path
                  strokeLinecap='round'
                  strokeLinejoin='round'
                  strokeWidth='2'
                  d='M5 13l4 4L19 7'
                />
              </svg>
              Full source code
            </li>
            <li className='flex items-center gap-2'>
              <svg
                className='h-5 w-5 text-green-500'
                fill='none'
                stroke='currentColor'
                viewBox='0 0 24 24'
                xmlns='http://www.w3.org/2000/svg'
              >
                <path
                  strokeLinecap='round'
                  strokeLinejoin='round'
                  strokeWidth='2'
                  d='M5 13l4 4L19 7'
                />
              </svg>
              12 months of support
            </li>
            <li className='flex items-center gap-2'>
              <svg
                className='h-5 w-5 text-green-500'
                fill='none'
                stroke='currentColor'
                viewBox='0 0 24 24'
                xmlns='http://www.w3.org/2000/svg'
              >
                <path
                  strokeLinecap='round'
                  strokeLinejoin='round'
                  strokeWidth='2'
                  d='M5 13l4 4L19 7'
                />
              </svg>
              Free updates
            </li>
          </ul>
          <Button className='w-full'>Purchase Now</Button>
        </div>
      </Card>
    </div>
  )
}
