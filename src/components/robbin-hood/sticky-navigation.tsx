"use client"

import { motion, useScroll, useTransform } from "framer-motion"
import { ChevronLeft, ChevronRight } from "lucide-react"
import { useEffect, useRef, useState } from "react"

import { But<PERSON> } from "@/components/ui/button"
import { cn } from "@/lib/utils"

const sections = [
  { id: "overview", label: "Overview" },
  { id: "preview", label: "Live Preview" },
  { id: "features", label: "Features & Highlights" },
  { id: "reviews", label: "Reviews" },
  { id: "faq", label: "FAQ" },
  { id: "pricing", label: "Pricing" },
]

export function StickyNavigation() {
  const [activeSection, setActiveSection] = useState("overview")
  const [isVisible, setIsVisible] = useState(false)
  const scrollContainerRef = useRef<HTMLDivElement>(null)
  const { scrollY } = useScroll()
  const opacity = useTransform(scrollY, [300, 400], [0, 1])
  const y = useTransform(scrollY, [300, 400], [-20, 0])

  useEffect(() => {
    const handleScroll = () => {
      const scrollPosition = window.scrollY + 150
      // setIsVisible(window.scrollY > 300)

      // Find the current active section
      for (let i = sections.length - 1; i >= 0; i--) {
        const element = document.getElementById(sections[i].id)
        if (element) {
          const elementTop = element.offsetTop
          if (scrollPosition >= elementTop) {
            setActiveSection(sections[i].id)
            break
          }
        }
      }
    }

    window.addEventListener("scroll", handleScroll)
    return () => window.removeEventListener("scroll", handleScroll)
  }, [])

  const scrollToSection = (sectionId: string) => {
    const element = document.getElementById(sectionId)
    if (element) {
      const offsetTop = element.offsetTop - 120
      window.scrollTo({
        top: offsetTop,
        behavior: "smooth",
      })
    }
  }

  const scrollNavigation = (direction: "left" | "right") => {
    if (scrollContainerRef.current) {
      const scrollAmount = 200
      scrollContainerRef.current.scrollBy({
        left: direction === "left" ? -scrollAmount : scrollAmount,
        behavior: "smooth",
      })
    }
  }

  // if (!isVisible) return null

  return (
    <motion.div
      style={{ opacity, y }}
      className={cn(
        "sticky left-0 right-0 top-14 z-40 -translate-y-10 border-b border-gray-800 bg-black/80 backdrop-blur-lg",
      )}
    >
      <div className='mx-auto max-w-7xl px-4'>
        <div className='flex items-center gap-2 py-3'>
          {/* Left scroll button */}
          <Button
            variant='ghost'
            size='sm'
            className='h-8 w-8 shrink-0 p-0 hover:bg-gray-800'
            onClick={() => scrollNavigation("left")}
          >
            <ChevronLeft className='h-4 w-4' />
          </Button>

          {/* Navigation items */}
          <div
            ref={scrollContainerRef}
            className='flex flex-1 items-center gap-1 overflow-x-auto scrollbar-hide'
            style={{ scrollbarWidth: "none", msOverflowStyle: "none" }}
          >
            {sections.map((section, index) => (
              <motion.button
                key={section.id}
                initial={{ opacity: 0, scale: 0.8 }}
                animate={{ opacity: 1, scale: 1 }}
                transition={{ delay: index * 0.05 }}
                onClick={() => scrollToSection(section.id)}
                className={cn(
                  "shrink-0 whitespace-nowrap rounded-full px-4 py-2 text-sm font-medium transition-all duration-200",
                  activeSection === section.id
                    ? "bg-primary text-primary-foreground shadow-lg shadow-primary/20"
                    : "text-gray-400 hover:bg-gray-800/50 hover:text-white",
                )}
              >
                {section.label}
              </motion.button>
            ))}
          </div>

          {/* Right scroll button */}
          <Button
            variant='ghost'
            size='sm'
            className='h-8 w-8 shrink-0 p-0 hover:bg-gray-800'
            onClick={() => scrollNavigation("right")}
          >
            <ChevronRight className='h-4 w-4' />
          </Button>
        </div>
      </div>
    </motion.div>
  )
}
