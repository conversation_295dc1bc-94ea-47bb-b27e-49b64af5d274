"use client"

import { motion } from "framer-motion"
import { <PERSON>R<PERSON>, ExternalLink, Star } from "lucide-react"
import Image from "next/image"
import Link from "next/link"

import Heading from "@/components/global/heading"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"

// Mock data for related products - in real app, this would come from an API
const relatedProducts = [
  {
    id: "digital-agency",
    name: "Digital Agency Pro",
    description: "Modern digital agency template with advanced animations",
    price: 49,
    image: "/placeholder.svg?height=200&width=300",
    rating: 4.8,
    downloads: 856,
    techStack: ["Next.js", "Tailwind", "Framer Motion"],
  },
  {
    id: "portfolio-studio",
    name: "Portfolio Studio",
    description: "Creative portfolio template for designers and developers",
    price: 29,
    image: "/placeholder.svg?height=200&width=300",
    rating: 4.9,
    downloads: 1203,
    techStack: ["React", "Styled Components", "GSAP"],
  },
  {
    id: "saas-landing",
    name: "SaaS Landing Kit",
    description: "Complete SaaS landing page with conversion optimization",
    price: 39,
    image: "/placeholder.svg?height=200&width=300",
    rating: 4.7,
    downloads: 742,
    techStack: ["Next.js", "Tailwind", "TypeScript"],
  },
]

export function RelatedProducts() {
  return (
    <div className='space-y-8'>
      <div className='flex items-center justify-between'>
        <Heading className='text-start'>Related Products</Heading>
        <Button variant='outline' className='group'>
          View All
          <ArrowRight className='ml-2 h-4 w-4 transition-transform group-hover:translate-x-1' />
        </Button>
      </div>

      <div className='grid gap-6 md:grid-cols-2 lg:grid-cols-3'>
        {relatedProducts.map((product, index) => (
          <motion.div
            key={product.id}
            initial={{ opacity: 0, y: 20 }}
            animate={{ opacity: 1, y: 0 }}
            transition={{ delay: index * 0.1 }}
          >
            <Card className='group border-gray-800 bg-gray-900/50 transition-all duration-300 hover:-translate-y-1 hover:bg-gray-900/70'>
              <CardContent className='p-0'>
                <div className='relative overflow-hidden rounded-t-lg'>
                  <Image
                    src={product.image}
                    alt={product.name}
                    width={300}
                    height={200}
                    className='h-48 w-full object-cover transition-transform duration-300 group-hover:scale-105'
                  />
                  <div className='absolute inset-0 bg-gradient-to-t from-black/60 via-transparent to-transparent' />
                  <div className='absolute bottom-3 left-3 right-3'>
                    <div className='flex flex-wrap gap-1'>
                      {product.techStack.slice(0, 2).map((tech) => (
                        <Badge
                          key={tech}
                          variant='secondary'
                          className='bg-black/50 text-xs backdrop-blur-sm'
                        >
                          {tech}
                        </Badge>
                      ))}
                      {product.techStack.length > 2 && (
                        <Badge
                          variant='secondary'
                          className='bg-black/50 text-xs backdrop-blur-sm'
                        >
                          +{product.techStack.length - 2}
                        </Badge>
                      )}
                    </div>
                  </div>
                </div>

                <div className='space-y-4 p-6'>
                  <div>
                    <h3 className='text-lg font-semibold text-white transition-colors group-hover:text-primary'>
                      {product.name}
                    </h3>
                    <p className='mt-1 text-sm text-gray-400'>
                      {product.description}
                    </p>
                  </div>

                  <div className='flex items-center gap-2'>
                    <div className='flex items-center gap-1'>
                      <Star className='h-4 w-4 fill-current text-yellow-500' />
                      <span className='text-sm font-medium text-white'>
                        {product.rating}
                      </span>
                    </div>
                    <span className='text-gray-400'>•</span>
                    <span className='text-sm text-gray-400'>
                      {product.downloads} downloads
                    </span>
                  </div>

                  <div className='flex items-center justify-between pt-2'>
                    <div className='text-2xl font-bold text-white'>
                      ${product.price}
                    </div>
                    <div className='flex gap-2'>
                      <Button
                        size='sm'
                        variant='outline'
                        className='opacity-0 transition-opacity group-hover:opacity-100'
                      >
                        <ExternalLink className='h-4 w-4' />
                      </Button>
                      <Button
                        size='sm'
                        className='opacity-0 transition-opacity group-hover:opacity-100'
                        asChild
                      >
                        <Link href={`/templates/${product.id}`}>
                          View Details
                        </Link>
                      </Button>
                    </div>
                  </div>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </div>
    </div>
  )
}
