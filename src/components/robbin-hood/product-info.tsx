import { Clock, User, FileText, Eye, Github, EyeIcon } from "lucide-react"
import Link from "next/link"

import { Badge } from "@/components/ui/badge"
import { But<PERSON> } from "@/components/ui/button"
import { Product } from "@/types/product"

export function ProductInfo({ product }: { product: Product }) {
  return (
    <div className='space-y-8'>
      <div className='flex flex-wrap gap-8'>
        <div className='flex-1 space-y-2'>
          <h3 className='text-sm font-medium text-gray-400'>Tech Stack</h3>
          <div className='flex flex-wrap gap-2'>
            {product.techStack?.map((tech: string) => (
              <Badge key={tech} variant='secondary' className='bg-gray-800'>
                {tech}
              </Badge>
            ))}
          </div>
        </div>
        {/* <div className="space-y-2">
          <h3 className="text-sm font-medium text-gray-400">Links</h3>
          <div className="flex gap-4">
            {!product.isPaid && product?.githubUrl !== "" && (
              <Button size={'sm'} variant={'tertiary'} asChild>
                <Link
                  href={product?.githubUrl ?? "/"}
                  className="flex items-center gap-2 text-gray-400 hover:text-white"
                >
                  <Github className="w-4 h-4" />
                  <span>Source Code</span>
                </Link>
              </Button>
            )}
            <Button size={'sm'} variant={"subtitle"} asChild>
              <Link
                href={product?.previewUrl ?? "/"}
              >
                <EyeIcon className="w-4 h-4 mr-1" />
                Live Preview
              </Link>
            </Button>
          </div>
        </div> */}
      </div>
      {/* <div className="grid grid-cols-2 md:grid-cols-4 gap-6">
        <div className="space-y-1">
          <div className="flex items-center gap-2 text-gray-400">
            <User className="w-4 h-4" />
            <span>Creator</span>
          </div>
          <p className="font-medium">{product.creator}</p>
        </div>
        <div className="space-y-1">
          <div className="flex items-center gap-2 text-gray-400">
            <Clock className="w-4 h-4" />
            <span>Last Updated</span>
          </div>
          <p className="font-medium">{product.updatedAt}</p>
        </div>
        <div className="space-y-1">
          <div className="flex items-center gap-2 text-gray-400">
            <FileText className="w-4 h-4" />
            <span>likes</span>
          </div>
          <p className="font-medium">{product.likes}</p>
        </div>
        <div className="space-y-1">
          <div className="flex items-center gap-2 text-gray-400">
            <Eye className="w-4 h-4" />
            <span>Rating</span>
          </div>
          <p className="font-medium">{product?.rating}</p>
        </div>
      </div> */}
    </div>
  )
}
