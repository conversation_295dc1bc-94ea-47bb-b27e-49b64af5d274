"use client"

import { motion } from "framer-motion"
import {
  Award,
  CheckCircle,
  Rocket,
  Shield,
  Sparkles,
  Star,
  Trophy,
  Zap,
} from "lucide-react"

import Heading from "@/components/global/heading"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"
import { createArrayFromString } from "@/utils/generic"

// Enhanced icon mapping for highlights
const getHighlightIcon = (highlight: string, index: number) => {
  const lowerHighlight = highlight.toLowerCase()

  if (
    lowerHighlight.includes("performance") ||
    lowerHighlight.includes("fast")
  ) {
    return <Rocket className='h-4 w-4 text-amber-400' />
  }
  if (lowerHighlight.includes("quality") || lowerHighlight.includes("best")) {
    return <Trophy className='h-4 w-4 text-yellow-400' />
  }
  if (lowerHighlight.includes("secure") || lowerHighlight.includes("safety")) {
    return <Shield className='h-4 w-4 text-blue-400' />
  }
  if (
    lowerHighlight.includes("optimized") ||
    lowerHighlight.includes("efficient")
  ) {
    return <Zap className='h-4 w-4 text-purple-400' />
  }
  if (
    lowerHighlight.includes("premium") ||
    lowerHighlight.includes("professional")
  ) {
    return <Award className='h-4 w-4 text-indigo-400' />
  }
  if (lowerHighlight.includes("rated") || lowerHighlight.includes("popular")) {
    return <Star className='h-4 w-4 text-yellow-400' />
  }

  return <CheckCircle className='h-4 w-4 text-green-400' />
}

export function ProductHighlights({ highlights }: { highlights: string }) {
  const highlightsList = createArrayFromString(highlights)

  if (!highlightsList || highlightsList.length === 0) {
    return (
      <div className='space-y-6'>
        <div className='flex items-center gap-2'>
          <Sparkles className='h-6 w-6 text-primary' />
          <Heading className='text-start'>Key Highlights</Heading>
        </div>
        <Card className='border-gray-800 bg-gray-900/50'>
          <CardContent className='p-6 text-center text-gray-400'>
            No highlights available
          </CardContent>
        </Card>
      </div>
    )
  }

  return (
    <div className='space-y-6'>
      <div className='flex items-center justify-between'>
        <div className='flex items-center gap-2'>
          <Sparkles className='h-6 w-6 text-primary' />
          <Heading className='text-start'>Key Highlights</Heading>
        </div>
        <Badge variant='secondary' className='text-xs'>
          {highlightsList.length} Highlights
        </Badge>
      </div>

      <div className='space-y-3'>
        {highlightsList.map((highlight, index) => (
          <motion.div
            key={index}
            initial={{ opacity: 0, x: -20 }}
            animate={{ opacity: 1, x: 0 }}
            transition={{ delay: index * 0.1 }}
          >
            <Card className='group border-gray-800 bg-gray-900/50 transition-all duration-300 hover:border-gray-700 hover:bg-gray-900/70'>
              <CardContent className='p-4'>
                <div className='flex items-start gap-3'>
                  <div className='mt-0.5 flex h-8 w-8 shrink-0 items-center justify-center rounded-full bg-gradient-to-br from-primary/20 to-secondary/20 transition-all group-hover:scale-110'>
                    {getHighlightIcon(highlight, index)}
                  </div>
                  <p className='leading-relaxed text-gray-300 transition-colors group-hover:text-white'>
                    {highlight}
                  </p>
                </div>
              </CardContent>
            </Card>
          </motion.div>
        ))}
      </div>

      {/* Highlight Summary */}
      <motion.div
        initial={{ opacity: 0, y: 20 }}
        animate={{ opacity: 1, y: 0 }}
        transition={{ delay: highlightsList.length * 0.1 + 0.2 }}
        className='mt-8'
      >
        <Card className='border-gray-800 bg-gradient-to-r from-green-500/5 to-blue-500/5'>
          <CardContent className='p-6'>
            <div className='mb-3 flex items-center gap-2'>
              <Trophy className='h-5 w-5 text-green-400' />
              <h3 className='text-lg font-semibold text-white'>
                What Makes This Special
              </h3>
            </div>
            <p className='text-sm leading-relaxed text-gray-300'>
              This template combines cutting-edge technology with proven design
              patterns, ensuring you get a premium product that delivers
              exceptional results from day one.
            </p>
          </CardContent>
        </Card>
      </motion.div>
    </div>
  )
}
