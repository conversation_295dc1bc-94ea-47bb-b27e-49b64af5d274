"use client"

import { motion } from "framer-motion"
import { ArrowLeftIcon, EyeIcon, EyeOffIcon, MailIcon } from "lucide-react"
import Link from "next/link"
import { useRouter, useSearchParams } from "next/navigation"
import React, { useEffect, useState } from "react"
import { toast } from "sonner"

import { FADE_IN_VARIANTS } from "@/constants"
import { useAuth } from "@/contexts/auth-context"

import Icons from "../global/icons"
import { Button } from "../ui/button"
import { Input } from "../ui/input"
import LoadingIcon from "../ui/loading-icon"

const SignUpForm = () => {
  const router = useRouter()
  const searchParams = useSearchParams()
  const { register, googleLogin } = useAuth()
  
  const [email, setEmail] = useState("")
  const [username, setUsername] = useState("")
  const [password, setPassword] = useState("")
  const [confirmPassword, setConfirmPassword] = useState("")
  const [showPassword, setShowPassword] = useState(false)
  const [showConfirmPassword, setShowConfirmPassword] = useState(false)
  const [isEmailOpen, setIsEmailOpen] = useState(false)
  const [isEmailLoading, setIsEmailLoading] = useState(false)
  const [isRegisterLoading, setIsRegisterLoading] = useState(false)
  const [isGoogleLoading, setIsGoogleLoading] = useState(false)

  useEffect(() => {
    const from = searchParams.get("from")
    if (from === "signin") {
      setIsEmailOpen(true)
    }
  }, [searchParams])

  const handleGoogleAuth = async () => {
    setIsGoogleLoading(true)
    try {
      googleLogin()
      toast.loading("Redirecting to Google...")
    } catch (error) {
      console.error(error)
      toast.error("An error occurred. Please try again.")
      setIsGoogleLoading(false)
    }
  }

  const handleEmail = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()

    if (!email) {
      toast.error("Please enter your email address")
      return
    }

    // Basic email validation
    const emailRegex = /^[^\s@]+@[^\s@]+\.[^\s@]+$/
    if (!emailRegex.test(email)) {
      toast.error("Please enter a valid email address")
      return
    }

    setIsEmailLoading(true)
    setIsEmailOpen(true)
    setIsEmailLoading(false)
  }

  const handleRegister = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()

    if (!email || !username || !password || !confirmPassword) {
      toast.error("Please fill in all fields")
      return
    }

    if (password !== confirmPassword) {
      toast.error("Passwords do not match")
      return
    }

    if (password.length < 6) {
      toast.error("Password must be at least 6 characters long")
      return
    }

    setIsRegisterLoading(true)

    try {
      const result = await register(email, username, password)
      
      if (result.success) {
        toast.success(result.message || "Registration successful! Please check your email to verify your account.")
        router.push("/auth/signin")
      } else {
        toast.error(result.message || "Registration failed. Please try again.")
      }
    } catch (error) {
      console.error("Registration error:", error)
      toast.error("An error occurred. Please try again.")
    } finally {
      setIsRegisterLoading(false)
    }
  }

  const handleBackToEmail = () => {
    setIsEmailOpen(false)
    setEmail("")
    setUsername("")
    setPassword("")
    setConfirmPassword("")
  }

  return (
    <div className='flex w-full flex-col text-center'>
      <motion.div
        variants={FADE_IN_VARIANTS}
        animate='visible'
        initial='hidden'
      >
        <div className='flex justify-center'>
          <Link href='/'>
            <Icons.icon className='h-8 w-8' />
          </Link>
        </div>
        <h1 className='mt-4 text-center text-2xl'>
          {isEmailOpen ? "Create your account" : "Join DesignByte"}
        </h1>
        <p className='mt-2 text-sm text-muted-foreground'>
          {isEmailOpen
            ? "Fill in your details to create your account"
            : "Enter your email address to get started"}
        </p>
      </motion.div>

      {isEmailOpen ? (
        <div>
          <motion.div
            variants={FADE_IN_VARIANTS}
            animate='visible'
            initial='hidden'
            className='flex flex-col gap-4 py-8'
          >
            <Button
              variant='outline'
              onClick={handleBackToEmail}
              className='flex items-center gap-2'
            >
              <ArrowLeftIcon className='h-4 w-4' />
              Back
            </Button>

            <form onSubmit={handleRegister} className='space-y-4'>
              <div className='space-y-2'>
                <Input
                  type='email'
                  placeholder='Email'
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  disabled
                  className='bg-muted'
                />
                <Input
                  type='text'
                  placeholder='Username'
                  value={username}
                  onChange={(e) => setUsername(e.target.value)}
                />
                <div className='relative'>
                  <Input
                    type={showPassword ? 'text' : 'password'}
                    placeholder='Password'
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    className='pr-10'
                  />
                  <button
                    type='button'
                    onClick={() => setShowPassword(!showPassword)}
                    className='absolute right-3 top-1/2 -translate-y-1/2 text-muted-foreground hover:text-foreground'
                  >
                    {showPassword ? (
                      <EyeOffIcon className='h-4 w-4' />
                    ) : (
                      <EyeIcon className='h-4 w-4' />
                    )}
                  </button>
                </div>
                <div className='relative'>
                  <Input
                    type={showConfirmPassword ? 'text' : 'password'}
                    placeholder='Confirm Password'
                    value={confirmPassword}
                    onChange={(e) => setConfirmPassword(e.target.value)}
                    className='pr-10'
                  />
                  <button
                    type='button'
                    onClick={() => setShowConfirmPassword(!showConfirmPassword)}
                    className='absolute right-3 top-1/2 -translate-y-1/2 text-muted-foreground hover:text-foreground'
                  >
                    {showConfirmPassword ? (
                      <EyeOffIcon className='h-4 w-4' />
                    ) : (
                      <EyeIcon className='h-4 w-4' />
                    )}
                  </button>
                </div>
              </div>

              <Button
                type='submit'
                className='w-full'
                disabled={isRegisterLoading}
              >
                {isRegisterLoading ? (
                  <LoadingIcon className='mr-2 h-4 w-4' />
                ) : null}
                Create Account
              </Button>
            </form>

            <div className='relative'>
              <div className='absolute inset-0 flex items-center'>
                <span className='w-full border-t' />
              </div>
              <div className='relative flex justify-center text-xs uppercase'>
                <span className='bg-background px-2 text-muted-foreground'>
                  Or continue with
                </span>
              </div>
            </div>

            <Button
              variant='outline'
              onClick={handleGoogleAuth}
              disabled={isGoogleLoading}
              className='w-full'
            >
              {isGoogleLoading ? (
                <LoadingIcon className='mr-2 h-4 w-4' />
              ) : (
                <Icons.google className='mr-2 h-4 w-4' />
              )}
              Google
            </Button>
          </motion.div>
        </div>
      ) : (
        <div>
          <motion.div
            variants={FADE_IN_VARIANTS}
            animate='visible'
            initial='hidden'
            className='flex flex-col gap-4 py-8'
          >
            <form onSubmit={handleEmail} className='space-y-4'>
              <Input
                type='email'
                placeholder='Enter your email'
                value={email}
                onChange={(e) => setEmail(e.target.value)}
              />
              <Button
                type='submit'
                className='w-full'
                disabled={isEmailLoading}
              >
                {isEmailLoading ? (
                  <LoadingIcon className='mr-2 h-4 w-4' />
                ) : (
                  <MailIcon className='mr-2 h-4 w-4' />
                )}
                Continue with Email
              </Button>
            </form>

            <div className='relative'>
              <div className='absolute inset-0 flex items-center'>
                <span className='w-full border-t' />
              </div>
              <div className='relative flex justify-center text-xs uppercase'>
                <span className='bg-background px-2 text-muted-foreground'>
                  Or continue with
                </span>
              </div>
            </div>

            <Button
              variant='outline'
              onClick={handleGoogleAuth}
              disabled={isGoogleLoading}
              className='w-full'
            >
              {isGoogleLoading ? (
                <LoadingIcon className='mr-2 h-4 w-4' />
              ) : (
                <Icons.google className='mr-2 h-4 w-4' />
              )}
              Google
            </Button>
          </motion.div>

          <motion.div
            variants={FADE_IN_VARIANTS}
            animate='visible'
            initial='hidden'
            className='mt-4'
          >
            <p className='text-sm text-muted-foreground'>
              Already have an account?{" "}
              <Link
                href='/auth/signin'
                className='font-medium text-primary hover:underline'
              >
                Sign in
              </Link>
            </p>
          </motion.div>
        </div>
      )}
    </div>
  )
}

export default SignUpForm
