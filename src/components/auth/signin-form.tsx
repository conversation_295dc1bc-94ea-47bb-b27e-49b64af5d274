"use client"

import { motion } from "framer-motion"
import { ArrowLeftIcon, EyeIcon, EyeOffIcon, MailIcon } from "lucide-react"
import Link from "next/link"
import { useRouter, useSearchParams } from "next/navigation"
import React, { useEffect, useState } from "react"
import { toast } from "sonner"

import { FADE_IN_VARIANTS } from "@/constants"
import { useAuth } from "@/contexts/auth-context"

import Icons from "../global/icons"
import { Button } from "../ui/button"
import { Input } from "../ui/input"
import LoadingIcon from "../ui/loading-icon"

const SignInForm = () => {
  const router = useRouter()
  const searchParams = useSearchParams()
  const { login, googleLogin } = useAuth()
  
  const [email, setEmail] = useState("")
  const [password, setPassword] = useState("")
  const [showPassword, setShowPassword] = useState(false)
  const [isEmailO<PERSON>, setIsEmailOpen] = useState(false)
  const [isEmailLoading, setIsEmailLoading] = useState(false)
  const [isPasswordLoading, setIsPasswordLoading] = useState(false)
  const [isGoogleLoading, setIsGoogleLoading] = useState(false)

  useEffect(() => {
    const from = searchParams.get("from")
    if (from === "signup") {
      setIsEmailOpen(true)
    }
  }, [searchParams])

  const handleGoogleAuth = async () => {
    setIsGoogleLoading(true)
    try {
      googleLogin()
      toast.loading("Redirecting to Google...")
    } catch (error) {
      console.error(error)
      toast.error("An error occurred. Please try again.")
      setIsGoogleLoading(false)
    }
  }

  const handleEmail = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()

    if (!email) {
      toast.error("Please enter your email address")
      return
    }

    setIsEmailLoading(true)
    setIsEmailOpen(true)
    setIsEmailLoading(false)
  }

  const handlePasswordLogin = async (e: React.FormEvent<HTMLFormElement>) => {
    e.preventDefault()

    if (!email || !password) {
      toast.error("Please enter both email and password")
      return
    }

    setIsPasswordLoading(true)

    try {
      const result = await login(email, password)
      
      if (result.success) {
        toast.success("Login successful!")
        router.push("/")
      } else {
        toast.error(result.message || "Login failed. Please try again.")
      }
    } catch (error) {
      console.error("Login error:", error)
      toast.error("An error occurred. Please try again.")
    } finally {
      setIsPasswordLoading(false)
    }
  }

  const handleBackToEmail = () => {
    setIsEmailOpen(false)
    setEmail("")
    setPassword("")
  }

  return (
    <div className='flex w-full flex-col text-center'>
      <motion.div
        variants={FADE_IN_VARIANTS}
        animate='visible'
        initial='hidden'
      >
        <div className='flex justify-center'>
          <Link href='/'>
            <Icons.icon className='h-8 w-8' />
          </Link>
        </div>
        <h1 className='mt-4 text-center text-2xl'>
          {isEmailOpen ? "Login to DesignByte" : "Welcome to DesignByte"}
        </h1>
        <p className='mt-2 text-sm text-muted-foreground'>
          {isEmailOpen
            ? "Enter your password to continue"
            : "Enter your email address to get started"}
        </p>
      </motion.div>

      {isEmailOpen ? (
        <div>
          <motion.div
            variants={FADE_IN_VARIANTS}
            animate='visible'
            initial='hidden'
            className='flex flex-col gap-4 py-8'
          >
            <Button
              variant='outline'
              onClick={handleBackToEmail}
              className='flex items-center gap-2'
            >
              <ArrowLeftIcon className='h-4 w-4' />
              Back
            </Button>

            <form onSubmit={handlePasswordLogin} className='space-y-4'>
              <div className='space-y-2'>
                <Input
                  type='email'
                  placeholder='Email'
                  value={email}
                  onChange={(e) => setEmail(e.target.value)}
                  disabled
                  className='bg-muted'
                />
                <div className='relative'>
                  <Input
                    type={showPassword ? 'text' : 'password'}
                    placeholder='Password'
                    value={password}
                    onChange={(e) => setPassword(e.target.value)}
                    className='pr-10'
                  />
                  <button
                    type='button'
                    onClick={() => setShowPassword(!showPassword)}
                    className='absolute right-3 top-1/2 -translate-y-1/2 text-muted-foreground hover:text-foreground'
                  >
                    {showPassword ? (
                      <EyeOffIcon className='h-4 w-4' />
                    ) : (
                      <EyeIcon className='h-4 w-4' />
                    )}
                  </button>
                </div>
              </div>

              <Button
                type='submit'
                className='w-full'
                disabled={isPasswordLoading}
              >
                {isPasswordLoading ? (
                  <LoadingIcon className='mr-2 h-4 w-4' />
                ) : null}
                Sign In
              </Button>
            </form>

            <div className='text-center'>
              <Link
                href='/auth/forgot-password'
                className='text-sm text-muted-foreground hover:text-foreground'
              >
                Forgot your password?
              </Link>
            </div>

            <div className='relative'>
              <div className='absolute inset-0 flex items-center'>
                <span className='w-full border-t' />
              </div>
              <div className='relative flex justify-center text-xs uppercase'>
                <span className='bg-background px-2 text-muted-foreground'>
                  Or continue with
                </span>
              </div>
            </div>

            <Button
              variant='outline'
              onClick={handleGoogleAuth}
              disabled={isGoogleLoading}
              className='w-full'
            >
              {isGoogleLoading ? (
                <LoadingIcon className='mr-2 h-4 w-4' />
              ) : (
                <Icons.google className='mr-2 h-4 w-4' />
              )}
              Google
            </Button>
          </motion.div>
        </div>
      ) : (
        <div>
          <motion.div
            variants={FADE_IN_VARIANTS}
            animate='visible'
            initial='hidden'
            className='flex flex-col gap-4 py-8'
          >
            <form onSubmit={handleEmail} className='space-y-4'>
              <Input
                type='email'
                placeholder='Enter your email'
                value={email}
                onChange={(e) => setEmail(e.target.value)}
              />
              <Button
                type='submit'
                className='w-full'
                disabled={isEmailLoading}
              >
                {isEmailLoading ? (
                  <LoadingIcon className='mr-2 h-4 w-4' />
                ) : (
                  <MailIcon className='mr-2 h-4 w-4' />
                )}
                Continue with Email
              </Button>
            </form>

            <div className='relative'>
              <div className='absolute inset-0 flex items-center'>
                <span className='w-full border-t' />
              </div>
              <div className='relative flex justify-center text-xs uppercase'>
                <span className='bg-background px-2 text-muted-foreground'>
                  Or continue with
                </span>
              </div>
            </div>

            <Button
              variant='outline'
              onClick={handleGoogleAuth}
              disabled={isGoogleLoading}
              className='w-full'
            >
              {isGoogleLoading ? (
                <LoadingIcon className='mr-2 h-4 w-4' />
              ) : (
                <Icons.google className='mr-2 h-4 w-4' />
              )}
              Google
            </Button>
          </motion.div>

          <motion.div
            variants={FADE_IN_VARIANTS}
            animate='visible'
            initial='hidden'
            className='mt-4'
          >
            <p className='text-sm text-muted-foreground'>
              Don't have an account?{" "}
              <Link
                href='/auth/signup'
                className='font-medium text-primary hover:underline'
              >
                Sign up
              </Link>
            </p>
          </motion.div>
        </div>
      )}
    </div>
  )
}

export default SignInForm
