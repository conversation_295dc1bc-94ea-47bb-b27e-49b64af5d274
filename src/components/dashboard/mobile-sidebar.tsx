"use client"

import { LogOutIcon, MenuIcon, SearchIcon } from "lucide-react"
import Link from "next/link"
import { usePathname } from "next/navigation"

import { Button, buttonVariants } from "@/components/ui/button"
import { Sheet, SheetContent, SheetTrigger } from "@/components/ui/sheet"
import { SIDEBAR_LINKS } from "@/constants/links"

const MobileSidebar = () => {
  const { logout } = useAuth()
  const pathname = usePathname()

  const handleLogout = async () => {
    await logout()
  }

  return (
    <div className='flex lg:hidden'>
      <Sheet>
        <SheetTrigger asChild>
          <Button size='icon' variant='ghost' className='flex lg:hidden'>
            <MenuIcon className='size-5' />
          </Button>
        </SheetTrigger>
        <SheetContent className='w-screen max-w-full'>
          <div className='mt-10 flex h-full w-full flex-col py-3'>
            <Button
              variant='outline'
              className='w-full justify-start gap-2 px-2'
            >
              <SearchIcon className='size-4' />
              <span className='text-sm'>Search...</span>
            </Button>
            <ul className='w-full space-y-2 py-5'>
              {SIDEBAR_LINKS.map((link, index) => {
                const isActive = pathname === link.href

                return (
                  <li key={index} className='w-full'>
                    <Link
                      href={link.href}
                      className={buttonVariants({
                        variant: "ghost",
                        className: isActive
                          ? "w-full !justify-start bg-muted text-primary"
                          : "w-full !justify-start text-foreground/70",
                        // "w-full !justify-start text-foreground/70"
                      })}
                    >
                      <link.icon
                        strokeWidth={2}
                        className='mr-1.5 size-[18px]'
                      />
                      {link.label}
                    </Link>
                  </li>
                )
              })}
            </ul>

            <div className='mt-auto flex w-full flex-col pb-4'>
              <Button
                size='sm'
                variant='ghost'
                className='w-full justify-start gap-2 px-4'
                onClick={handleLogout}
              >
                <LogOutIcon className='mr-1.5 size-4' />
                Logout
              </Button>
            </div>
          </div>
        </SheetContent>
      </Sheet>
    </div>
  )
}

export default MobileSidebar
