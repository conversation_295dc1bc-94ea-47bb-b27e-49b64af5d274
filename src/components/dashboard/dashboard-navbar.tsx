/* eslint-disable import/order */
"use client"

// import MobileSidebar from "@/components/dashboard/mobile-sidebar";
import { HelpCircleIcon, ZapIcon } from "lucide-react"
import Link from "next/link"

import Icons from "@/components/global/icons"

import ComponentSearch from "../custom/component-search"

import { Container } from "../layout"
import { Menu } from "../navigation"
import { Button } from "../ui/button"
import DashboardMobileSidebar from "./dashboard-mobile-sidebar"

const DashboardNavbar = () => {
  return (
    <header
      id='dashboard-navbar'
      // className="fixed top-0 inset-x-0 w-full h-16 bg-background/40 backdrop-blur-md border-b border-border/50 px-4 z-50"
      className='fixed inset-x-0 top-0 z-50 h-16 w-full border-b border-border/50 bg-background px-4 backdrop-blur-md'
    >
      <Container className='flex size-full items-center justify-between'>
        <div className='flex items-center'>
          <Link href='/' className='flex items-center gap-x-2'>
            <Icons.icon className='w-6' />
            <span className='text-lg font-semibold'>DesignByte</span>
          </Link>

          <div className='ml-4 hidden items-center xl:flex'>
            <Menu />
          </div>
        </div>

        {/* <div className="w-max xl:block hidden">
          <Menu />
        </div> */}

        <div className='hidden w-full md:max-w-lg lg:block'>
          <ComponentSearch />
        </div>

        <div className='flex items-center gap-x-2'>
          <Button size='sm' variant='ghost'>
            <ZapIcon className='mr-1.5 size-4 fill-orange-500 text-orange-500' />
            Upgrade
          </Button>
          <Button
            asChild
            size='icon'
            variant='ghost'
            className='hidden lg:flex'
          >
            <Link href='/help' target='_blank'>
              <HelpCircleIcon className='size-5' />
            </Link>
          </Button>
          <DashboardMobileSidebar />
        </div>
      </Container>
    </header>
  )
}

export default DashboardNavbar
