"use client"

import {
  CodeIcon,
  CreditCardIcon,
  GanttChartIcon,
  LayoutIcon,
  SettingsIcon,
} from "lucide-react"

import { GlowingEffect } from "@/components/ui/glowing-effect"

import { SectionTitle } from "../common"
import { SectionWrapper } from "../layout"

interface GridItemProps {
  area: string
  icon: React.ReactNode
  title: string
  description: React.ReactNode
}

const GridItem = ({ area, icon, title, description }: GridItemProps) => {
  return (
    <li className={`min-h-[14rem] list-none ${area}`}>
      <div className='relative h-full rounded-2xl border p-2 md:rounded-3xl md:p-3'>
        <GlowingEffect
          spread={40}
          glow={true}
          disabled={false}
          proximity={64}
          inactiveZone={0.01}
        />
        <div className='border-0.75 relative flex h-full flex-col justify-between gap-6 overflow-hidden rounded-xl p-6 dark:shadow-[0px_0px_27px_0px_#2D2D2D] md:p-6'>
          <div className='relative flex flex-1 flex-col justify-between gap-3'>
            <div className='w-fit rounded-lg border border-gray-600 p-2'>
              {icon}
            </div>
            <div className='space-y-3'>
              <h3 className='-tracking-4 text-balance pt-0.5 font-sans text-xl/[1.375rem] font-semibold text-black dark:text-white md:text-2xl/[1.875rem]'>
                {title}
              </h3>
              <h2 className='font-sans text-sm/[1.125rem] text-black dark:text-neutral-400 md:text-base/[1.375rem] [&_b]:md:font-semibold [&_strong]:md:font-semibold'>
                {description}
              </h2>
            </div>
          </div>
        </div>
      </div>
    </li>
  )
}

export default function GlowinBentoGrid() {
  return (
    <SectionWrapper>
      <div className='text-center'>
        <SectionTitle
          title='Powerful Features for Modern Development'
          description='Everything you need to build beautiful, responsive applications with the latest technologies'
        />
      </div>

      <ul className='mt-12 grid grid-cols-1 grid-rows-none gap-4 md:grid-cols-12 md:grid-rows-3 lg:gap-4 xl:max-h-[34rem] xl:grid-rows-2'>
        <GridItem
          area='md:[grid-area:1/1/2/7] xl:[grid-area:1/1/2/5]'
          icon={
            <CodeIcon className='h-4 w-4 text-black dark:text-neutral-400' />
          }
          title='TypeScript Ready'
          description='Fully typed components and utilities for type-safe development and better developer experience.'
        />

        <GridItem
          area='md:[grid-area:1/7/2/13] xl:[grid-area:2/1/3/5]'
          icon={
            <LayoutIcon className='h-4 w-4 text-black dark:text-neutral-400' />
          }
          title='Tailwind CSS'
          description='Utility-first CSS framework for rapid UI development with complete customization.'
        />

        <GridItem
          area='md:[grid-area:2/1/3/7] xl:[grid-area:1/5/3/8]'
          icon={
            <SettingsIcon className='h-4 w-4 text-black dark:text-neutral-400' />
          }
          title='Shadcn UI'
          description='Beautiful, accessible components built with Radix UI and styled with Tailwind CSS.'
        />

        <GridItem
          area='md:[grid-area:2/7/3/13] xl:[grid-area:1/8/2/13]'
          icon={
            <GanttChartIcon className='h-4 w-4 text-black dark:text-neutral-400' />
          }
          title='Quality Tools'
          description='Integrated Prettier, ESLint, and Husky for consistent code quality and formatting.'
        />

        <GridItem
          area='md:[grid-area:3/1/4/13] xl:[grid-area:2/8/3/13]'
          icon={
            <CreditCardIcon className='h-4 w-4 text-black dark:text-neutral-400' />
          }
          title='Subscription Ready'
          description='Integrated payment processing and subscription management for SaaS applications.'
        />
      </ul>
    </SectionWrapper>
  )
}
