"use client"

import { X } from "lucide-react"
import React, { useState } from "react"

import { cn } from "@/functions"

import { Badge } from "../ui/badge"
import { Input } from "../ui/input"

interface ArrayInputProps {
  value: string[]
  onChange: (value: string[]) => void
  placeholder?: string
}

const CustomArrayInput: React.FC<ArrayInputProps> = ({
  value,
  onChange,
  placeholder = "Add a tag and press Enter",
}) => {
  const [inputValue, setInputValue] = useState("")

  const handleKeyDown = (e: React.KeyboardEvent<HTMLInputElement>) => {
    if (e.key === "Enter" || e.key === ",") {
      e.preventDefault()
      const newTag = inputValue.trim()
      if (newTag && !value.includes(newTag)) {
        onChange([...value, newTag])
      }
      setInputValue("")
    }
    if (e.key === "Backspace" && !inputValue && value.length > 0) {
      onChange(value.slice(0, -1))
    }
  }

  const removeTag = (tag: string) => {
    onChange(value.filter((t) => t !== tag))
  }

  return (
    <div
      className={cn(
        "flex flex-wrap items-center gap-2 rounded-md border px-3 py-2",
        "focus-within:ring focus-within:ring-blue-500 focus-within:ring-opacity-50",
      )}
    >
      {value?.map((tag) => (
        <Badge key={tag} variant='secondary'>
          {tag}
          <button
            className='ml-1 rounded-full outline-none ring-offset-background focus:ring-2 focus:ring-ring focus:ring-offset-2'
            onClick={() => removeTag(tag)}
          >
            <X className='h-3 w-3 text-muted-foreground hover:text-foreground' />
          </button>
        </Badge>
      ))}
      <Input
        type='text'
        placeholder={placeholder}
        value={inputValue}
        onChange={(e) => setInputValue(e.target.value)}
        onKeyDown={handleKeyDown}
        className='flex-grow bg-transparent focus:outline-none'
      />
    </div>
  )
}

export default CustomArrayInput
