"use client"

import { X } from "lucide-react"
import * as React from "react"

import { Badge } from "@/components/ui/badge"
import {
  Command,
  CommandInput,
  CommandList,
  CommandGroup,
  CommandItem,
  CommandEmpty,
} from "@/components/ui/command"

export type Option = {
  label: string
  value: string
}

interface SelectorProps {
  options?: Option[]
  selected?: string | string[]
  onChange: (selected: string | string[]) => void
  placeholder?: string
  multiple?: boolean
}

export default function Selector({
  options = [],
  selected = [],
  onChange,
  placeholder = "Select items...",
  multiple = true,
}: SelectorProps) {
  const inputRef = React.useRef<HTMLInputElement>(null)
  const [inputValue, setInputValue] = React.useState("")
  const [open, setOpen] = React.useState(false)

  const handleUnselect = (value: string) => {
    if (multiple) {
      onChange((selected as string[]).filter((s) => s !== value))
    } else {
      onChange("")
    }
  }

  const handleSelect = (value: string) => {
    if (multiple) {
      const isSelected = (selected as string[]).includes(value)
      if (isSelected) {
        onChange((selected as string[]).filter((s) => s !== value))
      } else {
        onChange([...(selected as string[]), value])
      }
    } else {
      onChange(value)
    }
    setInputValue("")
  }

  const handleKeyDown = (e: React.KeyboardEvent<HTMLDivElement>) => {
    const input = inputRef.current
    if (input) {
      if (e.key === "Delete" || e.key === "Backspace") {
        if (
          input.value === "" &&
          multiple &&
          (selected as string[]).length > 0
        ) {
          handleUnselect(
            (selected as string[])[(selected as string[]).length - 1],
          )
        }
      }
      if (e.key === " " && input.value === "") {
        e.preventDefault()
      }
    }
  }

  const filteredOptions = options.filter((option) =>
    option.label.toLowerCase().includes(inputValue.toLowerCase()),
  )

  return (
    <Command
      className='overflow-visible bg-transparent'
      onKeyDown={handleKeyDown}
    >
      <div className='group rounded-md border border-input px-3 py-2 text-sm ring-offset-background focus-within:ring-2 focus-within:ring-ring focus-within:ring-offset-2'>
        <div className='flex flex-wrap gap-1'>
          {multiple
            ? (selected as string[]).map((value) => {
                const option = options.find((o) => o.value === value)
                return (
                  <Badge key={value} variant='secondary'>
                    {option ? option.label : value}
                    <button
                      className='ml-1 rounded-full outline-none ring-offset-background focus:ring-2 focus:ring-ring focus:ring-offset-2'
                      onKeyDown={(e) => {
                        if (e.key === "Enter") {
                          handleUnselect(value)
                        }
                      }}
                      onMouseDown={(e) => {
                        e.preventDefault()
                        e.stopPropagation()
                      }}
                      onClick={() => handleUnselect(value)}
                    >
                      <X className='h-3 w-3 text-muted-foreground hover:text-foreground' />
                    </button>
                  </Badge>
                )
              })
            : selected && (
                <Badge variant='secondary'>
                  {options.find((o) => o.value === selected)?.label || selected}
                  <button
                    className='ml-1 rounded-full outline-none ring-offset-background focus:ring-2 focus:ring-ring focus:ring-offset-2'
                    onKeyDown={(e) => {
                      if (e.key === "Enter") {
                        handleUnselect(selected as string)
                      }
                    }}
                    onMouseDown={(e) => {
                      e.preventDefault()
                      e.stopPropagation()
                    }}
                    onClick={() => handleUnselect(selected as string)}
                  >
                    <X className='h-3 w-3 text-muted-foreground hover:text-foreground' />
                  </button>
                </Badge>
              )}
        </div>

        <CommandInput
          ref={inputRef}
          value={inputValue}
          onValueChange={setInputValue}
          onFocus={() => setOpen(true)}
          onBlur={() => setOpen(false)}
          placeholder={placeholder}
          className='ml-2 flex-1 bg-transparent outline-none placeholder:text-muted-foreground'
        />
      </div>
      <div className='relative mt-2'>
        {open && (
          <div className='absolute top-0 z-10 w-full rounded-md border bg-popover text-popover-foreground shadow-md outline-none animate-in'>
            <CommandList>
              <CommandEmpty>No results found.</CommandEmpty>
              <CommandGroup>
                {filteredOptions.map((option) => {
                  const isSelected = multiple
                    ? (selected as string[]).includes(option.value)
                    : selected === option.value
                  return (
                    <CommandItem
                      key={option.value}
                      onMouseDown={(e) => {
                        e.preventDefault()
                        e.stopPropagation()
                      }}
                      onSelect={() => handleSelect(option.value)}
                      className={`cursor-pointer ${
                        isSelected ? "bg-accent" : ""
                      }`}
                    >
                      {option.label}
                    </CommandItem>
                  )
                })}
              </CommandGroup>
            </CommandList>
          </div>
        )}
      </div>
    </Command>
  )
}
