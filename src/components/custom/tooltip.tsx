import React from "react"

import { createArrayFromString } from "@/utils/generic"

import { Badge } from "../ui/badge"
import { Tooltip, TooltipTrigger, TooltipContent } from "../ui/tooltip"

interface TooltipListProps {
  items?: string[] // Can be a string (comma-separated) or an array
  item?: string // Single item
  placeholder?: string // Placeholder text when items are empty
  maxContentHeight?: string // Customizable max height for overflow
  tooltipClassName?: string // Custom className for Tooltip
}

const TooltipList: React.FC<TooltipListProps> = ({
  items,
  item,
  placeholder = "N/A",
  maxContentHeight = "max-h-36",
  tooltipClassName = "max-w-sm",
}) => {
  // Array.isArray(items)
  return (
    <Tooltip>
      <TooltipTrigger asChild>
        <span className='line-clamp-1'>
          {/* {normalizedItems.length ? normalizedItems.join(", ") : placeholder} */}
          {/* {Array.isArray(items) ? items?.join(", ") : items} */}
          {Array.isArray(items) ? items?.join(", ") : (item ?? placeholder)}
        </span>
      </TooltipTrigger>
      <TooltipContent className={`${tooltipClassName}`}>
        <div
          className={`flex flex-col items-center justify-start gap-2 ${maxContentHeight} overflow-auto`}
        >
          {items &&
            items?.map((item, index) => (
              <Badge key={item + index} variant='secondary'>
                {item}
              </Badge>
            ))}

          {item && <Badge className='rounded-md'>{item}</Badge>}
        </div>
      </TooltipContent>
    </Tooltip>
  )
}

export default TooltipList
