"use client"

// import Masonry from "react-layout-masonry";
import { motion } from "framer-motion"

import { MainCard } from "../cards/main-card"

type Props = {
  data: any
}

const MasonaryLayout = ({ data }: Props) => {
  return (
    // <Masonry
    //   // columns={3}
    //   columns={{ 640: 1, 768: 2, 1024: 2, 1480: 3, 1960: 4 }}
    //   gap={16}
    // >
    <motion.div
      initial={{ opacity: 0, y: -20 }}
      animate={{ opacity: 1, y: 0 }}
      transition={{ duration: 0.5 }}
      className='grid w-full gap-4 md:grid-cols-2 xl:grid-cols-3 2xl:grid-cols-4'
    >
      {data?.map((item: any) => {
        return <MainCard key={item.id} data={item} />
      })}
    </motion.div>

    // </Masonry>
  )
}

export default MasonaryLayout
