import { <PERSON><PERSON><PERSON><PERSON><PERSON>, ArrowLeft, Search } from "lucide-react"
import Link from "next/link"

import { <PERSON><PERSON><PERSON>, Wrapper } from "@/components/layout"
import { Button } from "@/components/ui/button"

export function ProductNotFound() {
  return (
    <Wrapper className='min-h-screen bg-gradient-to-br from-gray-900 via-black to-gray-900'>
      <Container className='flex items-center justify-center py-20'>
        <div className='mx-auto max-w-md text-center'>
          {/* Icon */}
          <div className='mb-8 flex justify-center'>
            <div className='relative'>
              <div className='flex h-24 w-24 items-center justify-center rounded-full border border-red-500/30 bg-gradient-to-br from-red-500/20 to-orange-500/20'>
                <AlertCircle className='h-12 w-12 text-red-400' />
              </div>
              <div className='absolute -right-1 -top-1 flex h-6 w-6 items-center justify-center rounded-full bg-red-500'>
                <Search className='h-3 w-3 text-white' />
              </div>
            </div>
          </div>

          {/* Heading */}
          <h1 className='mb-4 text-3xl font-bold tracking-tight text-white md:text-4xl'>
            Product Not Found
          </h1>

          {/* Description */}
          <p className='mb-8 text-lg leading-relaxed text-gray-400'>
            The product you&apos;re looking for doesn&apos;t exist or has been
            removed. It might have been moved or is temporarily unavailable.
          </p>

          {/* Actions */}
          <div className='space-y-4 sm:flex sm:justify-center sm:space-x-4 sm:space-y-0'>
            <Button
              asChild
              className='w-full bg-white text-black transition-colors hover:bg-gray-100 sm:w-auto'
            >
              <Link href='/'>
                <ArrowLeft className='mr-2 h-4 w-4' />
                Back to Home
              </Link>
            </Button>

            <Button
              asChild
              variant='outline'
              className='w-full border-gray-600 text-gray-300 transition-colors hover:bg-gray-800 hover:text-white sm:w-auto'
            >
              <Link href='/templates'>
                <Search className='mr-2 h-4 w-4' />
                Browse Templates
              </Link>
            </Button>
          </div>

          {/* Help text */}
          <div className='mt-12 border-t border-gray-800 pt-8'>
            <p className='text-sm text-gray-500'>
              Need help? Check our{" "}
              <Link
                href='/support'
                className='text-blue-400 underline transition-colors hover:text-blue-300'
              >
                support page
              </Link>{" "}
              or{" "}
              <Link
                href='/contact'
                className='text-blue-400 underline transition-colors hover:text-blue-300'
              >
                contact us
              </Link>
            </p>
          </div>
        </div>
      </Container>
    </Wrapper>
  )
}
