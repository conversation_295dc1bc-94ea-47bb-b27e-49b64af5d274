import { cn } from "@/lib/utils"

interface SectionTitleProps {
  title: string
  description?: string
  className?: string
  descriptionClassName?: string
}

export default function SectionTitle({
  title,
  description,
  className,
  descriptionClassName,
}: SectionTitleProps) {
  return (
    <div className='mx-auto max-w-3xl text-center'>
      <h2
        className={cn(
          "mb-4 bg-gradient-to-r from-primary to-white bg-clip-text font-heading text-3xl font-medium text-transparent sm:text-4xl md:text-5xl xl:text-6xl",
          className,
        )}
      >
        {title}
      </h2>
      {description && (
        <p
          className={cn(
            "mx-auto text-center text-lg text-accent-foreground md:max-w-[95%]",
            descriptionClassName,
          )}
        >
          {description}
        </p>
      )}
    </div>
  )
}
