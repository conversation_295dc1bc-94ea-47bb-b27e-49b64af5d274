import { cn } from "@/lib/utils"

interface PageTitleProps {
  title: string
  description?: string
  className?: string
  descriptionClassName?: string
}

function PageTitle({
  title,
  description,
  className,
  descriptionClassName,
}: PageTitleProps) {
  return (
    <div className='mx-auto max-w-3xl text-center'>
      <h1
        className={cn(
          "mb-4 bg-gradient-to-r from-primary to-white bg-clip-text text-3xl font-bold text-transparent sm:text-4xl md:text-4xl xl:text-5xl",
          className,
        )}
      >
        {title}
      </h1>
      {description && (
        <p
          className={cn("text-lg text-accent-foreground", descriptionClassName)}
        >
          {description}
        </p>
      )}
    </div>
  )
}

export default PageTitle
