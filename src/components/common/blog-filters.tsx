"use client"

import { Search, X } from "lucide-react"
import { useState } from "react"

import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Input } from "@/components/ui/input"
import { BLOG_CATEGORIES } from "@/constants/blogs"

interface BlogFiltersProps {
  searchQuery: string
  onSearchChange: (query: string) => void
  selectedCategory: string
  onCategoryChange: (category: string) => void
  selectedTags: string[]
  onTagsChange: (tags: string[]) => void
  availableTags: string[]
}

export const BlogFilters = ({
  searchQuery,
  onSearchChange,
  selectedCategory,
  onCategoryChange,
  selectedTags,
  onTagsChange,
  availableTags,
}: BlogFiltersProps) => {
  const [showAllTags, setShowAllTags] = useState(false)

  const handleTagToggle = (tag: string) => {
    if (selectedTags.includes(tag)) {
      onTagsChange(selectedTags.filter((t) => t !== tag))
    } else {
      onTagsChange([...selectedTags, tag])
    }
  }

  const clearAllFilters = () => {
    onSearchChange("")
    onCategoryChange("")
    onTagsChange([])
  }

  const hasActiveFilters =
    searchQuery || selectedCategory || selectedTags.length > 0
  const displayTags = showAllTags ? availableTags : availableTags.slice(0, 8)

  return (
    <div className='space-y-6'>
      {/* Search */}
      <div className='relative'>
        <Search className='absolute left-3 top-1/2 h-4 w-4 -translate-y-1/2 text-muted-foreground' />
        <Input
          placeholder='Search articles...'
          value={searchQuery}
          onChange={(e) => onSearchChange(e.target.value)}
          className='pl-10'
        />
      </div>

      {/* Categories */}
      <div className='space-y-3'>
        <h3 className='text-sm font-medium'>Categories</h3>
        <div className='flex flex-wrap gap-2'>
          <Button
            variant={selectedCategory === "" ? "default" : "outline"}
            size='sm'
            onClick={() => onCategoryChange("")}
            className='h-8'
          >
            All
          </Button>
          {BLOG_CATEGORIES.map((category) => (
            <Button
              key={category.id}
              variant={selectedCategory === category.id ? "default" : "outline"}
              size='sm'
              onClick={() => onCategoryChange(category.id)}
              className='h-8'
            >
              {category.name}
            </Button>
          ))}
        </div>
      </div>

      {/* Tags */}
      <div className='space-y-3'>
        <h3 className='text-sm font-medium'>Tags</h3>
        <div className='flex flex-wrap gap-2'>
          {displayTags.map((tag) => (
            <Badge
              key={tag}
              variant={selectedTags.includes(tag) ? "default" : "outline"}
              className='cursor-pointer transition-colors hover:bg-primary/10'
              onClick={() => handleTagToggle(tag)}
            >
              {tag}
              {selectedTags.includes(tag) && <X className='ml-1 h-3 w-3' />}
            </Badge>
          ))}
          {availableTags.length > 8 && (
            <Button
              variant='ghost'
              size='sm'
              onClick={() => setShowAllTags(!showAllTags)}
              className='h-6 text-xs'
            >
              {showAllTags ? "Show Less" : `+${availableTags.length - 8} more`}
            </Button>
          )}
        </div>
      </div>

      {/* Clear Filters */}
      {hasActiveFilters && (
        <Button
          variant='outline'
          size='sm'
          onClick={clearAllFilters}
          className='w-full'
        >
          <X className='mr-2 h-4 w-4' />
          Clear All Filters
        </Button>
      )}
    </div>
  )
}
