import Image from "next/image"
import React from "react"

import Icons from "./icons"

interface Props {
  children: React.ReactNode
  background?: number
}

const Background = ({ children, background = 3 }: Props) => {
  return (
    <main id='background' className='min-h-scree relative -z-10 flex-none'>
      {background === 0 && (
        <div className='absolute inset-0 h-screen'>
          <Image
            src={
              "https://utfs.io/f/qPlpyBmwd8UNDbQGePgfcU0BPHiJwnq2OTMo8C6r5aRkyYWb"
            }
            alt='background'
            layout='fill'
            objectFit='cover'
            objectPosition='center'
            quality={100}
            className='z-[-1]'
          />
        </div>
      )}

      {background === 1 && (
        <div className='absolute inset-0 hidden h-full bg-dot-foreground/[0.2] lg:flex' />
      )}
      {background === 2 && (
        <div className='pointer-events-none absolute inset-0 hidden h-full items-center justify-center bg-black [mask-image:radial-gradient(ellipse_at_center,transparent_20%,black)] lg:flex' />
      )}

      {(background === 0 || 3) && (
        <>
          <div className='absolute left-[calc(55%-379px/2)] top-[-266px] -z-10 hidden h-[620px] w-[379px] rounded-full bg-primary/60 opacity-50 blur-[10rem] lg:flex' />
          <div className='absolute left-[calc(50%-433px/2)] top-[-60px] -z-10 hidden h-[525px] w-[433px] rounded-full bg-primaryLight/40 opacity-50 blur-[15rem] lg:flex' />
        </>
      )}

      {children}
      {background === 4 && (
        <Icons.grid className='absolute left-0 top-0 h-full w-full' />
      )}

      {background === 5 && (
        <div className='absolute inset-0 -z-[1] hidden items-center justify-center bg-background [mask-image:radial-gradient(ellipse_at_center,transparent_5%,black)] lg:flex' />
      )}

      {background === 6 && (
        <div className='relative flex h-[50rem] w-full items-center justify-center bg-background bg-dot-white/[0.2]' />
      )}
    </main>
  )
}

export default Background
