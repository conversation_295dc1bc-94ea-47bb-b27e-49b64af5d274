"use client"

import { <PERSON><PERSON><PERSON><PERSON> } from "@clerk/nextjs"
import { ReactQueryDevtools } from "@tanstack/react-query-devtools"
import React from "react"

import { PHProvider } from "@/providers/posthog-provider"
import ReactQueryProvider from "@/providers/react-query-provider"
import ThemeProvider from "@/providers/theme-provider"

import { Toaster } from "../ui/sonner"

// import { TokenProvider } from "@/context/tokenContext";

// import { QueryClient, QueryClientProvider } from "@tanstack/react-query";

interface Props {
  children: React.ReactNode
}

// const client = new QueryClient();

const Providers = ({ children }: Props) => {
  return (
    // <QueryClientProvider client={client}>
    // <ClerkProvider>
    //     {children}
    // </ClerkProvider>
    // // </QueryClientProvider>

    <>
      {/* <ThemeProvider attribute="class" defaultTheme="system" enableSystem> */}
      <ReactQueryProvider>
        <ThemeProvider
          attribute='class'
          defaultTheme='dark'
          enableSystem
          disableTransitionOnChange
        >
          <PHProvider>
            {/* <SessionProvider session={session}> */}

            <ClerkProvider>
              {/* <TokenProvider>{children}</TokenProvider> */}
              {children}
            </ClerkProvider>

            <Toaster richColors theme='dark' position='top-right' />
            <ReactQueryDevtools initialIsOpen={false} />
            {/* </SessionProvider> */}
          </PHProvider>
        </ThemeProvider>
      </ReactQueryProvider>
    </>
  )
}

export default Providers
