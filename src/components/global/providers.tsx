"use client"

import { ReactQueryDevtools } from "@tanstack/react-query-devtools"
import React from "react"

import { PHProvider } from "@/providers/posthog-provider"
import ReactQueryProvider from "@/providers/react-query-provider"
import ThemeProvider from "@/providers/theme-provider"

import { Toaster } from "../ui/sonner"

interface Props {
  children: React.ReactNode
}

const Providers = ({ children }: Props) => {
  return (
    <ReactQueryProvider>
      <ThemeProvider
        attribute='class'
        defaultTheme='dark'
        enableSystem
        disableTransitionOnChange
      >
        <PHProvider>
          <AuthInitializer />
          {children}
          <Toaster richColors theme='dark' position='top-right' />
          <ReactQueryDevtools initialIsOpen={false} />
        </PHProvider>
      </ThemeProvider>
    </ReactQueryProvider>
  )
}

export default Providers
