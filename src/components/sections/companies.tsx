import { SectionTitle } from "../common"
import Images from "../global/images"
import { SectionWrapper } from "../layout"
import Marquee from "../ui/marquee"

export const Companies = () => {
  return (
    <SectionWrapper className='flex w-full py-20'>
      <SectionTitle
        title='Companies that trust us'
        description={`We are trusted by some of the world's leading companies`}
      />
      <div className='relative mt-16 w-full overflow-hidden'>
        <Marquee pauseOnHover className='[--duration:30s]'>
          <div className='flex gap-8 md:gap-12'>
            <Images.company1 className='h-8 w-24' />
            <Images.company2 className='h-8 w-24' />
            <Images.company3 className='h-8 w-24' />
            <Images.company4 className='h-8 w-24' />
            <Images.company5 className='h-8 w-24' />
            <Images.company6 className='h-8 w-24' />
            <Images.company7 className='h-8 w-24' />
            <Images.company8 className='h-8 w-24' />
            <Images.company9 className='h-8 w-24' />
            <Images.company10 className='h-8 w-24' />
          </div>
        </Marquee>
        <div className='pointer-events-none absolute inset-y-0 left-0 w-1/4 bg-gradient-to-r from-background' />
        <div className='pointer-events-none absolute inset-y-0 right-0 w-1/4 bg-gradient-to-l from-background' />
      </div>
    </SectionWrapper>
  )
}
