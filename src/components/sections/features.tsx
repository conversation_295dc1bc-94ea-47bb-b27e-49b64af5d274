"use client"

import Heading from "../global/heading"
import Icons from "../global/icons"
import Images from "../global/images"
import { Container } from "../layout"
import MagicCard from "../ui/magic-card"
import { Ripple } from "../ui/ripple"
import { SectionBadge } from "../ui/section-bade"

const Features = () => {
  return (
    <div className='flex w-full flex-col items-center justify-center py-12 md:py-16 lg:py-24'>
      <Container>
        <div className='mx-auto flex max-w-2xl flex-col items-center text-center'>
          <SectionBadge title='Features' />
          {/* <h2 className="text-2xl md:text-4xl lg:text-5xl font-heading font-medium !leading-snug mt-6">
                        Create content faster <br /> and smarter
                    </h2> */}
          <Heading className='mt-6'>
            {/* Create content faster <br /> and smarter */}
            {/* Why Developers Love <br /> DesignByte. */}
            Exceptional Resources <br /> For Modern Development
          </Heading>
          <p className='mt-6 text-center text-base text-accent-foreground/80 md:text-lg'>
            Every resource on DesignByte is meticulously crafted with modern
            best practices and strict quality standards. We prioritize developer
            experience and code quality to help you build better applications
            faster.
            {/* DesignByte is the go-to platform for developers looking to save time and maximize efficiency. With versatile resources and seamless integrations, it’s everything you need to build smarter. */}
          </p>
        </div>
      </Container>
      <div className='mt-16 w-full'>
        <div className='flex w-full flex-col items-center gap-5 lg:gap-5'>
          <Container>
            <div className='grid w-full grid-cols-1 gap-5 lg:grid-cols-[1fr_.65fr] lg:gap-5'>
              <MagicCard
                particles={true}
                className='flex size-full flex-col items-start bg-primary/[0.08]'
              >
                <div className='bento-card flex min-h-72 items-center justify-center'>
                  <span className='relative mx-auto text-muted-foreground group-hover:text-foreground'>
                    <Icons.stars className='h-20 w-20' />
                  </span>
                  <Ripple />
                </div>
              </MagicCard>
              <MagicCard
                particles={true}
                className='flex w-full flex-col items-start bg-primary/[0.08]'
              >
                <div className='bento-card w-full flex-row gap-6'>
                  <div className='h-40 w-full'>
                    <Images.analytics className='h-full w-full' />
                  </div>
                  <div className='flex flex-col'>
                    <h4 className='heading font-heading text-xl font-medium'>
                      Modern Development Stack
                    </h4>
                    <p className='mt-2 text-sm text-muted-foreground md:text-base'>
                      Built with the latest frameworks and tools to ensure type
                      safety, performance, and long-term maintainability.
                    </p>
                  </div>
                </div>
              </MagicCard>
            </div>
          </Container>
          <Container>
            <div className='grid w-full grid-cols-1 gap-5 lg:grid-cols-3 lg:gap-5'>
              <MagicCard
                particles={true}
                className='row-span-1 flex w-full flex-col items-start bg-primary/[0.08]'
              >
                <div className='bento-card w-full flex-row gap-6'>
                  <div className='relative h-52 w-full'>
                    <Images.ideation className='h-full w-full' />
                    <div className='absolute left-1/2 top-1/2 -z-10 h-40 w-40 -translate-x-1/2 -translate-y-1/2 rounded-full bg-primary/10 blur-3xl' />
                  </div>
                  <div className='mt-auto flex flex-col'>
                    <h4 className='heading font-heading text-xl font-medium'>
                      {/* Content ideation */}
                      Beautiful UI Components
                    </h4>
                    <p className='mt-2 text-sm text-muted-foreground md:text-base'>
                      Responsive, accessible interfaces with consistent design
                      systems that adapt perfectly to any screen size.
                    </p>
                  </div>
                </div>
              </MagicCard>
              <div className='grid-rows grid gap-5 lg:gap-5'>
                <MagicCard
                  particles={true}
                  className='row-span- row-start-[0.5] flex h-32 w-full flex-col items-start bg-primary/[0.08]'
                >
                  <div className='bento-card relative w-full items-center justify-center'>
                    <div className='absolute left-1/2 top-1/2 w-full -translate-x-1/2 -translate-y-1/2'>
                      <p className='text-justify text-base text-muted-foreground [mask-image:radial-gradient(50%_50%_at_50%_50%,#BAB3FF_0%,rgba(186,179,255,0)_90.69%)]'>
                        Quality is our priority. Every resource undergoes
                        rigorous testing and follows strict coding standards to
                        ensure clean, consistent, and maintainable code that you
                        can trust.
                      </p>
                    </div>
                    <div className='relative h-16 w-full'>
                      <Images.centeral className='h-full w-full' />
                      <div className='absolute left-1/2 top-1/2 z-10 h-20 w-20 -translate-x-1/2 -translate-y-1/2 rounded-full bg-primary/10 blur-2xl' />
                    </div>
                  </div>
                </MagicCard>
                <MagicCard
                  particles={true}
                  className='row-start-2 flex !h-max w-full flex-col items-start bg-primary/[0.08]'
                >
                  <div className='bento-card relative w-full gap-6'>
                    <div className='relative h-48 w-full'>
                      <Images.rings className='absolute inset-0 h-full w-full' />
                      <Images.rings className='absolute left-1/2 top-1/2 h-56 w-56 -translate-x-1/2 -translate-y-1/2' />
                      <Icons.icon className='absolute left-1/2 top-1/2 h-24 w-24 -translate-x-1/2 -translate-y-1/2 opacity-80' />
                      <Images.circlePallete className='h-full w-full opacity-30' />
                    </div>
                    <div className='absolute left-1/2 top-1/2 -z-10 h-28 w-28 -translate-x-1/2 -translate-y-1/2 rounded-full bg-primary/10 blur-3xl' />
                  </div>
                </MagicCard>
              </div>
              <MagicCard
                particles={true}
                className='row-span-1 flex w-full flex-col items-start bg-primary/[0.08]'
              >
                <div className='bento-card w-full flex-row gap-6'>
                  <div className='mb-auto flex flex-col'>
                    <h4 className='heading font-heading text-xl font-medium'>
                      Developer Experience First
                    </h4>
                    <p className='mt-2 text-sm text-muted-foreground md:text-base'>
                      Optimized developer experience with proper folder
                      structure, documentation, and testing setup for maximum
                      productivity.
                    </p>
                  </div>
                  <div className='relative h-28 w-full'>
                    <Images.integration className='h-full w-full' />
                    <div className='absolute left-1/2 top-1/2 -z-10 h-28 w-28 -translate-x-1/2 -translate-y-full rounded-full bg-primary/10 blur-3xl' />
                  </div>
                </div>
              </MagicCard>
            </div>
          </Container>
          <Container>
            <div className='grid w-full grid-cols-1 gap-5 lg:grid-cols-[.40fr_1fr] lg:gap-5'>
              <MagicCard
                particles={true}
                className='flex w-full flex-col items-start bg-primary/[0.08]'
              >
                <div className='bento-card w-full flex-row gap-6'>
                  <div className='w-full'>
                    <Images.image className='h-40 w-full lg:h-auto' />
                  </div>
                  <div className='mt-auto flex flex-col'>
                    <h4 className='heading font-heading text-xl font-medium'>
                      Production-Ready Code
                    </h4>
                    <p className='mt-2 text-sm text-muted-foreground md:text-base'>
                      Thoroughly tested code that&apos;s ready for production
                      deployment with best practices for performance and
                      security.
                    </p>
                  </div>
                </div>
              </MagicCard>
              <MagicCard
                particles={true}
                className='flex w-full flex-col items-start bg-primary/[0.08]'
              >
                <div className='bento-card w-full flex-row gap-6'>
                  <div className='w-full'>
                    <Images.hash className='h-40 w-full lg:h-52' />
                  </div>
                  <div className='mt-auto flex flex-col'>
                    <h4 className='heading font-heading text-xl font-medium'>
                      Comprehensive Resources
                    </h4>
                    <p className='mt-2 text-sm text-muted-foreground md:text-base'>
                      Complete packages including templates, starter kits,
                      themes, and Figma designs that work seamlessly together.
                    </p>
                  </div>
                </div>
              </MagicCard>
            </div>
          </Container>
        </div>
      </div>
    </div>
  )
}

export default Features
