import { TrendingUp } from "lucide-react"

import { BlogPostCard } from "@/components/cards"
import { Card, CardContent, CardHeader, CardTitle } from "@/components/ui/card"
import { POPULAR_POSTS } from "@/constants"

export const PopularPostsSidebar = () => {
  return (
    <Card>
      <CardHeader>
        <CardTitle className='flex items-center gap-2 text-lg'>
          <TrendingUp className='h-5 w-5 text-primary' />
          Popular Articles
        </CardTitle>
      </CardHeader>
      <CardContent className='space-y-4'>
        {POPULAR_POSTS.map((post) => (
          <BlogPostCard
            key={post.id}
            post={post}
            variant='compact'
            className='border-0 shadow-none'
          />
        ))}
      </CardContent>
    </Card>
  )
}
