"use client"

import {
  CodeIcon,
  GanttChartIcon,
  LayoutIcon,
  SettingsIcon,
  ZapIcon,
} from "lucide-react"

import { FeatureCard } from "@/components/cards/feature-card"

import { SectionTitle } from "../common"
import { SectionWrapper } from "../layout"

const FEATURES = [
  {
    title: "TypeScript Ready",
    description:
      "All our templates are built with TypeScript for type safety and better developer experience.",
    icon: <CodeIcon className='h-6 w-6' />,
  },
  {
    title: "Tailwind CSS",
    description:
      "Utility-first CSS framework for rapid UI development with complete customization.",
    icon: <LayoutIcon className='h-6 w-6' />,
  },
  {
    title: "Shadcn UI",
    description:
      "Beautiful, accessible components built with Radix UI and styled with Tailwind CSS.",
    icon: <SettingsIcon className='h-6 w-6' />,
  },
  {
    title: "Quality Tools",
    description:
      "Integrated Prettier, ESLint, and Husky for consistent code quality and formatting.",
    icon: <GanttChartIcon className='h-6 w-6' />,
  },
  {
    title: "Performance Optimized",
    description:
      "Built with performance in mind, ensuring fast load times and smooth interactions.",
    icon: <ZapIcon className='h-6 w-6' />,
  },
  {
    title: "Responsive Design",
    description:
      "Fully responsive layouts that work beautifully on all devices and screen sizes.",
    icon: <LayoutIcon className='h-6 w-6' />,
  },
] as const

export const FeaturesGrid = () => {
  return (
    <SectionWrapper>
      <SectionTitle
        title='Built with Modern Technologies'
        description='Our starter kits and templates are built with the latest technologies and best practices'
      />

      <div className='mt-12 grid grid-cols-1 gap-6 sm:grid-cols-2 lg:grid-cols-3'>
        {FEATURES.map((feature, index) => (
          <FeatureCard
            key={index}
            title={feature.title}
            description={feature.description}
            icon={feature.icon}
          />
        ))}
      </div>
    </SectionWrapper>
  )
}
