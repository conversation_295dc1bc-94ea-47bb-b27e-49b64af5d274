import {
  ArrowRight,
  CheckCircle,
  Crown,
  Rocket,
  Sparkles,
  Star,
  Users,
  Zap,
} from "lucide-react"
import Link from "next/link"

import Heading from "@/components/global/heading"
import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"

const AboutCtaSection = () => {
  const stats = [
    {
      value: "500+",
      label: "Premium Resources",
      icon: Crown,
      iconColor: "text-amber-500",
      bgColor: "bg-amber-50 dark:bg-amber-950/20",
    },
    {
      value: "10k+",
      label: "Happy Developers",
      icon: Users,
      iconColor: "text-emerald-500",
      bgColor: "bg-emerald-50 dark:bg-emerald-950/20",
    },
    {
      value: "99%",
      label: "Quality Score",
      icon: Star,
      iconColor: "text-violet-500",
      bgColor: "bg-violet-50 dark:bg-violet-950/20",
    },
    {
      value: "24/7",
      label: "Expert Support",
      icon: Zap,
      iconColor: "text-orange-500",
      bgColor: "bg-orange-50 dark:bg-orange-950/20",
    },
  ]

  const features = [
    "Premium templates & starter kits",
    "Best practices implementation",
    "Modern tech stack",
    "Lifetime updates",
    "Priority support",
  ]

  return (
    <section className='relative overflow-hidden rounded-xl border border-border bg-background py-20 lg:py-32'>
      {/* Background with gradient */}
      <div className='absolute inset-0 -z-10'>
        {/* Gradient Background */}
        <div className='absolute inset-0 bg-gradient-to-br from-background via-muted/20 to-background' />

        {/* Animated gradient orbs */}
        <div className='absolute left-1/4 top-1/3 h-96 w-96 animate-pulse rounded-full bg-gradient-to-r from-accent/10 to-secondary/10 blur-3xl' />
        <div className='absolute bottom-1/3 right-1/4 h-80 w-80 animate-pulse rounded-full bg-gradient-to-r from-secondary/10 to-accent/10 blur-3xl' />

        {/* Grid pattern */}
        <div className='absolute inset-0 bg-grid-foreground/[0.02]' />
      </div>

      <div className='container relative z-10 mx-auto px-4'>
        <div className='mx-auto max-w-5xl'>
          {/* Header */}
          <div className='mb-16 text-center'>
            <div className='mb-6 inline-flex items-center gap-2 rounded-full border border-blue-200 bg-gradient-to-r from-blue-50 to-indigo-50 px-6 py-3 text-sm font-medium shadow-lg backdrop-blur-sm dark:border-blue-800 dark:from-blue-950/30 dark:to-indigo-950/30'>
              <Rocket className='h-4 w-4 text-blue-600 dark:text-blue-400' />
              <span className='font-semibold text-blue-700 dark:text-blue-300'>
                Transform Your Development
              </span>
              <Badge
                variant='secondary'
                className='ml-2 border-blue-200 bg-gradient-to-r from-blue-100 to-indigo-100 text-xs text-blue-700 dark:border-blue-700 dark:from-blue-900/50 dark:to-indigo-900/50 dark:text-blue-300'
              >
                <Sparkles className='mr-1 h-3 w-3 text-yellow-500' />
                New
              </Badge>
            </div>

            <Heading className='mb-8 bg-gradient-to-r from-slate-900 via-blue-900 to-indigo-900 bg-clip-text text-4xl font-bold text-transparent dark:from-slate-100 dark:via-blue-100 dark:to-indigo-100 lg:text-6xl'>
              Ready to Build <br />
              <span className='relative text-primary-foreground'>
                Exceptional Projects?
                <svg
                  className='absolute -bottom-2 left-0 h-3 w-full text-blue-500'
                  viewBox='0 0 300 12'
                  fill='none'
                  xmlns='http://www.w3.org/2000/svg'
                >
                  <path
                    d='M1 8.5C50 4.5 100 1.5 150 5.5C200 9.5 250 4.5 299 8.5'
                    stroke='currentColor'
                    strokeWidth='2'
                    strokeLinecap='round'
                  />
                </svg>
              </span>
            </Heading>

            <p className='mb-12 text-xl text-muted-foreground lg:text-2xl'>
              Join thousands of developers who value code quality and standards.
              Build amazing projects faster than ever with DesignByte&apos;s
              premium resources.
            </p>
          </div>

          {/* Features List */}
          <div className='mb-12 flex flex-wrap justify-center gap-4'>
            {features.map((feature, index) => (
              <div
                key={index}
                className='flex items-center gap-2 rounded-full border border-slate-200 bg-gradient-to-r from-slate-50 to-gray-50 px-4 py-2 text-sm font-medium shadow-md backdrop-blur-sm transition-all duration-200 hover:shadow-lg dark:border-slate-700 dark:from-slate-900/50 dark:to-gray-900/50'
              >
                <CheckCircle className='h-4 w-4 text-green-600 dark:text-green-400' />
                <span className='text-slate-700 dark:text-slate-300'>
                  {feature}
                </span>
              </div>
            ))}
          </div>

          {/* Stats Grid */}
          <div className='mb-16 grid grid-cols-2 gap-6 md:grid-cols-4'>
            {stats.map((stat, index) => (
              <Card
                key={index}
                className='group relative border-slate-200 bg-card backdrop-blur-sm transition-all duration-300 hover:scale-105 hover:shadow-xl dark:border-slate-700'
              >
                <CardContent className='p-6 text-center'>
                  <div
                    className={`mx-auto mb-3 flex h-12 w-12 items-center justify-center rounded-xl ${stat.bgColor} border border-white/20 transition-transform group-hover:scale-110 dark:border-gray-800/20`}
                  >
                    <stat.icon className={`h-6 w-6 ${stat.iconColor}`} />
                  </div>
                  <div className='mb-2 text-2xl font-bold text-slate-900 dark:text-slate-100'>
                    {stat.value}
                  </div>
                  <div className='text-sm text-muted-foreground'>
                    {stat.label}
                  </div>
                </CardContent>
              </Card>
            ))}
          </div>

          {/* CTA Buttons */}
          <div className='flex flex-col gap-4 sm:flex-row sm:justify-center'>
            <Button
              size='lg'
              className='group relative overflow-hidden px-8 py-4 text-lg font-semibold shadow-2xl transition-all duration-300 hover:scale-105'
              asChild
            >
              <Link href='/templates'>
                <span className='relative z-10 flex items-center'>
                  <Sparkles className='mr-2 h-5 w-5' />
                  Browse Premium Templates
                  <ArrowRight className='ml-2 h-5 w-5 transition-transform group-hover:translate-x-1' />
                </span>
              </Link>
            </Button>

            <Button
              size='lg'
              variant='outline'
              className='px-8 py-4 text-lg font-semibold transition-all duration-300 hover:shadow-lg'
              asChild
            >
              <Link href='/pricing'>
                <span className='flex items-center'>
                  <Crown className='mr-2 h-5 w-5' />
                  View Pricing Plans
                </span>
              </Link>
            </Button>
          </div>

          {/* Trust indicators */}
          <div className='mt-12 text-center'>
            <div className='mb-4 flex justify-center gap-2'>
              {[...Array(5)].map((_, i) => (
                <Star
                  key={i}
                  className='h-5 w-5 fill-yellow-400 text-yellow-500 drop-shadow-sm'
                />
              ))}
            </div>
            <p className='text-lg font-medium text-muted-foreground'>
              🚀 Start with a free account • No credit card required • Cancel
              anytime
            </p>
            <p className='mt-2 text-sm text-muted-foreground'>
              Trusted by 10,000+ developers worldwide
            </p>
          </div>
        </div>
      </div>
    </section>
  )
}

export default AboutCtaSection
