import { <PERSON>rk<PERSON> } from "lucide-react"

import Heading from "@/components/global/heading"
import { Badge } from "@/components/ui/badge"
import { Card, CardContent } from "@/components/ui/card"
import { statsData } from "@/constants/about"

const AboutStorySection = () => {
  return (
    <section className='relative py-16 lg:py-24'>
      <div className='container mx-auto max-w-6xl px-4'>
        {/* Header */}
        <div className='mx-auto max-w-3xl text-center'>
          <Badge variant='outline' className='mb-6 gap-2 px-4 py-2'>
            <Sparkles className='h-3.5 w-3.5' />
            Our Story
          </Badge>

          <Heading className='mb-6'>
            Crafting Excellence in Developer Resources
          </Heading>

          <p className='mb-16 text-lg leading-relaxed text-muted-foreground'>
            DesignByte is a premium marketplace for developer resources that
            follow strict standards and best practices for modern web
            development. We believe that quality code starts with quality tools
            and templates. Our collection of templates, starter kits, themes,
            and design resources is meticulously crafted to ensure quality,
            maintainability, and exceptional developer experience.
          </p>
        </div>

        {/* Stats Grid */}
        <div className='grid gap-6 md:grid-cols-3'>
          {statsData.map((stat, index) => {
            const Icon = stat.icon
            return (
              <Card
                key={index}
                className='border-border/50 transition-all duration-200 hover:shadow-md'
              >
                <CardContent className='p-6'>
                  <div className='flex items-center gap-4'>
                    <div className='flex h-12 w-12 items-center justify-center rounded-lg bg-muted'>
                      <Icon className='h-6 w-6 text-foreground' />
                    </div>
                    <div className='flex-1'>
                      <div className='flex items-baseline gap-2'>
                        <span className='text-2xl font-bold text-foreground'>
                          {stat.value}
                        </span>
                        <Badge variant='secondary' className='text-xs'>
                          {stat.badge}
                        </Badge>
                      </div>
                      <p className='text-sm text-muted-foreground'>
                        {stat.label}
                      </p>
                    </div>
                  </div>
                </CardContent>
              </Card>
            )
          })}
        </div>
      </div>
    </section>
  )
}

export default AboutStorySection
