import { Target } from "lucide-react"

import { AboutMissionCard } from "@/components/cards"
import Heading from "@/components/global/heading"
import { missionData } from "@/constants/about"

const AboutMissionSection = () => {
  return (
    <section className='relative py-16 lg:py-24'>
      <div className='mx-auto max-w-6xl'>
        <div className='mb-16 text-center'>
          <div className='mb-6 inline-flex items-center gap-2'>
            <Target className='h-5 w-5 text-blue-600' />
            <span className='text-sm font-medium text-blue-600'>
              Mission & Vision
            </span>
          </div>
          <Heading className='mb-8 bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent dark:from-gray-100 dark:to-gray-400'>
            Our Mission & Vision
          </Heading>
          <p className='mx-auto max-w-3xl text-lg text-muted-foreground lg:text-xl'>
            At DesignByte, our mission is to elevate the standard of web
            development by providing developers with resources that adhere to
            strict quality standards. We believe that following best practices
            and modern development principles should be the norm, not the
            exception.
          </p>
        </div>

        <div className='grid gap-8 lg:grid-cols-3'>
          {missionData.map((mission, index) => (
            <AboutMissionCard
              key={index}
              icon={mission.icon}
              title={mission.title}
              description={mission.description}
              badgeText={mission.badgeText}
              badgeColor={mission.badgeColor}
            />
          ))}
        </div>
      </div>
    </section>
  )
}

export default AboutMissionSection
