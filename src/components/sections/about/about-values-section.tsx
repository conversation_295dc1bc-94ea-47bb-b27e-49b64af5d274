import { Lightbulb } from "lucide-react"

import { AboutValueCard } from "@/components/cards"
import Heading from "@/components/global/heading"
import { valuesData } from "@/constants/about"

const AboutValuesSection = () => {
  return (
    <section className='relative py-16 lg:py-24'>
      <div className='container relative mx-auto max-w-6xl px-4'>
        <div className='mb-16 text-center'>
          <div className='mb-6 inline-flex items-center gap-2'>
            <Lightbulb className='h-5 w-5 text-yellow-600' />
            <span className='text-sm font-medium text-yellow-600'>
              Core Values
            </span>
          </div>
          <Heading className='mb-8'>Values That Drive Us</Heading>
          <p className='mx-auto max-w-3xl text-lg text-muted-foreground'>
            Our core values shape every decision we make and every resource we
            create.
          </p>
        </div>

        <div className='grid gap-6 md:grid-cols-2 lg:grid-cols-4'>
          {valuesData.map((value, index) => (
            <AboutValueCard
              key={index}
              icon={value.icon}
              title={value.title}
              description={value.description}
              colorClass={value.colorClass}
            />
          ))}
        </div>
      </div>
    </section>
  )
}

export default AboutValuesSection
