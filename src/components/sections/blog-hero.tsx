import { Calendar, Clock, User } from "lucide-react"
import Image from "next/image"
import Link from "next/link"

import { Badge } from "@/components/ui/badge"
import { Button } from "@/components/ui/button"
import { Card, CardContent } from "@/components/ui/card"
import type { BlogPost } from "@/constants/blogs"

interface BlogHeroProps {
  featuredPost: BlogPost
}

export const BlogHero = ({ featuredPost }: BlogHeroProps) => {
  return (
    <Card className='relative overflow-hidden border-0 bg-gradient-to-br from-primary/5 to-secondary/5'>
      <div className='grid gap-8 lg:grid-cols-2'>
        {/* Content */}
        <CardContent className='flex flex-col justify-center p-8 lg:p-12'>
          <Badge
            variant='secondary'
            className='mb-4 w-fit bg-primary/10 text-primary'
          >
            Featured Article
          </Badge>

          <h1 className='mb-4 text-3xl font-bold leading-tight lg:text-4xl'>
            {featuredPost.title}
          </h1>

          <p className='mb-6 text-lg text-muted-foreground'>
            {featuredPost.description}
          </p>

          <div className='mb-6 flex flex-wrap items-center gap-4 text-sm text-muted-foreground'>
            <div className='flex items-center gap-1'>
              <User className='h-4 w-4' />
              <span>{featuredPost.author.name}</span>
            </div>
            <div className='flex items-center gap-1'>
              <Calendar className='h-4 w-4' />
              <span>{featuredPost.date}</span>
            </div>
            <div className='flex items-center gap-1'>
              <Clock className='h-4 w-4' />
              <span>{featuredPost.readTime}</span>
            </div>
          </div>

          <div className='mb-6 flex flex-wrap gap-2'>
            {featuredPost.tags.slice(0, 3).map((tag) => (
              <Badge key={tag} variant='outline'>
                {tag}
              </Badge>
            ))}
          </div>

          <Button asChild size='lg' className='w-fit'>
            <Link href={`/blogs/${featuredPost.slug}`}>Read Full Article</Link>
          </Button>
        </CardContent>

        {/* Image */}
        <div className='relative min-h-[300px] lg:min-h-[400px]'>
          <Image
            src={featuredPost.image}
            alt={featuredPost.title}
            fill
            className='object-cover'
          />
          <div className='absolute inset-0 bg-gradient-to-t from-background/20 to-transparent' />
        </div>
      </div>
    </Card>
  )
}
