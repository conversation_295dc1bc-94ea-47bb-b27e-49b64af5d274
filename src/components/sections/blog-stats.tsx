import { Book<PERSON><PERSON>, <PERSON>, Eye, Users } from "lucide-react"

import { Card, CardContent } from "@/components/ui/card"
import { BLOG_POSTS } from "@/constants"

export const BlogStats = () => {
  const totalArticles = BLOG_POSTS.length
  const totalViews = BLOG_POSTS.reduce(
    (sum, post) => sum + (post.views || 0),
    0,
  )
  const totalReadTime = BLOG_POSTS.reduce((sum, post) => {
    const time = parseInt(post.readTime.split(" ")[0])
    return sum + time
  }, 0)
  const uniqueAuthors = new Set(BLOG_POSTS.map((post) => post.author.name)).size

  const stats = [
    {
      label: "Articles",
      value: totalArticles,
      icon: BookOpen,
      color: "text-blue-500",
    },
    {
      label: "Total Views",
      value: totalViews.toLocaleString(),
      icon: Eye,
      color: "text-green-500",
    },
    {
      label: "Read Time",
      value: `${totalReadTime}min`,
      icon: Clock,
      color: "text-orange-500",
    },
    {
      label: "Authors",
      value: uniqueAuthors,
      icon: Users,
      color: "text-purple-500",
    },
  ]

  return (
    <div className='grid grid-cols-2 gap-4 lg:grid-cols-4'>
      {stats.map((stat) => {
        const Icon = stat.icon
        return (
          <Card key={stat.label} className='border-0 bg-muted/30'>
            <CardContent className='flex items-center gap-3 p-4'>
              <Icon className={`h-5 w-5 ${stat.color}`} />
              <div>
                <p className='text-lg font-semibold'>{stat.value}</p>
                <p className='text-xs text-muted-foreground'>{stat.label}</p>
              </div>
            </CardContent>
          </Card>
        )
      })}
    </div>
  )
}
