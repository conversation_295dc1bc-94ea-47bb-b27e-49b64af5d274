"use client"

import { AnimatePresence, motion } from "framer-motion"
import { CheckIcon } from "lucide-react"
import Link from "next/link"

import { <PERSON><PERSON>, <PERSON><PERSON><PERSON><PERSON><PERSON>, <PERSON><PERSON>List, TabsTrigger } from "@/components/ui/tabs"
import { PLANS } from "@/constants"
import { cn } from "@/functions"

import { Container, SectionWrapper } from "../layout"
import { Button } from "../ui/button"
import NumberTicker from "../ui/number-ticker"
import { SectionBadge } from "../ui/section-bade"

type Plan = "monthly" | "yearly"

const Pricing = () => {
  return (
    <SectionWrapper>
      <Container>
        <div className='mx-auto flex max-w-xl flex-col items-center text-center'>
          <SectionBadge title='Choose your plan' />
          <h2 className='mt-6 font-heading text-2xl font-medium !leading-snug md:text-4xl lg:text-5xl'>
            Simple and transparent pricing
          </h2>
          <p className='mt-6 text-center text-base text-accent-foreground/80 md:text-lg'>
            Choose the plan that suits your needs. No hidden fees, no surprises.
          </p>
        </div>
      </Container>
      <div className='relative mt-8 flex w-full flex-col items-center justify-center'>
        <div className='absolute right-2/3 top-1/2 -z-10 hidden h-96 w-96 -translate-y-1/2 translate-x-1/4 bg-primary/15 blur-[10rem] lg:block' />
        <div className='absolute left-2/3 top-1/2 -z-10 hidden h-96 w-96 -translate-x-1/4 -translate-y-1/2 bg-violet-500/15 blur-[10rem] lg:block' />
        <Container>
          <Tabs
            defaultValue='monthly'
            className='flex w-full flex-col items-center justify-center'
          >
            <TabsList>
              <TabsTrigger value='monthly'>Monthly</TabsTrigger>
              <TabsTrigger value='yearly'>Yearly</TabsTrigger>
            </TabsList>
            <TabsContent value='monthly'>
              <div className='mt-14 grid w-full grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3'>
                {PLANS.map((plan, index) => (
                  <Plan key={index} index={index} {...plan} plan='monthly' />
                ))}
              </div>
            </TabsContent>
            <TabsContent value='yearly'>
              <div className='mt-14 grid w-full grid-cols-1 gap-6 md:grid-cols-2 lg:grid-cols-3'>
                {PLANS.map((plan, index) => (
                  <Plan key={index} index={index} {...plan} plan='yearly' />
                ))}
              </div>
            </TabsContent>
          </Tabs>
        </Container>
      </div>
    </SectionWrapper>
  )
}

const Plan = ({
  id,
  title,
  desc,
  monthlyPrice,
  yearlyPrice,
  buttonText,
  features,
  index,
  plan,
}: {
  id: string
  title: string
  desc: string
  monthlyPrice: number
  yearlyPrice: number
  buttonText: string
  features: string[]
  index: number
  plan: Plan
}) => {
  const getDisplayedPrice = (
    plan: string,
    monthlyPrice: number,
    yearlyPrice: number,
  ) => {
    if (plan === "monthly") {
      return monthlyPrice === 0 ? 0 : monthlyPrice
    } else if (plan === "yearly") {
      const discountedPrice = Math.round((yearlyPrice * 0.8) / 12)
      return yearlyPrice === 0 ? 0 : discountedPrice
    }
    return 0
  }

  const displayedPrice = getDisplayedPrice(plan, monthlyPrice, yearlyPrice)

  return (
    <div
      key={index}
      className='relative flex w-full flex-col rounded-2xl saturate-150'
    >
      <div
        className={cn(
          "relative flex size-full flex-col rounded-2xl border p-3 [background-image:linear-gradient(345deg,rgba(255,255,255,0.01)_0%,rgba(255,255,255,0.03)_100%)]",
          id === "pro" ? "border-primary/80" : "border-border/60",
        )}
      >
        {id === "pro" && (
          <div className='absolute -top-3 left-1/2 inline-flex h-7 min-w-min max-w-fit -translate-x-1/2 select-none items-center whitespace-nowrap rounded-full bg-gradient-to-r from-primary to-violet-500 px-1'>
            <span className='flex-1 animate-background-shine bg-gradient-to-r from-foreground to-foreground/80 bg-[length:250%_100%] bg-clip-text px-2 text-sm font-medium text-transparent'>
              Most Popular
            </span>
          </div>
        )}
        <div className='flex w-full flex-col p-3'>
          <h2 className='text-xl font-medium'>{title}</h2>
          <p className='mt-2 break-words text-sm text-muted-foreground'>
            {desc}
          </p>
        </div>
        <hr
          className='h-px w-full shrink-0 border-none bg-border'
          role='separator'
        />
        <div className='relative flex h-full w-full flex-1 flex-col gap-4 break-words p-3 text-left align-top'>
          <div className='flex items-end gap-2'>
            <div className='flex w-40 items-end gap-1'>
              <span className='text-3xl font-bold md:text-4xl'>
                $
                {displayedPrice === 0 ? (
                  0
                ) : (
                  <NumberTicker value={displayedPrice} />
                )}
              </span>
              {/* In here 120 * 0.8 = 96 and /12 to get monthly price */}
              <span className='font-headin text-lg font-medium text-muted-foreground'>
                per {plan === "monthly" ? "month" : "month"}
              </span>
            </div>
            <AnimatePresence>
              {id !== "free" && plan === "yearly" && (
                <motion.span
                  initial={{ opacity: 0, scale: 0 }}
                  animate={{ opacity: 1, scale: 1 }}
                  exit={{ opacity: 0, scale: 0 }}
                  transition={{ duration: 0.2 }}
                  aria-hidden='true'
                  className='mb-1 rounded bg-primary px-2 py-0.5 text-xs font-medium text-foreground'
                >
                  -20%
                </motion.span>
              )}
            </AnimatePresence>
          </div>
          <ul className='flex flex-col gap-2'>
            {features.map((feature, index) => (
              <li key={index} className='flex items-center gap-2'>
                <CheckIcon
                  aria-hidden='true'
                  className='h-5 w-5 text-primary'
                />
                <p className='text-sm text-muted-foreground md:text-base'>
                  {feature}
                </p>
              </li>
            ))}
          </ul>
        </div>
        <div className='mt- flex h-auto w-full items-center p-3'>
          <Button
            asChild
            variant={id === "pro" ? "default" : "tertiary"}
            className='w-full shadow-none hover:translate-y-0 hover:scale-100'
          >
            <Link href={"/"}>{buttonText}</Link>
          </Button>
        </div>
      </div>
    </div>
  )
}

export default Pricing
