import { AboutFeatureCard } from "@/components/cards"
import Heading from "@/components/global/heading"
import { featuresData } from "@/constants/about"

const FeaturesSection = () => {
  return (
    <section className='py-16 lg:py-24'>
      <div className='mx-auto max-w-6xl'>
        <div className='mb-16 text-center'>
          <Heading className='mb-8 bg-gradient-to-r from-gray-900 to-gray-600 bg-clip-text text-transparent dark:from-gray-100 dark:to-gray-400'>
            What Sets Us Apart
          </Heading>
          <p className='mx-auto max-w-3xl text-lg text-muted-foreground'>
            We don&apos;t just create templates; we craft comprehensive
            development experiences that follow industry best practices and
            modern standards.
          </p>
        </div>

        <div className='grid gap-8 lg:grid-cols-2'>
          <div className='space-y-8'>
            {featuresData.slice(0, 3).map((feature, index) => (
              <AboutFeatureCard
                key={index}
                icon={feature.icon}
                title={feature.title}
                description={feature.description}
                badges={feature.badges}
                gradientFrom={feature.gradientFrom}
                gradientTo={feature.gradientTo}
              />
            ))}
          </div>

          <div className='space-y-8'>
            {featuresData.slice(3).map((feature, index) => (
              <AboutFeatureCard
                key={index + 3}
                icon={feature.icon}
                title={feature.title}
                description={feature.description}
                badges={feature.badges}
                gradientFrom={feature.gradientFrom}
                gradientTo={feature.gradientTo}
              />
            ))}
          </div>
        </div>
      </div>
    </section>
  )
}

export default FeaturesSection
