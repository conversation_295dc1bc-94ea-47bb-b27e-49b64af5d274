import { ArrowRightIcon } from "lucide-react"
import Image from "next/image"
import Link from "next/link"

import { Container } from "../layout"
import { Spotlight } from "../ui"
import { BlurText } from "../ui/blur-text"
import { Button } from "../ui/button"

const Hero = () => {
  return (
    <div className='relative z-40 mx-auto flex w-full flex-col items-center px-4 py-24 text-center md:pt-40'>
      <Image
        src='/images/hero.svg'
        alt=''
        width={1024}
        height={1024}
        className='absolute inset-x-0 -top-16 z-10 w-full min-w-full'
      />

      {/* gradient background effect */}
      <div className='absolute left-[calc(55%-379px/2)] top-[-266px] -z-10 hidden h-[620px] w-[379px] rounded-full bg-primary/60 opacity-50 blur-[10rem] lg:flex' />
      <div className='absolute left-[calc(50%-433px/2)] top-[-60px] -z-10 hidden h-[525px] w-[433px] rounded-full bg-primaryLight/40 opacity-50 blur-[15rem] lg:flex' />

      {/* spotlight effect */}
      <Spotlight
        className='-top-40 left-0 md:-top-20 md:left-60'
        fill='rgb(81, 224, 207)'
      />

      {/* background image */}
      {/* <div className='absolute inset-0 h-screen'>
        <Image
          src={
            // "https://agency-assets.theportfolyo.com/backgrounds/background-3.webp"
            "https://gallery.theportfolio.in/background-images/1750953293834-section-bg-13.webp"
            // "https://utfs.io/f/qPlpyBmwd8UNDbQGePgfcU0BPHiJwnq2OTMo8C6r5aRkyYWb"
          }
          alt='background'
          layout='fill'
          objectFit='cover'
          objectPosition='center'
          quality={100}
          className='z-[-1] opacity-30'
        />
      </div> */}

      <Container delay={0.0}>
        <div className='mx-auto flex w-max cursor-pointer select-none items-center gap-2.5 rounded-full border border-foreground/10 py-1 pl-2 pr-1 backdrop-blur-lg hover:border-foreground/15'>
          <div className='relative flex h-3.5 w-3.5 items-center justify-center rounded-full bg-primary/40'>
            <div className='flex h-2.5 w-2.5 animate-ping items-center justify-center rounded-full bg-primary/60'>
              <div className='flex h-2.5 w-2.5 animate-ping items-center justify-center rounded-full bg-primary/60' />
            </div>
            <div className='absolute left-1/2 top-1/2 flex h-1.5 w-1.5 -translate-x-1/2 -translate-y-1/2 items-center justify-center rounded-full bg-primary' />
          </div>
          <span className='animate-text-gradient inline-flex animate-background-shine items-center justify-center gap-2 bg-gradient-to-r from-[#b2a8fd] via-[#51E0CF] to-[#c7d2fe] bg-[200%_auto] bg-clip-text text-sm text-transparent'>
            Build Better Projects
            <span className='flex items-center justify-center rounded-full bg-gradient-to-b from-foreground/20 to-foreground/10 px-1.5 py-0.5 text-xs text-secondary-foreground'>
              What&apos;s new
              <ArrowRightIcon className='ml-1 h-3.5 w-3.5 text-foreground/50' />
            </span>
          </span>
        </div>
      </Container>
      <BlurText
        word={"Elevate Your Development \n With Quality Resources"}
        // word={"Build, Learn, and Innovate \n with DesignByte"}
        // word={"Your Ultimate Web \n Development Resource"}
        // word={"Build Faster, Smarter, and \n Better with DesignBytee"}
        className='racking-[-0.0125em] mt-6 bg-gradient-to-br from-foreground to-foreground/60 bg-clip-text py-2 font-heading text-3xl font-medium text-transparent sm:text-5xl md:py-0 lg:text-6xl lg:!leading-snug xl:text-7xl'
      />
      <Container delay={0.1}>
        <p className='mx-auto mt-4 max-w-2xl text-sm text-accent-foreground/60 sm:text-base lg:text-lg'>
          DesignByte is your premium marketplace for developer resources that
          follow strict standards and best practices for modern web development.
          <span className='hidden sm:inline'>
            {" "}
            Every template, starter kit, and design resource is meticulously
            crafted to ensure quality, maintainability, and exceptional
            developer experience from start to finish.
          </span>
        </p>
      </Container>
      <Container delay={0.2}>
        <div className='mt-8 flex items-center justify-center md:gap-x-6'>
          <Button asChild size='lg'>
            <Link href='/templates'>Explore Templates</Link>
          </Button>
          <Button
            asChild
            size='lg'
            variant='outline'
            className='hidden md:flex'
          >
            <Link href='/starter-kits'>Browse Starter Kits</Link>
          </Button>
        </div>
      </Container>
      <Container delay={0.3}>
        <div className='relative mx-auto mt-12 max-w-5xl rounded-xl border border-neutral-200/50 border-neutral-700 bg-neutral-800/50 p-2 backdrop-blur-lg md:p-4 lg:rounded-[32px]'>
          {/* <div className='gradient absolute inset-0 left-1/2 top-1/4 -z-10 h-1/4 w-3/4 -translate-x-1/2 -translate-y-1/2 blur-[10rem]' /> */}
          <div className='absolute inset-0 left-1/2 top-1/4 -z-10 h-1/4 w-3/4 -translate-x-1/2 -translate-y-1/2 bg-primary opacity-50 blur-[10rem]' />

          <div className='rounded-lg border border-neutral-700 bg-black p-2 lg:rounded-[24px]'>
            <Image
              src='/images/dashboard.png'
              alt='dashboard'
              width={1920}
              height={1080}
              className='rounded-lg lg:rounded-[20px]'
            />
          </div>
        </div>
      </Container>
    </div>
  )
}

export default Hero
