"use client"

import { create } from "zustand"
import { persist } from "zustand/middleware"

import { User } from "@/types/user"

interface AuthState {
  user: User | null
  isLoading: boolean
  isAuthenticated: boolean
  token: string | null
}

interface AuthActions {
  login: (email: string, password: string) => Promise<{ success: boolean; message?: string }>
  register: (email: string, username: string, password: string) => Promise<{ success: boolean; message?: string }>
  logout: () => Promise<void>
  verifyEmail: (token: string) => Promise<{ success: boolean; message?: string }>
  forgotPassword: (email: string) => Promise<{ success: boolean; message?: string }>
  resetPassword: (token: string, password: string) => Promise<{ success: boolean; message?: string }>
  googleLogin: () => void
  refreshUser: () => Promise<void>
  setUser: (user: User | null) => void
  setToken: (token: string | null) => void
  setLoading: (loading: boolean) => void
  checkAuth: () => Promise<void>
}

type AuthStore = AuthState & AuthActions

export const useAuth = create<AuthStore>()(
  persist(
    (set, get) => ({
      // State
      user: null,
      isLoading: true,
      token: null,
      get isAuthenticated() {
        return !!get().user
      },

      // Actions
      setUser: (user) => set({ user }),
      setToken: (token) => {
        if (token) {
          localStorage.setItem("token", token)
        } else {
          localStorage.removeItem("token")
        }
        set({ token })
      },
      setLoading: (isLoading) => set({ isLoading }),

      checkAuth: async () => {
        const { setUser, setLoading, setToken } = get()
        try {
          const token = localStorage.getItem("token")
          if (!token) {
            setLoading(false)
            return
          }

          const response = await fetch("/api/auth/me", {
            headers: {
              Authorization: `Bearer ${token}`,
            },
          })

          if (response.ok) {
            const data = await response.json()
            setUser(data.user)
            setToken(token)
          } else {
            setToken(null)
            setUser(null)
          }
        } catch (error) {
          console.error("Auth check failed:", error)
          setToken(null)
          setUser(null)
        } finally {
          setLoading(false)
        }
      },

      login: async (email: string, password: string) => {
        const { setUser, setToken } = get()
        try {
          const response = await fetch("/api/auth/login", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({ email, password }),
          })

          const data = await response.json()

          if (response.ok) {
            setToken(data.token)
            setUser(data.user)
            return { success: true }
          } else {
            return { success: false, message: data.message }
          }
        } catch (error) {
          console.error("Login failed:", error)
          return { success: false, message: "Login failed. Please try again." }
        }
      },

      register: async (email: string, username: string, password: string) => {
        try {
          const response = await fetch("/api/auth/register", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({ email, username, password }),
          })

          const data = await response.json()

          if (response.ok) {
            return { success: true, message: "Registration successful. Please check your email to verify your account." }
          } else {
            return { success: false, message: data.message }
          }
        } catch (error) {
          console.error("Registration failed:", error)
          return { success: false, message: "Registration failed. Please try again." }
        }
      },

      logout: async () => {
        const { setUser, setToken } = get()
        try {
          const token = localStorage.getItem("token")
          if (token) {
            await fetch("/api/auth/logout", {
              method: "POST",
              headers: {
                Authorization: `Bearer ${token}`,
              },
            })
          }
        } catch (error) {
          console.error("Logout failed:", error)
        } finally {
          setToken(null)
          setUser(null)
        }
      },

      verifyEmail: async (token: string) => {
        try {
          const response = await fetch("/api/auth/verify-email", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({ token }),
          })

          const data = await response.json()

          if (response.ok) {
            return { success: true, message: "Email verified successfully!" }
          } else {
            return { success: false, message: data.message }
          }
        } catch (error) {
          console.error("Email verification failed:", error)
          return {
            success: false,
            message: "Email verification failed. Please try again.",
          }
        }
      },

      forgotPassword: async (email: string) => {
        try {
          const response = await fetch("/api/auth/forgot-password", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({ email }),
          })

          const data = await response.json()

          if (response.ok) {
            return {
              success: true,
              message: "Password reset email sent. Please check your inbox.",
            }
          } else {
            return { success: false, message: data.message }
          }
        } catch (error) {
          console.error("Forgot password failed:", error)
          return {
            success: false,
            message: "Failed to send reset email. Please try again.",
          }
        }
      },

      resetPassword: async (token: string, password: string) => {
        try {
          const response = await fetch("/api/auth/reset-password", {
            method: "POST",
            headers: {
              "Content-Type": "application/json",
            },
            body: JSON.stringify({ token, password }),
          })

          const data = await response.json()

          if (response.ok) {
            return { success: true, message: "Password reset successfully!" }
          } else {
            return { success: false, message: data.message }
          }
        } catch (error) {
          console.error("Password reset failed:", error)
          return {
            success: false,
            message: "Password reset failed. Please try again.",
          }
        }
      },

      googleLogin: () => {
        window.location.href = "/api/auth/google"
      },

      refreshUser: async () => {
        const { checkAuth } = get()
        await checkAuth()
      },
    }),
    {
      name: "auth-storage",
      partialize: (state) => ({
        token: state.token,
        user: state.user,
      }),
    }
  )
)

// Initialize auth check on app start
if (typeof window !== "undefined") {
  useAuth.getState().checkAuth()
}
